{
*****************************************************************************
*  Name    : SALTEC.BAS                                                     *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2010 Scanner Alarmas                             *
*          : All Rights Reserved                                            *
*  Date    : 09/03/2010                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

Module SALTEC

    Include "SUART.bas"
    Include "IO.bas"

    //Constantes para la comunicacion del teclado
    Public Const
        //Tipo de comando
        tecEstadoCentral=$00,                           //Indica el estado de la central. Valor: activando, activada o desactivada
        tecBateriaBaja=$01,                             //Indica la deteccion de un sensor con bateria baja. Valor: zona bat baja
        tecCentralDisparadaI=$02,                       //Indica que la central se disparo por zona inalambrica. Valor: zona disparada
        tecCentralDisparadaC=$03,                       //Indica que la central se disparo por zona cableada. Valor: zona disparada
        tecCentralDisparadaA=$04,                       //Indica que la central se disparo por antidesarme. Valor: nada
        tecCentralDemoradaI=$05,                        //Indica que se activo la demora inalambrica. Valor: zona disparada
        tecCentralDemoradaC=$06,                        //Indica que se activo la demora cableada. Valor: zona disparada
        tecZonasCableadasActuadas=$07,                  //Indica que que existen zonas cableadas abiertas. Valor: zonas abiertas
        tecZonasCanceladasI=$08,                        //Indica zonas canceladas inalambricas. Valor: zonas canceladas
        tecZonasCanceladasC=$09,                        //Indica zonas canceladas cableadas. Valor: zonas canceladas
        tecZonasAutoexcluidas=$0A,                      //Indica zonas autoexcluidas. Valor: zonas autoexcluidas
        tecZonaInalambricaTransmitiendo=$0B,            //Indica zona inalambrica transmitiendo. Valor: zona transmitiendo (0=ninguna)
    
        //Valor del comando para tecTipoEstadoCentral
        tecCentralDesactivada=0,
        tecCentralActivando=1,
        tecCentralActivada=2
    
    //Envia informacio al teclado
    //Privada que conoce el protocolo del teclado
    Public Sub EnviarInfoTeclado(pTipo As Byte, pValor As Byte)
        //Inicializacion del modulo
        UART.SetTX(IO.SalidaTeclado)
        UART.SetBaudrate(sbr4800)
        UART.SetMode(umTrue)
        intcon.6=0
        UART.Write($ff, $ff, pTipo, pValor)
        UART.Write($ff, $ff, pTipo, pValor)
        intcon.6=1
    End Sub
    
    //Envia el estado de central activada
    Public Sub CentralActivada()
        EnviarInfoTeclado(tecEstadoCentral, tecCentralActivada)
    End Sub
    
    //Envia el estado de central activando
    Public Sub CentralActivando()
        EnviarInfoTeclado(tecEstadoCentral, tecCentralActivando)
        EnviarInfoTeclado(tecEstadoCentral, tecCentralActivando)
    End Sub
    
    //Envia el estado de central desactivada
    Public Sub CentralDesactivada()
        EnviarInfoTeclado(tecEstadoCentral, tecCentralDesactivada)
    End Sub
    
    //Envia bateria baja en sensor
    Public Sub BateriaBaja(pZona As Byte)
        EnviarInfoTeclado(tecBateriaBaja, pZona)
    End Sub
    
    //Envia zona inalambrica disparada
    Public Sub ZonaInalambricaDisparada(pZona As Byte)
        EnviarInfoTeclado(tecCentralDisparadaI, pZona)
    End Sub
    
    //Envia zona cableada disparada
    Public Sub ZonaCableadaDisparada(pZona As Byte)
        EnviarInfoTeclado(tecCentralDisparadaC, pZona)
    End Sub
    
    //Envia antidesarme disparado
    Public Sub AntidesarmeDisparado()
        EnviarInfoTeclado(tecCentralDisparadaA, 0)
    End Sub
    
    //Envia zona demorada inalambrica
    Public Sub ZonaInalambricaDemorada(pZona As Byte)
        EnviarInfoTeclado(tecCentralDemoradaI, pZona)
    End Sub
    
    //Envia zona demorada cableada
    Public Sub ZonaCableadaDemorada(pZona As Byte)
        EnviarInfoTeclado(tecCentralDemoradaC, pZona)
    End Sub
    
    //Envia zonas cabladas actuadas
    Public Sub ZonasCableadasActuadas(pZonas As Byte)
        EnviarInfoTeclado(tecZonasCableadasActuadas, pZonas)
    End Sub

    //Envia zona canceladas inalambricas
    Public Sub ZonasCanceladasInalambricas(pZonas As Byte)
        EnviarInfoTeclado(tecZonasCanceladasI, pZonas)
    End Sub
    
'    //Envia zona canceladas cableadas
'    Public Sub ZonasCableadasCanceladas(pZonas As Byte)
'        EnviarInfoTeclado(tecZonasCanceladasC, pZonas)
'    End Sub
    
    //Envia zonas autoexcluidas
    Public Sub ZonasAutoExcluidas(pZonas As Byte)
        EnviarInfoTeclado(tecZonasAutoexcluidas, pZonas)
    End Sub
    
    //Envia zona inalambrica transmtiendo
    Public Sub ZonaInalambricaTransmitiendo(pZona As Byte)
        EnviarInfoTeclado(tecZonaInalambricaTransmitiendo, pZona)
    End Sub
