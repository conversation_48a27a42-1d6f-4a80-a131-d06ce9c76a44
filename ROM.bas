Module ROM

Include "EEPROM.bas"
'Include "6P20.bas"
Include "H6P20.bas"
Include "CENTRAL.bas"
'Include "TECLADO.bas"
Include "MEMORYACCESSORIGINAL.bas"
Include "convert.bas"
Include "WALA.bas"


    //Declaracion de variables privadas
    Dim
        FlagGIEH As Bit,
        FlagGIEL As Bit

    //Declaracion de variables publicas
    'Public Dim
        'FlagActivadoAMasa As Boolean,
        'FlagRoboCondicionado As Boolean,
        'FlagRoboAMasa As Boolean,
        'FlagAsaltoAMasa As Boolean,
        'FlagEmergenciaAMasa As Boolean,
        'FlagIncendioAMasa As Boolean,
        'FlagSalida1PorNivel As Boolean,
        'FlagSalida1EstadoNormalDesactivado As Boolean,
        'TiempoMascaraRobo As Byte,
        'TiempoMascaraAsalto As Byte,
        'TiempoMascaraEmergencia As Byte,
        'TiempoMascaraIncendio As Byte,
        'TiempoPulsoSalida1 As Byte,
        'TiempoMascaraActivado As Byte
        
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
    EEPROM=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)      
      
    EEPROM(@dirZonasCableadasNormalAbiertas)=(%00000000)
    EEPROM(@dirZonasCableadasCancelables)=(%00000000)
    EEPROM(@dirZonasCableadasDemoradas)=(%00000000)
    EEPROM(@dirZonasCableadas24Horas)=(%00000000)
    EEPROM(@dirZonasInalambricasCancelables)=(%00000000)
    EEPROM(@dirZonasInalambricasDemoradas)=(%00000000)
    EEPROM(@dirZonasInalambricas24Horas)=(%00000000)
    EEPROM(@dirTiempoSirenas)=(120)'(240)
    EEPROM(@dirTiempoEntrada)=(20)'(20)
    EEPROM(@dirTiempoSalida)=(10)'(30)
    EEPROM(@dirTiempoTemporizadoDesconexion)=(43200)
    EEPROM(@dirFlagsCentral1)=(%01000011) //Bits: Funcion
                                        //   0: Sirena interior habilitada para beeps
                                        //   1: Sirena exterior habilitada para beeps
                                        //   2: Asalto cableado habilitado (zona 6)
                                        //   3: Emergencia cableada habilitada (zona 7)
                                        //   4: Incendio cableado habilitado (zona 8)
                                        //   5: Antidesarme habilitado
                                        //   6: Asalto por boton 2 al desactivar
                                        //   7: Central Bloqueada por instalador
    
    EEPROM(@dirFlagsCentral2)=(%00000000) //Bits: Funcion
                                        //   0: Central Bloqueada de fabrica
                                        //   1: Entrada wala habilitada
                                        //   2: 
                                        //   3: 
                                        //   4: 
                                        //   5: 
                                        //   6: 
                                        //   7: 
    
    EEPROM(@dirCodigoProgramacion)=(10000)
    EEPROM(@dirAlarmasRoboZ1C)=(%00000000)
    EEPROM(@dirAlarmasRoboZ2C)=(%00000000)
    EEPROM(@dirAlarmasRoboZ3C)=(%00000000)
    EEPROM(@dirAlarmasRoboZ4C)=(%00000000)
    EEPROM(@dirAlarmasRoboZ5C)=(%00000000)
    EEPROM(@dirAlarmasRoboZ6C)=(%00000000)
    EEPROM(@dirAlarmasRoboZ7C)=(%00000000)
    EEPROM(@dirAlarmasRoboZ8C)=(%00000000)
    EEPROM(@dirAlarmasRoboZ1I)=(%00000000)
    EEPROM(@dirAlarmasRoboZ2I)=(%00000000)
    EEPROM(@dirAlarmasRoboZ3I)=(%00000000)
    EEPROM(@dirAlarmasRoboZ4I)=(%00000000)
    EEPROM(@dirAlarmasRoboZ5I)=(%00000000)
    EEPROM(@dirAlarmasRoboZ6I)=(%00000000)
    EEPROM(@dirAlarmasRoboZ7I)=(%00000000)
    EEPROM(@dirAlarmasRoboZ8I)=(%00000000)
    EEPROM(@dirAlarmasAsaltoC)=(%00000000)
    EEPROM(@dirAlarmasAsaltoI)=(%00000000)
    EEPROM(@dirAlarmasEmergenciaC)=(%00000000)
    EEPROM(@dirAlarmasEmergenciaI)=(%00000000)
    EEPROM(@dirAlarmasIncendioC)=(%00000000)
    EEPROM(@dirAlarmasIncendioI)=(%00000000)
    EEPROM(@dirAlarmasActivado)=(%00000000)
    EEPROM(@dirAlarmasDesactivado)=(%00000000)
    EEPROM(@dirAlarmasActivar)=(%00000000)
    EEPROM(@dirAlarmasDesactivar)=(%00000000)
    EEPROM(@dirAlarmasDesconexionAlimentacionInmediata)=(%00000000)
    EEPROM(@dirAlarmasReconexionAlimentacionInmediata)=(%00000000)
    EEPROM(@dirAlarmasDesconexionAlimentacionTemporizada)=(%00000000)
    EEPROM(@dirAlarmasReconexionAlimentacionTemporizada)=(%00000000)
    EEPROM(@dirAlarmasAntidesarme)=(%00000000)
    EEPROM(@dirTelefonosEscucha)=(%00000000)
    EEPROM(@dirSalidaTransistor1Activar)=(%00000000)
    EEPROM(@dirSalidaTransistor1Desactivar)=(%00000000)
    EEPROM(@dirSalidaTransistor2Activar)=(%00000000)
    EEPROM(@dirSalidaTransistor2Desactivar)=(%00000000)
    EEPROM(@dirBateriaBaja)=(%00000000)
    EEPROM(@dirDisparosGuardados)=(0,0,0,0,0,0,0,0)
    EEPROM(@dirMaximoErroresRonda)=(3)
    EEPROM(@dirFlagsGPRS)=(%00000010)   //Bits: Funcion
                                        //   0: GPRS Habilitado
                                        //   1: GPRS por nombre de dominio
                                        //   2: 
                                        //   3: 
                                        //   4: 
                                        //   5: 
                                        //   6: 
                                        //   7: 
    EEPROM(@dirFlagsSalidas)=(%00001111)//Bits: Funcion
                                        //   0: Salida1PorNivel
                                        //   1: Salida1EstadoNormalDesactivado
                                        //   2: Salida2PorNivel
                                        //   3: Salida2EstadoNormalDesactivado
                                        //   4: 
                                        //   5: 
                                        //   6: 
                                        //   7: 
    EEPROM(@dirTiempoPulsoSalida1)=(10)
    EEPROM(@dirTiempoPulsoSalida2)=(10)
    EEPROM(@dirUnidadTiempoPulsoSalida1)=(UnidadSeg)
    EEPROM(@dirUnidadTiempoPulsoSalida2)=(UnidadSeg)
    EEPROM(@dirTiempoTest)=(900)
    EEPROM(@dirCuenta)=(1,2,3,4)
    EEPROM(@dirTipoInicioCentral)=(TipoInicioUltimoEstado)
    EEPROM(@dirUltimoEstadoCentral)=(EstadoCentralDesactivada)
    EEPROM(@dirTelefono1)=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)
    EEPROM(@dirTelefono2)=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)
    EEPROM(@dirTelefono3)=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)
    EEPROM(@dirTelefono4)=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)
    EEPROM(@dirTelefono5)=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)
    EEPROM(@dirTelefono6)=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)
    EEPROM(@dirTelefono7)=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)
    EEPROM(@dirTelefono8)=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)
    EEPROM(@dirIMEI)=(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)
    
    Public Const
        'dirTelefono1                        = $7640,
        'dirTelefono1                        = $f440,
        'dirTelefono1                        = $f5c0,
        'dirTelefono2                        = dirTelefono1 + $10,
        'dirTelefono3                        = dirTelefono2 + $10,
        'dirTelefono4                        = dirTelefono3 + $10,
        'dirTelefono5                        = dirTelefono4 + $10,
        'dirTelefono6                        = dirTelefono5 + $10,
        'dirTelefono7                        = dirTelefono6 + $10,
        'dirTelefono8                        = dirTelefono7 + $10,
        dirCuerpoMensajeRoboZ1C             = $f700, 'dirTelefono8 + $10,
        dirCuerpoMensajeRoboZ2C             = dirCuerpoMensajeRoboZ1C + $40,
        dirCuerpoMensajeRoboZ3C             = dirCuerpoMensajeRoboZ2C + $40,
        dirCuerpoMensajeRoboZ4C             = dirCuerpoMensajeRoboZ3C + $40,
        dirCuerpoMensajeRoboZ5C             = dirCuerpoMensajeRoboZ4C + $40,
        dirCuerpoMensajeRoboZ6C             = dirCuerpoMensajeRoboZ5C + $40,
        dirCuerpoMensajeRoboZ7C             = dirCuerpoMensajeRoboZ6C + $40,
        dirCuerpoMensajeRoboZ8C             = dirCuerpoMensajeRoboZ7C + $40,
        dirCuerpoMensajeRoboZ1I             = dirCuerpoMensajeRoboZ8C + $40,
        dirCuerpoMensajeRoboZ2I             = dirCuerpoMensajeRoboZ1I + $40,
        dirCuerpoMensajeRoboZ3I             = dirCuerpoMensajeRoboZ2I + $40,
        dirCuerpoMensajeRoboZ4I             = dirCuerpoMensajeRoboZ3I + $40,
        dirCuerpoMensajeRoboZ5I             = dirCuerpoMensajeRoboZ4I + $40,
        dirCuerpoMensajeRoboZ6I             = dirCuerpoMensajeRoboZ5I + $40,
        dirCuerpoMensajeRoboZ7I             = dirCuerpoMensajeRoboZ6I + $40,
        dirCuerpoMensajeRoboZ8I             = dirCuerpoMensajeRoboZ7I + $40,
        dirCuerpoMensajeAntidesarme         = dirCuerpoMensajeRoboZ8I + $40,
        dirCuerpoMensajeAsaltoC             = dirCuerpoMensajeAntidesarme + $40,
        dirCuerpoMensajeAsaltoI             = dirCuerpoMensajeAsaltoC + $40,
        dirCuerpoMensajeEmergenciaC         = dirCuerpoMensajeAsaltoI + $40,
        dirCuerpoMensajeEmergenciaI         = dirCuerpoMensajeEmergenciaC + $40,
        dirCuerpoMensajeIncendioC           = dirCuerpoMensajeEmergenciaI + $40,
        dirCuerpoMensajeIncendioI           = dirCuerpoMensajeIncendioC + $40,
        dirCuerpoMensajeActivado            = dirCuerpoMensajeIncendioI + $40,
        dirCuerpoMensajeDesactivado         = dirCuerpoMensajeActivado + $40,
'        dirCuerpoMensajeDesconexion         = dirCuerpoMensajeDesactivado + $40,
'        dirCuerpoMensajeReconexion          = dirCuerpoMensajeDesconexion + $40,        
        dirCuerpoMensajeTextoUsuario        = dirCuerpoMensajeDesactivado + $40,
        dirCuerpoMensajeGrupoRemoto1        = dirCuerpoMensajeTextoUsuario + $40,
        dirCuerpoMensajeGrupoRemoto2        = dirCuerpoMensajeGrupoRemoto1 + $40,
        dirCuerpoMensajeGrupoRemoto3        = dirCuerpoMensajeGrupoRemoto2 + $40,
        dirCuerpoMensajeGrupoRemoto4        = dirCuerpoMensajeGrupoRemoto3 + $40,
        dirCuerpoMensajeGrupoRemoto5        = dirCuerpoMensajeGrupoRemoto4 + $40,
        dirCuerpoMensajeGrupoRemoto6        = dirCuerpoMensajeGrupoRemoto5 + $40,
        dirCuerpoMensajeGrupoRemoto7        = dirCuerpoMensajeGrupoRemoto6 + $40,
        dirCuerpoMensajeGrupoRemoto8        = dirCuerpoMensajeGrupoRemoto7 + $40,
        dirAPN                              = dirCuerpoMensajeGrupoRemoto8 + $40,
        dirServidor                         = dirAPN + $40,
    
        Codigo_Dump = 1,
        Codigo_Telefono = 2,
        Codigo_Texto = 3,
        Codigo_CuerpoMensaje = 4,
        Codigo_Seteos = 5,
        Codigo_Bandera = 6,
        Codigo_RSSI = 7,
        Codigo_Debug = 8,
        //Codigo_Salidas = 9,
        //No se deben usar los codigo 10 y 13, para que nunca sea confundidos con los indicadores de ping y sync
        //Codigos para mensajes de texto generados por la central
        Codigo_Robo_Z1C = 30,
        Codigo_Robo_Z2C = 31,
        Codigo_Robo_Z3C = 32,
        Codigo_Robo_Z4C = 33,
        Codigo_Robo_Z5C = 34,
        Codigo_Robo_Z6C = 35,
        Codigo_Robo_Z7C = 36,
        Codigo_Robo_Z8C = 37,
        Codigo_Robo_Z1I = 38,
        Codigo_Robo_Z2I = 39,
        Codigo_Robo_Z3I = 40,
        Codigo_Robo_Z4I = 41,
        Codigo_Robo_Z5I = 42,
        Codigo_Robo_Z6I = 43,
        Codigo_Robo_Z7I = 44,
        Codigo_Robo_Z8I = 45,
        Codigo_Antidesarme = 46,
        Codigo_AsaltoC = 47,
        Codigo_AsaltoI = 48,
        Codigo_EmergenciaC = 49,
        Codigo_EmergenciaI = 50,
        Codigo_IncendioC = 51,
        Codigo_IncendioI = 52,
        Codigo_Activado = 53,
        Codigo_Desactivado = 54,
        Codigo_Desconexion_Alimentacion_Inmediata = 55,
        Codigo_Reconexion_Alimentacion_Inmediata = 56,
        Codigo_Desconexion_Alimentacion_Temporizada = 57,
        Codigo_Reconexion_Alimentacion_Temporizada = 58,
        Codigo_Respuesta_Activar = 59,
        Codigo_Respuesta_Desactivar = 60,
        Codigo_Activar = 61,
        Codigo_Desactivar = 62,
        Codigo_Texto_Usuario = 63,
        Codigo_Respuesta_Ya_Activada = 64,
        Codigo_Respuesta_Ya_Desactivada = 65,
        Codigo_Grupo_Remotos_1 = 66,
        Codigo_Grupo_Remotos_2 = 67,
        Codigo_Grupo_Remotos_3 = 68,
        Codigo_Grupo_Remotos_4 = 69,
        Codigo_Grupo_Remotos_5 = 70,
        Codigo_Grupo_Remotos_6 = 71,
        Codigo_Grupo_Remotos_7 = 72,
        Codigo_Grupo_Remotos_8 = 73,
        
        Codigo_Zonas_Cableadas_Normal_Abiertas = 74,
        Codigo_Zonas_Cableadas_Cancelables = 75,
        Codigo_Zonas_Cableadas_Demoradas = 76,
        Codigo_Zonas_Cableadas_24Hs = 77,
        Codigo_Zonas_Inalambricas_Cancelables = 78,
        Codigo_Zonas_Inalambricas_Demoradas = 79,
        Codigo_Zonas_Inalambricas_24Hs = 80,
        Codigo_Tiempo_Sirenas = 81,
        Codigo_Tiempo_Entrada = 82,
        Codigo_Tiempo_Salida = 83,
        Codigo_Tiempo_Temporizado_Desconexion = 84,
        Codigo_Flags_Central = 85,
        Codigo_Maximo_Errores_Ronda = 86,
        Codigo_Registro_Disparos = 87,

        Codigo_GPRS = 88,
        Codigo_APN = 89,
        Codigo_Servidor = 90,
        Codigo_Reporte_Periodico = 91,
        Codigo_Cuenta = 92,
        Codigo_IMEI = 93,
        Codigo_Flags_GPRS = 94,
        Codigo_Version = 95,
        
        Codigo_Transistor1_Activar = 96,
        Codigo_Transistor2_Activar = 97,
        Codigo_Transistor1_Desactivar = 98,
        Codigo_Transistor2_Desactivar = 99,
        Codigo_Escucha = 100,
        Codigo_Flags_Salidas =101,
        Codigo_Tiempo_Salida1 =102,
        Codigo_Unidades_Salida1 =103,
        Codigo_Tiempo_Salida2 =104,
        Codigo_Unidades_Salida2 =105,
        Codigo_Tipo_Inicio =106,
        
        Codigo_Bateria_Baja =107,

        Codigo_Memoria_Inalambricos =108
        
        Public Const 
            CharCR As Char = 13,
            CharLF As Char = 10
            
            
    Public Sub GuardarIMEI(imei As String)
    FlagGIEH=INTCON.7
        INTCON.7=0
        FlagGIEL=INTCON.6
        INTCON.6=0
        EE.Write(dirIMEI, imei)
        INTCON.7=FlagGIEH
        INTCON.6=FlagGIEL
        'WALA.HighByte=EE.ReadByte($3fa)
    End Sub
    
    Public Function LeerIMEI() As String(16)
        EE.Read(dirIMEI, result)
    End Function
            
    Public Sub GuardarTelefono(pMemoria As Byte, pTelefono As String)
    
        Dim
            direccion As Word
       
        direccion=0     
        
        Select pMemoria
            Case 0
                direccion=dirTelefono1
            Case 1
                direccion=dirTelefono2
            Case 2
                direccion=dirTelefono3
            Case 3
                direccion=dirTelefono4
            Case 4
                direccion=dirTelefono5
            Case 5
                direccion=dirTelefono6
            Case 6
                direccion=dirTelefono7
            Case 7
                direccion=dirTelefono8
        EndSelect
        
        pTelefono(15)=0
		FlagGIEH=INTCON.7
		INTCON.7=0
		FlagGIEL=INTCON.6
		INTCON.6=0
        EE.Write(direccion, pTelefono)
		INTCON.7=FlagGIEH
		INTCON.6=FlagGIEL
        
    End Sub
    
    Public Function CargarDesdeRom(direccion As Word) As String(64)
        Dim contador As Byte
        
        For contador=0 To 63
            FlagGIEH=INTCON.7
            INTCON.7=0
            FlagGIEL=INTCON.6
            INTCON.6=0
            result(contador)=MemoryAccessOriginal.ReadByte(direccion+contador)
            INTCON.7=FlagGIEH
            INTCON.6=FlagGIEL
        Next
        result(63)=0
    End Function

    Public Sub GuardarString(pDireccion As Word, pCuerpoMensaje As String)
        GrabarStringMemoria(pDireccion, pCuerpoMensaje)
    End Sub
    
{
    Public Sub GuardarCuerpoMensajeRoboZ1C(pCuerpoMensaje As String)
        GrabarStringMemoria(dirCuerpoMensajeRoboZ1C, pCuerpoMensaje)
    End Sub
    
    Public Sub GuardarCuerpoMensajeRoboZ2C(pCuerpoMensaje As String)
        GrabarStringMemoria(dirCuerpoMensajeRoboZ2C, pCuerpoMensaje)
    End Sub
    
    Public Sub GuardarCuerpoMensajeAsalto(pCuerpoMensaje As String)
        GrabarStringMemoria(dirCuerpoMensajeAsalto, pCuerpoMensaje)
    End Sub
    
    Public Sub GuardarCuerpoMensajeEmergencia(pCuerpoMensaje As String)
        GrabarStringMemoria(dirCuerpoMensajeEmergencia, pCuerpoMensaje)
    End Sub
    
    Public Sub GuardarCuerpoMensajeIncendio(pCuerpoMensaje As String)
        GrabarStringMemoria(dirCuerpoMensajeIncendio, pCuerpoMensaje)
    End Sub
    
    Public Sub GuardarCuerpoMensajeActivado(pCuerpoMensaje As String)
        GrabarStringMemoria(dirCuerpoMensajeActivado, pCuerpoMensaje)
    End Sub
    
    Public Sub GuardarCuerpoMensajeDesactivado(pCuerpoMensaje As String)
        GrabarStringMemoria(dirCuerpoMensajeDesactivado, pCuerpoMensaje)
    End Sub

    Public Sub GuardarMensajeActivar(pCuerpoMensaje As String)
        GrabarStringMemoria(dirCuerpoMensajeActivar, pCuerpoMensaje)
    End Sub
    
    Public Sub GuardarMensajeDesactivar(pCuerpoMensaje As String)
        GrabarStringMemoria(dirCuerpoMensajeDesactivar, pCuerpoMensaje)
    End Sub

    Public Sub GuardarMensajeActivarSalida1(pCuerpoMensaje As String)
        GrabarStringMemoria(dirCuerpoMensajeActivarSalida1, pCuerpoMensaje)
    End Sub
    
    Public Sub GuardarMensajeDesactivarSalida1(pCuerpoMensaje As String)
        GrabarStringMemoria(dirCuerpoMensajeDesactivarSalida1, pCuerpoMensaje)
    End Sub

    Public Sub GuardarIMEI(pCuerpoMensaje As String)
        GrabarStringMemoria(dirIMEI, pCuerpoMensaje)
    End Sub

    Public Sub GuardarTelefono1(pCuerpoMensaje As String)
        GrabarStringMemoria(dirTelefono1, pCuerpoMensaje)
    End Sub

    Public Sub GuardarTelefono2(pCuerpoMensaje As String)
        GrabarStringMemoria(dirTelefono2, pCuerpoMensaje)
    End Sub

    Public Sub GuardarTelefono3(pCuerpoMensaje As String)
        GrabarStringMemoria(dirTelefono3, pCuerpoMensaje)
    End Sub

    Public Sub GuardarTelefono4(pCuerpoMensaje As String)
        GrabarStringMemoria(dirTelefono4, pCuerpoMensaje)
    End Sub

    Public Sub GuardarTelefono5(pCuerpoMensaje As String)
        GrabarStringMemoria(dirTelefono5, pCuerpoMensaje)
    End Sub

    Public Sub GuardarTelefono6(pCuerpoMensaje As String)
        GrabarStringMemoria(dirTelefono6, pCuerpoMensaje)
    End Sub

    Public Sub GuardarTelefono7(pCuerpoMensaje As String)
        GrabarStringMemoria(dirTelefono7, pCuerpoMensaje)
    End Sub

    Public Sub GuardarTelefono8(pCuerpoMensaje As String)
        GrabarStringMemoria(dirTelefono8, pCuerpoMensaje)
    End Sub
}
    
    Public Function LeerTelefono(numero As Byte) As String(16)

        Dim direccion As Word
            
        Select numero
            Case 0
                direccion=dirTelefono1
            Case 1
                direccion=dirTelefono2
            Case 2
                direccion=dirTelefono3
            Case 3
                direccion=dirTelefono4
            Case 4
                direccion=dirTelefono5
            Case 5
                direccion=dirTelefono6
            Case 6
                direccion=dirTelefono7
            Case 7
                direccion=dirTelefono8
        EndSelect
        EE.Read(direccion, result)
    End Function

    Public Function TextoGrupoRemoto(pOrigen As Byte) As String(64)
        Select pOrigen
            Case 9
                result=CargarDesdeRom(dirCuerpoMensajeGrupoRemoto1)
            Case 10
                result=CargarDesdeRom(dirCuerpoMensajeGrupoRemoto2)
            Case 11
                result=CargarDesdeRom(dirCuerpoMensajeGrupoRemoto3)
            Case 12
                result=CargarDesdeRom(dirCuerpoMensajeGrupoRemoto4)
            Case 13
                result=CargarDesdeRom(dirCuerpoMensajeGrupoRemoto5)
            Case 14
                result=CargarDesdeRom(dirCuerpoMensajeGrupoRemoto6)
            Case 15
                result=CargarDesdeRom(dirCuerpoMensajeGrupoRemoto7)
            Case 16
                result=CargarDesdeRom(dirCuerpoMensajeGrupoRemoto8)
        EndSelect
    End Function
    
    Public Function LeerMensaje(pCodigo As Byte) As String(64)
    
        Dim direccion As Word
    
        Select pCodigo
            Case Codigo_Robo_Z1C
                direccion=dirCuerpoMensajeRoboZ1C
            Case Codigo_Robo_Z2C
                direccion=dirCuerpoMensajeRoboZ2C
            Case Codigo_Robo_Z3C
                direccion=dirCuerpoMensajeRoboZ3C
            Case Codigo_Robo_Z4C
                direccion=dirCuerpoMensajeRoboZ4C
            Case Codigo_Robo_Z5C
                direccion=dirCuerpoMensajeRoboZ5C
            Case Codigo_Robo_Z6C
                direccion=dirCuerpoMensajeRoboZ6C
            Case Codigo_Robo_Z7C
                direccion=dirCuerpoMensajeRoboZ7C
            Case Codigo_Robo_Z8C
                direccion=dirCuerpoMensajeRoboZ8C
            Case Codigo_Robo_Z1I
                direccion=dirCuerpoMensajeRoboZ1I
            Case Codigo_Robo_Z2I
                direccion=dirCuerpoMensajeRoboZ2I
            Case Codigo_Robo_Z3I
                direccion=dirCuerpoMensajeRoboZ3I
            Case Codigo_Robo_Z4I
                direccion=dirCuerpoMensajeRoboZ4I
            Case Codigo_Robo_Z5I
                direccion=dirCuerpoMensajeRoboZ5I
            Case Codigo_Robo_Z6I
                direccion=dirCuerpoMensajeRoboZ6I
            Case Codigo_Robo_Z7I
                direccion=dirCuerpoMensajeRoboZ7I
            Case Codigo_Robo_Z8I
                direccion=dirCuerpoMensajeRoboZ8I
            Case Codigo_Antidesarme
                direccion=dirCuerpoMensajeAntidesarme
            Case Codigo_AsaltoC
                direccion=dirCuerpoMensajeAsaltoC
            Case Codigo_AsaltoI
                direccion=dirCuerpoMensajeAsaltoI
            Case Codigo_EmergenciaC
                direccion=dirCuerpoMensajeEmergenciaC
            Case Codigo_EmergenciaI
                direccion=dirCuerpoMensajeEmergenciaI
            Case Codigo_IncendioC
                direccion=dirCuerpoMensajeIncendioC
            Case Codigo_IncendioI
                direccion=dirCuerpoMensajeIncendioI
            Case Codigo_Activado
                direccion=dirCuerpoMensajeActivado
            Case Codigo_Desactivado
                direccion=dirCuerpoMensajeDesactivado
'            Case Codigo_Desconexion_Alimentacion_Inmediata, Codigo_Desconexion_Alimentacion_Temporizada
'                direccion=dirCuerpoMensajeDesconexion
'            Case Codigo_Reconexion_Alimentacion_Inmediata, Codigo_Reconexion_Alimentacion_Temporizada
'                direccion=dirCuerpoMensajeReconexion
'            Case Codigo_Activar
'                direccion=dirCuerpoMensajeActivar
'            Case Codigo_Desactivar
'                direccion=dirCuerpoMensajeDesactivar
            Case Codigo_Texto_Usuario
                direccion=dirCuerpoMensajeTextoUsuario
            Case Codigo_Grupo_Remotos_1
                direccion=dirCuerpoMensajeGrupoRemoto1
            Case Codigo_Grupo_Remotos_2
                direccion=dirCuerpoMensajeGrupoRemoto2
            Case Codigo_Grupo_Remotos_3
                direccion=dirCuerpoMensajeGrupoRemoto3
            Case Codigo_Grupo_Remotos_4
                direccion=dirCuerpoMensajeGrupoRemoto4
            Case Codigo_Grupo_Remotos_5
                direccion=dirCuerpoMensajeGrupoRemoto5
            Case Codigo_Grupo_Remotos_6
                direccion=dirCuerpoMensajeGrupoRemoto6
            Case Codigo_Grupo_Remotos_7
                direccion=dirCuerpoMensajeGrupoRemoto7
            Case Codigo_Grupo_Remotos_8
                direccion=dirCuerpoMensajeGrupoRemoto8
'            Case Codigo_Transistor1_Activar
'                direccion=dirMensajeActivarSalida1
'            Case Codigo_Transistor2_Activar
'                direccion=dirMensajeActivarSalida2
'            Case Codigo_Transistor1_Desactivar
'                direccion=dirMensajeDesactivarSalida1
'            Case Codigo_Transistor2_Desactivar
'                direccion=dirMensajeDesactivarSalida2
        EndSelect
        result=CargarDesdeRom(direccion)
    End Function
    
'    //Permite guardar un tipo de elemento inalambrico en la memoria (remotos, sensores de incendio, emergencia o robo de una zona)
'    Public Function GuardarInalambrico(ZonaSeleccionada As Byte) As Boolean
'        Dim Direccion As Word
'       ' Dim InfoAdicional As Byte
'    
'        If HT6P20.CantidadInalambricos<204 Then
'            direccion=HT6P20.CantidadInalambricos*4
'
'            FlagGIEH=INTCON.7
'            INTCON.7=0
'            FlagGIEL=INTCON.6
'            INTCON.6=0
'            EE.Write(direccion + 1, HT6P20._AddressHigh)
'            EE.Write(direccion + 2, HT6P20._AddressMid)
'            EE.Write(direccion + 3, HT6P20._AddressLow)
'            EE.Write(direccion + 4, ZonaSeleccionada)
'            Inc(HT6P20.CantidadInalambricos)
'            EE.Write( 0, HT6P20.CantidadInalambricos)
'            INTCON.7=FlagGIEH
'            INTCON.6=FlagGIEL
'            result=true
'        Else
'            result=false
'        EndIf
'    End Function

    //pPosicion = posicion en memoria del elemento inalambrico (indice 1)
    Public Function ZonaPorPosicion(pPosicion As Byte) As Byte
        Dim direccion As Word
        
        direccion=(pPosicion-1)*4
        direccion=direccion+3
        
        result=EE.ReadByte(direccion)
    End Function

    //Permite guardar un tipo de elemento inalambrico en la memoria (remotos, sensores de incendio, emergencia o robo de una zona)
    Public Function GuardarInalambrico(ZonaSeleccionada As Byte) As Boolean
        Dim direccion As Word,
            contador As Byte

        contador=0
        result=false
 
        While contador<CantidadMaximaInalambricos
            direccion=contador*4
            
            //Si la direccion esta libre...
            If EE.ReadByte(direccion+3)=0 Then
                FlagGIEH=INTCON.7
                INTCON.7=0
                FlagGIEL=INTCON.6
                INTCON.6=0
                EE.Write(direccion, HT6P20._AddressHigh)
                EE.Write(direccion + 1, HT6P20._AddressMid)
                EE.Write(direccion + 2, HT6P20._AddressLow)
                EE.Write(direccion + 3, ZonaSeleccionada)
                INTCON.7=FlagGIEH
                INTCON.6=FlagGIEL
                result=true
                HT6P20.PosicionEnMemoria=contador+1
                Exit
            EndIf
            Inc(contador)
        Wend
 
    End Function
    
'    Sub CorrerLista(ContadorElementos As Byte)
'        'ContadorElementos tiene el numero de posicion en memoria donde se encuentra almacenado el remoto a borrar
'        'ElementosLista contiene la cantidad de elementos almacenados en memoria
'        Dim ContadorDesplazamiento As Byte
'        Dim temporal As Byte
'
'        FlagGIEH=INTCON.7
'        INTCON.7=0
'        FlagGIEL=INTCON.6
'        INTCON.6=0
'        
'        For ContadorDesplazamiento = (ContadorElementos * 4) + 1 To (CantidadInalambricos - 1) * 4
'            temporal=EE.ReadByte(ContadorDesplazamiento + 4)
'            EE.WriteByte(ContadorDesplazamiento, temporal)
'        Next
'        Dec(CantidadInalambricos)
'        EE.WriteByte(0, CantidadInalambricos)
'        INTCON.7=FlagGIEH
'        INTCON.6=FlagGIEL
'    End Sub
    
    //Permite borrar un tipo de elemento inalambrico de la memoria (remotos, sensores de incendio, emergencia o robo de una zona)
    Public Sub BorrarInalambrico(ZonaSeleccionada As Byte)
        Dim 
            direccion As Word,
            ContadorElementos As Byte,
            DatoLeido As Byte
    
        ContadorElementos=0    
        While ContadorElementos < HT6P20.CantidadMaximaInalambricos
            direccion = ContadorElementos * 4
            direccion = direccion + 3
            DatoLeido=EE.ReadByte(direccion)
            
            If DatoLeido = ZonaSeleccionada Then
        		FlagGIEH=INTCON.7
        		INTCON.7=0
        		FlagGIEL=INTCON.6
        		INTCON.6=0
                EE.WriteByte(direccion, 0)
        		INTCON.7=FlagGIEH
        		INTCON.6=FlagGIEL
            Else
                Inc(ContadorElementos)
            EndIf
        Wend
    End Sub
    
    Public Sub LeerSeteos()
        Dim Flags As Byte
        
        'HT6P20.CantidadInalambricos=EE.ReadByte(0)
        'CENTRAL.
        CENTRAL.ZonasNormalAbiertas=EE.ReadByte(dirZonasCableadasNormalAbiertas)
        CENTRAL.ZonasCableadasCancelables=EE.ReadByte(dirZonasCableadasCancelables)
        CENTRAL.ZonasCableadasCancelablesBak=ZonasCableadasCancelables
        CENTRAL.ZonasCableadasDemoradas=EE.ReadByte(dirZonasCableadasDemoradas)
        CENTRAL.ZonasCableadas24Horas=EE.ReadByte(dirZonasCableadas24Horas)
        CENTRAL.ZonasInalambricasCancelables=EE.ReadByte(dirZonasInalambricasCancelables)
        CENTRAL.ZonasInalambricasCancelablesBak=ZonasInalambricasCancelables
        CENTRAL.ZonasInalambricasDemoradas=EE.ReadByte(dirZonasInalambricasDemoradas)
        CENTRAL.ZonasInalambricas24Horas=EE.ReadByte(dirZonasInalambricas24Horas)
        CENTRAL.TiempoSirenas=EE.ReadByte(dirTiempoSirenas)
        CENTRAL.TiempoEntrada=EE.ReadByte(dirTiempoEntrada)
        CENTRAL.TiempoSalida=EE.ReadByte(dirTiempoSalida)
        CENTRAL.TiempoTemporizadoDesconexion=EE.ReadWord(dirTiempoTemporizadoDesconexion)
        Flags=EE.ReadByte(dirFlagsCentral1)
        CENTRAL.BeepsPorSirenaInterior=(Flags.0=1)
        CENTRAL.BeepsPorSirenaExterior=(Flags.1=1)
        CENTRAL.AsaltoCableadoHabilitado=(Flags.2=1)
        CENTRAL.EmergenciaCableadaHabilitada=(Flags.3=1)
        CENTRAL.IncendioCableadoHabilitado=(Flags.4=1)
        CENTRAL.AntidesarmeHabilitado=(Flags.5=1)
        CENTRAL.AsaltoPorBoton2Habilitado=(Flags.6=1)
        CENTRAL.CentralBloqueadaInstalador=(Flags.7=1)
        CENTRAL.CodigoProgramacion=EE.ReadWord(dirCodigoProgramacion)
        Flags=EE.ReadByte(dirFlagsCentral2)
        CENTRAL.CentralBloqueadaFabrica=(Flags.0=1)
        CENTRAL.flagEntradaWalaHabilitada=(Flags.1=1)
        'EE.Read(dirCodigoProgramacion+2, TECLADO.CodigoProgramacion(1))
        'EE.Read(dirCodigoProgramacion+1, TECLADO.CodigoProgramacion(2))
        'EE.Read(dirCodigoProgramacion+0, TECLADO.CodigoProgramacion(3))
        'CENTRAL.MetodoMarcado=EE.ReadByte(dirMetodoMarcado)
        'CENTRAL.CantidadMensajes=EE.ReadByte(dirCantidadMensajes)
        'CENTRAL.CantidadRondasDisparo=EE.ReadByte(dirCantidadRondasDisparo)
        CENTRAL.MaximoErroresRonda=EE.ReadByte(dirMaximoErroresRonda)
        Flags=EE.ReadByte(dirFlagsGPRS)
        CENTRAL.flagGPRSHabilitado=(Flags.0=1)
        CENTRAL.flagGPRSPorDominio=(Flags.1=1)
        Flags=EE.ReadByte(dirFlagsSalidas)
        CENTRAL.FlagSalida1PorNivel=(Flags.0=1)
        CENTRAL.FlagSalida1EstadoNormalDesactivado=(Flags.1=1)
        CENTRAL.FlagSalida2PorNivel=(Flags.2=1)
        CENTRAL.FlagSalida2EstadoNormalDesactivado=(Flags.3=1)
        TiempoPulsoSalida1=EE.ReadByte(dirTiempoPulsoSalida1)
        TiempoPulsoSalida2=EE.ReadByte(dirTiempoPulsoSalida2)
        UnidadesTiempoPulsoSalida1=EE.ReadByte(dirUnidadTiempoPulsoSalida1)
        UnidadesTiempoPulsoSalida2=EE.ReadByte(dirUnidadTiempoPulsoSalida2)
        CENTRAL.TiempoTest=EE.ReadWord(dirTiempoTest)
        CENTRAL.Account(0)=EE.ReadByte(dirCuenta)
        CENTRAL.Account(1)=EE.ReadByte(dirCuenta+1)
        CENTRAL.Account(2)=EE.ReadByte(dirCuenta+2)
        CENTRAL.Account(3)=EE.ReadByte(dirCuenta+3)
        CENTRAL.TipoInicioCentral=EE.ReadByte(dirTipoInicioCentral)
        CENTRAL.UltimoEstadoCentralEEPROM=EE.ReadByte(dirUltimoEstadoCentral) 
'        CENTRAL.TestFabricaConcluido=EE.ReadByte(dirTestFabricaConcluido)
    End Sub
    
    Public Sub GuardarUltimoEstado()
        EE.WriteByte(dirUltimoEstadoCentral, CENTRAL.UltimoEstadoCentralEEPROM)
    End Sub

    Public Sub GuardarSeteos()
        Dim 
            Flags As Byte
        
        FlagGIEH=INTCON.7
        INTCON.7=0
        FlagGIEL=INTCON.6
        INTCON.6=0
        'EE.WriteByte(0, HT6P20.CantidadInalambricos)
        EE.WriteByte(dirZonasCableadasNormalAbiertas, CENTRAL.ZonasNormalAbiertas)
        EE.WriteByte(dirZonasCableadasCancelables, CENTRAL.ZonasCableadasCancelables)
        EE.WriteByte(dirZonasCableadasDemoradas, CENTRAL.ZonasCableadasDemoradas)
        EE.WriteByte(dirZonasCableadas24Horas, CENTRAL.ZonasCableadas24Horas)
        EE.WriteByte(dirZonasInalambricasCancelables, CENTRAL.ZonasInalambricasCancelables)
        EE.WriteByte(dirZonasInalambricasDemoradas, CENTRAL.ZonasInalambricasDemoradas)
        EE.WriteByte(dirZonasInalambricas24Horas, CENTRAL.ZonasInalambricas24Horas)
        EE.WriteByte(dirTiempoSirenas, CENTRAL.TiempoSirenas)
        EE.WriteByte(dirTiempoEntrada, CENTRAL.TiempoEntrada)
        EE.WriteByte(dirTiempoSalida, CENTRAL.TiempoSalida)
        EE.WriteWord(dirTiempoTemporizadoDesconexion, CENTRAL.TiempoTemporizadoDesconexion)
        
        Flags=0
        If CENTRAL.BeepsPorSirenaInterior Then
            Flags.0=1
        EndIf
        If CENTRAL.BeepsPorSirenaExterior Then
            Flags.1=1
        EndIf
        If CENTRAL.AsaltoCableadoHabilitado Then
            Flags.2=1
        EndIf
        If CENTRAL.EmergenciaCableadaHabilitada Then
            Flags.3=1
        EndIf
        If CENTRAL.IncendioCableadoHabilitado Then
            Flags.4=1
        EndIf
        If CENTRAL.AntidesarmeHabilitado Then
            Flags.5=1
        EndIf
        If CENTRAL.AsaltoPorBoton2Habilitado Then
            Flags.6=1
        EndIf
        If CENTRAL.CentralBloqueadaInstalador Then
            Flags.7=1
        EndIf

        EE.WriteByte(dirFlagsCentral1, Flags)
        
        Flags=0
        If CENTRAL.CentralBloqueadaFabrica Then
            Flags.0=1
        EndIf
        If CENTRAL.flagEntradaWalaHabilitada Then
            Flags.1=1
        EndIf
        
        EE.WriteByte(dirFlagsCentral2, Flags)
        
        Flags=0
        If CENTRAL.flagGPRSHabilitado Then
            Flags.0=1
        EndIf
        If CENTRAL.flagGPRSPorDominio Then
            Flags.1=1
        EndIf

        EE.WriteByte(dirFlagsGPRS, Flags)
        
        Flags=0
        If CENTRAL.FlagSalida1PorNivel Then
            Flags.0=1
        EndIf
        If CENTRAL.FlagSalida1EstadoNormalDesactivado Then
            Flags.1=1
        EndIf
        If CENTRAL.FlagSalida2PorNivel Then
            Flags.2=1
        EndIf
        If CENTRAL.FlagSalida2EstadoNormalDesactivado Then
            Flags.3=1
        EndIf

        EE.WriteByte(dirFlagsSalidas, Flags)
        
        EE.WriteByte(dirTiempoPulsoSalida1, TiempoPulsoSalida1)
        EE.WriteByte(dirTiempoPulsoSalida2, TiempoPulsoSalida2)
        EE.WriteByte(dirUnidadTiempoPulsoSalida1, UnidadesTiempoPulsoSalida1)
        EE.WriteByte(dirUnidadTiempoPulsoSalida2, UnidadesTiempoPulsoSalida2)
        
        EE.WriteWord(dirCodigoProgramacion, CENTRAL.CodigoProgramacion)
        
        EE.WriteByte(dirMaximoErroresRonda, CENTRAL.MaximoErroresRonda)
        'EE.WriteByte(dirCodigoProgramacion+2, TECLADO.CodigoProgramacion(1))
        'EE.WriteByte(dirCodigoProgramacion+1, TECLADO.CodigoProgramacion(2))
        'EE.WriteByte(dirCodigoProgramacion+0, TECLADO.CodigoProgramacion(3))
        
        'EE.WriteByte(dirMetodoMarcado, CENTRAL.MetodoMarcado)
        
        'EE.WriteByte(dirCantidadMensajes, CENTRAL.CantidadMensajes)
        'EE.WriteByte(dirCantidadRondasDisparo, CENTRAL.CantidadRondasDisparo)
        
        EE.WriteWord(dirTiempoTest, CENTRAL.TiempoTest)
        
        EE.WriteByte(dirCuenta, CENTRAL.Account(0))
        EE.WriteByte(dirCuenta+1, CENTRAL.Account(1))
        EE.WriteByte(dirCuenta+2, CENTRAL.Account(2))
        EE.WriteByte(dirCuenta+3, CENTRAL.Account(3))
        
        EE.WriteByte(dirTipoInicioCentral, CENTRAL.TipoInicioCentral)
        'EE.WriteByte(dirUltimoEstadoCentral, CENTRAL.UltimoEstadoCentralEEPROM)
        GuardarUltimoEstado()

        INTCON.7=FlagGIEH
        INTCON.6=FlagGIEL
    End Sub
    
{
    //devuelve las alarmas para un determinado telefono
    Public Sub LeerAlarmas(pNumero As Byte, ByRef pRobo As Boolean, ByRef pAsalto As Boolean, ByRef pEmergencia As Boolean, ByRef pIncendio As Boolean)
        Dim Mascara As Byte
        Dim Leido As Byte
        
        Mascara=%********
        Mascara=Mascara<<(pNumero-1)
        leido=EE.ReadByte(dirAlarmasRobo)
        pRobo=(leido And Mascara) <> 0
        leido=EE.ReadByte(dirAlarmasAsalto)
        pAsalto=(leido And Mascara) <> 0
        leido=EE.ReadByte(dirAlarmasEmergencia)
        pEmergencia=(leido And Mascara) <> 0
        leido=EE.ReadByte(dirAlarmasIncendio)
        pIncendio=(leido And Mascara) <> 0
    End Sub
}
    
    //Devuelve las alarmas de un determinado tipo
    Public Function LeerAlarmas(pTipo As Byte) As Byte
        Dim direccion As Word
        
        Select pTipo
            Case Codigo_Robo_Z1C
                direccion=dirAlarmasRoboZ1C
            Case Codigo_Robo_Z2C
                direccion=dirAlarmasRoboZ2C
            Case Codigo_Robo_Z3C
                direccion=dirAlarmasRoboZ3C
            Case Codigo_Robo_Z4C
                direccion=dirAlarmasRoboZ4C
            Case Codigo_Robo_Z5C
                direccion=dirAlarmasRoboZ5C
            Case Codigo_Robo_Z6C
                direccion=dirAlarmasRoboZ6C
            Case Codigo_Robo_Z7C
                direccion=dirAlarmasRoboZ7C
            Case Codigo_Robo_Z8C
                direccion=dirAlarmasRoboZ8C
            Case Codigo_Robo_Z1I
                direccion=dirAlarmasRoboZ1I
            Case Codigo_Robo_Z2I
                direccion=dirAlarmasRoboZ2I
            Case Codigo_Robo_Z3I
                direccion=dirAlarmasRoboZ3I
            Case Codigo_Robo_Z4I
                direccion=dirAlarmasRoboZ4I
            Case Codigo_Robo_Z5I
                direccion=dirAlarmasRoboZ5I
            Case Codigo_Robo_Z6I
                direccion=dirAlarmasRoboZ6I
            Case Codigo_Robo_Z7I
                direccion=dirAlarmasRoboZ7I
            Case Codigo_Robo_Z8I
                direccion=dirAlarmasRoboZ8I
            Case Codigo_AsaltoC
                direccion=dirAlarmasAsaltoC
            Case Codigo_AsaltoI
                direccion=dirAlarmasAsaltoI
            Case Codigo_EmergenciaC
                direccion=dirAlarmasEmergenciaC
            Case Codigo_EmergenciaI
                direccion=dirAlarmasEmergenciaI
            Case Codigo_IncendioC
                direccion=dirAlarmasIncendioC
            Case Codigo_IncendioI
                direccion=dirAlarmasIncendioI
            Case Codigo_Activado
                direccion=dirAlarmasActivado
            Case Codigo_Desactivado
                direccion=dirAlarmasDesactivado
            Case Codigo_Activar
                direccion=dirAlarmasActivar
            Case Codigo_Desactivar
                direccion=dirAlarmasDesactivar
            Case Codigo_Desconexion_Alimentacion_Inmediata
                direccion=dirAlarmasDesconexionAlimentacionInmediata
            Case Codigo_Reconexion_Alimentacion_Inmediata
                direccion=dirAlarmasReconexionAlimentacionInmediata
            Case Codigo_Desconexion_Alimentacion_Temporizada
                direccion=dirAlarmasDesconexionAlimentacionTemporizada
            Case Codigo_Reconexion_Alimentacion_Temporizada
                direccion=dirAlarmasReconexionAlimentacionTemporizada
            Case Codigo_Transistor1_Activar
                direccion=dirSalidaTransistor1Activar
            Case Codigo_Transistor2_Activar
                direccion=dirSalidaTransistor2Activar
            Case Codigo_Transistor1_Desactivar
                direccion=dirSalidaTransistor1Desactivar
            Case Codigo_Transistor2_Desactivar
                direccion=dirSalidaTransistor2Desactivar
            Case Codigo_Escucha
                direccion=dirTelefonosEscucha
            Case Codigo_Antidesarme
                direccion=dirAlarmasAntidesarme
            Case Codigo_Bateria_Baja
                direccion=dirBateriaBaja
        End Select
        
        result=EE.ReadByte(direccion)

    End Function

    
    //devuelve las alarmas para un determinado telefono
{
    Public Sub EscribirAlarmas(pNumero As Byte, pRobo As Boolean, pAsalto As Boolean, pEmergencia As Boolean, pIncendio As Boolean)
        Dim Leido As Byte
        
        FlagGIEH=INTCON.7
        INTCON.7=0
        FlagGIEL=INTCON.6
        INTCON.6=0

        Leido=EE.ReadByte(dirAlarmasRoboZ1C)
        Leido.booleans(pNumero-1)=pRobo
        EE.WriteByte(dirAlarmasRobo, Leido)
        
        Leido=EE.ReadByte(dirAlarmasAsalto)
        Leido.booleans(pNumero-1)=pAsalto
        EE.WriteByte(dirAlarmasAsalto, Leido)
        
        Leido=EE.ReadByte(dirAlarmasEmergencia)
        Leido.booleans(pNumero-1)=pemergencia
        EE.WriteByte(dirAlarmasEmergencia, Leido)
        
        Leido=EE.ReadByte(dirAlarmasIncendio)
        Leido.booleans(pNumero-1)=pIncendio
        EE.WriteByte(dirAlarmasIncendio, Leido)
        
        INTCON.7=FlagGIEH
        INTCON.6=FlagGIEL
   End Sub
}
   
   //Guarda las alarmas de un tipo determinado
   Public Sub EscribirAlarmas(pTipo As Byte, pHabilitados As Byte)
        Dim direccion As Word
        
        FlagGIEH=INTCON.7
        INTCON.7=0
        FlagGIEL=INTCON.6
        INTCON.6=0
        Select pTipo
            Case Codigo_Robo_Z1C
                direccion=dirAlarmasRoboZ1C
            Case Codigo_Robo_Z2C
                direccion=dirAlarmasRoboZ2C
            Case Codigo_Robo_Z3C
                direccion=dirAlarmasRoboZ3C
            Case Codigo_Robo_Z4C
                direccion=dirAlarmasRoboZ4C
            Case Codigo_Robo_Z5C
                direccion=dirAlarmasRoboZ5C
            Case Codigo_Robo_Z6C
                direccion=dirAlarmasRoboZ6C
            Case Codigo_Robo_Z7C
                direccion=dirAlarmasRoboZ7C
            Case Codigo_Robo_Z8C
                direccion=dirAlarmasRoboZ8C
            Case Codigo_Robo_Z1I
                direccion=dirAlarmasRoboZ1I
            Case Codigo_Robo_Z2I
                direccion=dirAlarmasRoboZ2I
            Case Codigo_Robo_Z3I
                direccion=dirAlarmasRoboZ3I
            Case Codigo_Robo_Z4I
                direccion=dirAlarmasRoboZ4I
            Case Codigo_Robo_Z5I
                direccion=dirAlarmasRoboZ5I
            Case Codigo_Robo_Z6I
                direccion=dirAlarmasRoboZ6I
            Case Codigo_Robo_Z7I
                direccion=dirAlarmasRoboZ7I
            Case Codigo_Robo_Z8I
                direccion=dirAlarmasRoboZ8I
            Case Codigo_AsaltoC
                direccion=dirAlarmasAsaltoC
            Case Codigo_AsaltoI
                direccion=dirAlarmasAsaltoI
            Case Codigo_EmergenciaC
                direccion=dirAlarmasEmergenciaC
            Case Codigo_EmergenciaI
                direccion=dirAlarmasEmergenciaI
            Case Codigo_IncendioC
                direccion=dirAlarmasIncendioC
            Case Codigo_IncendioI
                direccion=dirAlarmasIncendioI
            Case Codigo_Activado
                direccion=dirAlarmasActivado
            Case Codigo_Desactivado
                direccion=dirAlarmasDesactivado
            Case Codigo_Activar
                direccion=dirAlarmasActivar
            Case Codigo_Desactivar
                direccion=dirAlarmasDesactivar
            Case Codigo_Desconexion_Alimentacion_Inmediata
                direccion=dirAlarmasDesconexionAlimentacionInmediata
            Case Codigo_Reconexion_Alimentacion_Inmediata
                direccion=dirAlarmasReconexionAlimentacionInmediata
            Case Codigo_Desconexion_Alimentacion_Temporizada
                direccion=dirAlarmasDesconexionAlimentacionTemporizada
            Case Codigo_Reconexion_Alimentacion_Temporizada
                direccion=dirAlarmasReconexionAlimentacionTemporizada
            Case Codigo_Transistor1_Activar
                direccion=dirSalidaTransistor1Activar
            Case Codigo_Transistor2_Activar
                direccion=dirSalidaTransistor2Activar
            Case Codigo_Transistor1_Desactivar
                direccion=dirSalidaTransistor1Desactivar
            Case Codigo_Transistor2_Desactivar
                direccion=dirSalidaTransistor2Desactivar
            Case Codigo_Escucha
                direccion=dirTelefonosEscucha
            Case Codigo_Antidesarme
                direccion=dirAlarmasAntidesarme
            Case Codigo_Bateria_Baja
                direccion=dirBateriaBaja
        End Select
        
        EE.WriteByte(direccion, pHabilitados)
        
        INTCON.7=FlagGIEH
        INTCON.6=FlagGIEL
   End Sub
      
   Public Sub GuardarDisparo()
        Dim
            ContadorDisparos As Byte,
            DatoAGuardar As Byte,
            temporal As Byte
        
        DatoAGuardar=RegistroDisparos(ContadorRegistroDisparos-1).Zona
        If RegistroDisparos(ContadorRegistroDisparos-1).EsCableado Then
            DatoAGuardar.bits(7)=1
        EndIf
        
		FlagGIEH=INTCON.7
		INTCON.7=0
		FlagGIEL=INTCON.6
		INTCON.6=0
        For ContadorDisparos = 0 To 6
            temporal=EE.ReadByte(dirDisparosGuardados+ContadorDisparos+1)
            EE.WriteByte(dirDisparosGuardados+ContadorDisparos, temporal)
        Next
        EE.WriteByte(dirDisparosGuardados+7, DatoAGuardar)
		INTCON.7=FlagGIEH
		INTCON.6=FlagGIEL
   End Sub
   
   Public Function LeerDisparo(pDisparo As Byte, ByRef pZona As Byte, ByRef pEsCableado As Boolean) As Boolean
        Dim DatoLeido As Byte
        
        DatoLeido=EE.ReadByte(dirDisparosGuardados+pDisparo)
        If DatoLeido=0 Then
            result=false
        Else
            pZona=DatoLeido And %01111111
            pEsCableado=DatoLeido.booleans(7)
            result=true
        EndIf
   End Function
   
'   Public Sub BorrarDisparos()
'        Dim Contador As Byte
'        
'        INTCON.7=0
'        For Contador=0 To 7
'            EE.WriteByte(dirDisparosGuardados+Contador, 0)
'        Next
'        INTCON.7=1
'   End Sub
   
            
   'LeerSeteos()
   Const Comilla=#34
  
'   GuardarTelefono(1, "1111111111")
'   GuardarString(dirCuerpoMensajeRoboZ1C, "Zona 1")
'   GuardarTelefono(2, "2222222222")
'   GuardarString(dirCuerpoMensajeRoboZ2C, "Zona 2")
'   GuardarTelefono(3, "3333333333")
'   GuardarString(dirCuerpoMensajeRoboZ3C, "Zona 3")
'   GuardarTelefono(4, "4444444444")
'   GuardarString(dirCuerpoMensajeRoboZ4C, "Zona 4")
'   GuardarTelefono(5, "5555555555")
'   GuardarString(dirCuerpoMensajeRoboZ5C, "Zona 5")
'   GuardarTelefono(6, "6666666666")
'   GuardarString(dirCuerpoMensajeRoboZ6C, "Zona 6")
'   GuardarTelefono(7, "7777777777")
'   GuardarString(dirCuerpoMensajeRoboZ7C, "Zona 7")
'   GuardarTelefono(8, "8888888888")
'   GuardarString(dirCuerpoMensajeRoboZ8C, "Zona 8")

'    GuardarString(dirAPN, " " + Comilla + "internet.movil" + Comilla + "," + Comilla + "internet" + Comilla + "," + Comilla + "internet" + Comilla)
'    GuardarString(dirServidor, " " + Comilla + "138.36.99.64" + Comilla + "," + "5555")
'    EE.WriteByte(dirFlagsGPRS, %********)
'    EE.WriteWord(dirCodigoProgramacion, 10000)
'    EE.WriteWord(dirTiempoTest, 60)
'    EE.WriteByte(dirMaximoErroresRonda, 3)
'    GuardarTelefono(7, "2804307610")
    
'   texto=LeerTelefono(0)
'   texto=LeerTelefono(1)
   
'    EE.WriteByte(dirFlagsCentral1, %01000111)
'    EE.WriteByte(dirAlarmasAsaltoC, %********)
