{
*****************************************************************************
*  Name    : Central.BAS                                                    *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2010 Scanner Alarmas                             *
*          : All Rights Reserved                                            *
*  Date    : 02/02/2010                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

Module CENTRAL

    'Include "6P20.bas"
    Include "H6P20.bas"
    Include "IO.bas"
    Include "BUZZER.bas"
    Include "DISPLAY.bas"
    Include "wala.bas"
//    Include "MEMORIA.bas"
    'Include "SALTEC.bas"
//    Include "GSM.bas"
    
    //Declaracion de estructuras
    Public Structure TMensajeDeTexto
        EsNuevo As Byte                                     //Indica si es un mensaje nuevo o no
        Tipo As Byte                                        //Tipo de mensaje
        InfoAdicional As Byte                               //Informacion adicional al mensaje de texto
        PosicionEnMemoria As Byte                           //Posicion en la eeprom donde se encuentra guardado el elemento inalambrico que genero el mensaje
        Telefonos As Byte                                   //Info de los telefonos a los que se debe enviar
        ErroresTelefonos As Byte                            //Marcas de error de cada uno de los telefonos
        ErroresRonda As Byte                                //Contador de errores de ronda completa
        TimeStamp As Word                                   //Estampa de tiempo indicando el momnto de la generacion del mensaje
        TelefonoRespuesta As String(15)                     //En el caso de que el mensaje sea de contestacion de configuracion, aca guardo el telefo a contestar
        TextoRecibido As String(100)                        //En el caso de que el mensaje sea de contestacion de configuracion, aca guardo el mensaje recibido
    End Structure 

    //Declaracion de estructuras
    Structure TRegistroDisparo
        Zona As Byte
        EsCableado As Boolean
    End Structure 
    
    //Declaracion de estructuras
    Public Structure TMensajeContactID
        'Account(4) As Byte
        EventQualifier As Byte
        EventCode(3) As Byte
        'Partition = 00
        Zone(3) As Byte
        CheckSum As Byte
        Errores As Byte
        MensajeCompleto(7) As Byte Union
    End Structure 
    
    //Declaracion de conatantes locales
    
    //Declaracion de varibles locales
    //Dim MensajeDePrueba As TMensajeContactID

    //Declaracion de constantes publicas
    Public Const
        EstadoCentralActivada=1,
        EstadoCentralDesactivada=2,
        EstadoCentralDemora=3,
        EstadoCentralActivando=4,
        EstadoCentralPanico=5,
        MetodoMarcadoTonos=1,
        MetodoMarcadoPulsos=2,
        CantidadTelefonos=8,
        
        TipoInicioUltimoEstado=0,
        TipoInicioActivada=EstadoCentralActivada,
        TipoInicioDesactivada=EstadoCentralDesactivada,
        
        TiempoBloqueoAsalto=10,
        TiempoBloqueoEmergencia=10,
        TiempoBloqueoIncendio=10,
        TiempoPrealarma=10,
        
        TiempoPanico=20,
        
        LongitudColaGPRS=6,
        LongitudCola=7,

        UnidadMs = 0,
        UnidadSeg = 1,
        UnidadMin = 2,
        UnidadHoras = 3,
        
        TestNinguno = 0,                //Indica que ningun test se corrio con exito todavia
        TestSinGSM = 1,                 //Indica que se corrio el primer test (sin GSM) con exito
        TestConGSM = 2,                 //Indica que se corrio el segundo test (con GSM) con exito y el equipo esta operativo

        //Codigos de tipo de mensajes que puede generar la central
        //Deben coincidir con los del modulo ROM!!!!!!!
        Mensaje_Robo_Z1C = 30,
        Mensaje_Robo_Z2C = 31,
        Mensaje_Robo_Z3C = 32,
        Mensaje_Robo_Z4C = 33,
        Mensaje_Robo_Z5C = 34,
        Mensaje_Robo_Z6C = 35,
        Mensaje_Robo_Z7C = 36,
        Mensaje_Robo_Z8C = 37,
        Mensaje_Robo_Z1I = 38,
        Mensaje_Robo_Z2I = 39,
        Mensaje_Robo_Z3I = 40,
        Mensaje_Robo_Z4I = 41,
        Mensaje_Robo_Z5I = 42,
        Mensaje_Robo_Z6I = 43,
        Mensaje_Robo_Z7I = 44,
        Mensaje_Robo_Z8I = 45,
        Mensaje_Antidesarme = 46,
        Mensaje_AsaltoC = 47,
        Mensaje_AsaltoI = 48,
        Mensaje_EmergenciaC = 49,
        Mensaje_EmergenciaI = 50,
        Mensaje_IncendioC = 51,
        Mensaje_IncendioI = 52,
        Mensaje_Activado = 53,
        Mensaje_Desactivado = 54,
        Mensaje_Desconexion_Alimentacion_Inmediata = 55,
        Mensaje_Reconexion_Alimentacion_Inmediata = 56,
        Mensaje_Desconexion_Alimentacion_Temporizada = 57,
        Mensaje_Reconexion_Alimentacion_Temporizada = 58,
        Mensaje_Respuesta_Activar = 59,
        Mensaje_Respuesta_Desactivar = 60,
        Mensaje_Respuesta_Ya_Activada = 64,
        Mensaje_Respuesta_Ya_Desactivada = 65,
        Mensaje_Transistor1_Activar = 96,
        Mensaje_Transistor2_Activar = 97,
        Mensaje_Transistor1_Desactivar = 98,
        Mensaje_Transistor2_Desactivar = 99,

        Mensaje_Bateria_Baja = 107
    
    //Declaracion de varibles publicas
    Public Dim
        flagEntradaWalaHabilitada As Boolean,           //Flag que indica que la entrada de prealarma funciona como estado de wala
        Estado As Byte,                                 //Estado de la central: Activada, Desactivada, Disparada, Demora, Activando
        EstadoAnterior As Byte,                         //Estado de la central: Activada, Desactivada, Disparada, Demora, Activando
        CancelacionActivada As Boolean,                 //Indica si e habilito la cancelacion de zonas
        PrealarmaActivada As Boolean,                   //Indica si e habilito la cancelacion de zonas
        HuboDisparo As Boolean,                         //Indica si hubo un disparo en el ultimo periodo activado
        UltimaZonaDisparo As Byte,                      //Indica la zona que genero el disparo en el ultimo periodo activado
        ZonasNormalAbiertas As Byte,                    //Contiene las zonas normal abiertas bit0..bit7 = zona1..zona8
        ZonasAutoExcluidas As Byte,                     //Contiene las zonas auto excluidas bit0..bit7 = zona1..zona8
        ZonasCableadasActuadas As Byte,                 //Contiene las zonas cableadas actuadas bit0..bit7 = zona1..zona8
        ZonasCableadasCancelables As Byte,              //Contiene las zonas cableadas cancelables bit0..bit7 = zona1..zona8
        ZonasCableadasDemoradas As Byte,                //Contiene las zonas cableadas demoradas bit0..bit7 = zona1..zona8
        ZonasCableadasCancelablesBak As Byte,           //Contiene las zonas cableadas cancelables bit0..bit7 = zona1..zona8 resguardo en ram del valor en rom
        ZonasCableadas24Horas As Byte,                  //Contiene las zonas cableadas 24 horas bit0..bit7 = zona1..zona8
        ZonasInalambricasCancelables As Byte,           //Contiene las zonas inalambricas cancelables bit0..bit7 = zona1..zona8 resguardo en ram del valor en rom
        ZonasInalambricasCancelablesBak As Byte,        //Contiene las zonas inalambricas cancelables bit0..bit7 = zona1..zona8
        ZonasInalambricasDemoradas As Byte,             //Contiene las zonas inalambricas demoradas bit0..bit7 = zona1..zona8
        ZonasInalambricas24Horas As Byte,               //Contiene las zonas inalambricas 24 horas bit0..bit7 = zona1..zona8
        DisparoCableado As Boolean,                     //Flag de indicacion de disparo cableado
        TiempoSirenas As Byte,                          //Tiempo de accionamiento de sirenas
        TiempoEntrada As Byte,                          //Tiempo de entrada para las zonas demoradas
        TiempoSalida As Byte,                           //Tiempo de salida para la activacion de la central
        TiempoTest As Word,                             //Tiempo del periodo para el envio de test por GPRS
        TiempoTemporizadoDesconexion As Word,           //Tiempo de desconexion minimo para enviar a los telefonos temporizados
        TimerSirenas As Byte,                           //Timer que cuenta el tiempo de accionamiento de sirenas
        TimerPrealarma As Byte,                         //Timer que cuenta el tiempo de accionamiento de la prealarma
        TimerBeepsSirenas As Byte,                      //Timer que cuenta el tiempo de accionamiento para los beeps de sirenas
        ContadorBeepsSirenas As Byte,                   //Contador que cuenta los beeps de sirenas
        TimerEntrada As Byte,                           //Timer que cuenta el tiempo de entrada para las zonas demoradas
        TimerSalida As Byte,                            //Timer que cuenta el tiempo de salida para activar la central
        TimerTest As Word,                              //Timer del periodo para el envio de test por GPRS
        TimerPanico As Byte,                            //Timer para temporizar el panico que viene por sms
        BeepsPorSirenaInterior As Boolean,              //Indicacion si se deben generar los beeps por sirena interior
        BeepsPorSirenaExterior As Boolean,              //Indicacion si se deben generar los beeps por sirena exterior
        MetodoMarcado As Byte,                          //Contiene el tipo de marcado, tonos o pulsos
        AsaltoCableadoHabilitado As Boolean,            //Indica que la zona 6 se utiliza como asalto cableado
        EmergenciaCableadaHabilitada As Boolean,        //Indica que la zona 7 se utiliza como emergencia cableada
        IncendioCableadoHabilitado As Boolean,          //Indica que la zona 8 se utiliza como incendio cableado
        AntidesarmeHabilitado As Boolean,               //Indica que el antideasarme esta habilitado
        AsaltoPorBoton2Habilitado As Boolean,           //Indica que el asalto por boton 2 al desactivar esta habilitado
        CentralBloqueadaInstalador As Boolean,          //Indica que la central se encuentra bloqueada para cualquier uso excepto el desbloqueo (instalador)
        CentralBloqueadaFabrica As Boolean,             //Indica que la central se encuentra bloqueada para cualquier uso excepto el desbloqueo (fabrica)
        TimerDisparo As Byte,                           //Timer generico que cuenta los tiempos entre eventos de la secuencia de disparo
        TimerBateriaBaja As Byte,                       //Timer que se utiliza para la indicacion de bateria baja
        ZonaBateriaBaja As Byte,                        //Almacena la zona que transmitio con bateria baja
        TimerBloqueoBateriaBaja As Byte,                //Timer para bloquear el control de bateria baja por 5 segundos y solo chequear una vez por TX
'        ContadorTelefonos As Byte,                      //Numero de telefono que se esta discando en este momento
'        TipoDisparo As Byte,                            //Robo, asalto, emergencia medica o incendio. Se ultiza para discriminar comportamientos y seleccionar el mensaje
//        ContadorRondasDisparo As Byte,                  //Numero de ronda de disparo actual
//        CantidadRondasDisparo As Byte,                  //Numero de rondas de disparo a realizar
//        CantidadMensajes As Byte,                       //Cantidad de veces que se debe reproducir un mensaje en una llamada
//        ContadorMensajes As Byte,                       //Numero de mensaje que se esta reproduciendo en este momento
        UltimoEstadoCentralEEPROM As Byte,              //Almacena el ultimo estado de la central para poder restaurarlo si se pierde la alimentacion por completo
        TipoInicioCentral As Byte,                      //Almacena el tipo de inicio de la central: desactivada, activada o el ultimo estado.

        FlagSalida1PorNivel As Boolean,
        FlagSalida1EstadoNormalDesactivado As Boolean,
        TiempoPulsoSalida1 As Byte,
        UnidadesTiempoPulsoSalida1 As Byte,
        flagSalidaTransistor1Activar As Boolean,            //Flag que indica la solicitud de activacion de la salida a transistor 1
        flagSalidaTransistor1Desactivar As Boolean,         //Flag que indica la solicitud de desactivacion de la salida a transistor 1
        TimerSalida1 As LongWord,

        FlagSalida2PorNivel As Boolean,
        FlagSalida2EstadoNormalDesactivado As Boolean,
        TiempoPulsoSalida2 As Byte,
        UnidadesTiempoPulsoSalida2 As Byte,
        flagSalidaTransistor2Activar As Boolean,            //Flag que indica la solicitud de activacion de la salida a transistor 1
        flagSalidaTransistor2Desactivar As Boolean,         //Flag que indica la solicitud de desactivacion de la salida a transistor 1
        TimerSalida2 As LongWord,

        RegistroDisparos(8) As TRegistroDisparo,        //Registro de secuencia de disparo
        ContadorRegistroDisparos As Byte,               //Puntero que indica la cantidad de disparos ocurridos durante el periodo de activacion actual
        flagDisparoSinGuardar As Boolean,               //Flag que indica que existe un nuevo disparo sin guardar en memoria
        ZonaConDemora As Byte,                          //Almacena el numero de zona que genero el proceso de demora
        ZonaConDemoraEsCableada As Boolean,             //Almacena si la zona que ocaciono la demora es cableada
        FlagMostrarAutoexcluidas As Boolean,            //Flag que sirve para mostrar autoexcluidas
        FlagEnviarEstadoAlTeclado As Boolean,           //Flag que sirve para enviar el estado al teclado
        flagLlamadaEnCurso As Boolean,                      //Flag de llamada en cuarso actualmente
        ColaDeMensajes(LongitudCola) As TMensajeDeTexto,
        PrimerElementoCola, UltimoElementoCola As Byte,
        ColaDeMensajesGPRS(LongitudColaGPRS) As TMensajeContactID,
        PrimerElementoColaGPRS, UltimoElementoColaGPRS As Byte,
        TimerBloqueoAsalto As Byte,                     //Indica el tiempo de bloqueo para evitar redisparos inmediatos
        TimerBloqueoEmergencia As Byte,                 //Indica el tiempo de bloqueo para evitar redisparos inmediatos
        TimerBloqueoIncendio As Byte,                   //Indica el tiempo de bloqueo para evitar redisparos inmediatos
        TimerAlimentacionConectada As Word,             //Indica la cantidad de segundos que estuvo conectada la alimentacion
        TimerAlimentacionDesconectada As Word,          //Indica la cantidad de segundos que estuvo desconectada la alimentacion
        TimerMostrarRSSI As Byte,                       //Indica el tiempo que se debe mostrar el rssi en el display de la central (segundos)
        FlagHuboDesconexionTemporizada As Boolean,      //Indica que la ultima desconexion duro por lo menos el tiempo especificado para el envio del mensaje
        FlagAlimentacionNormal As Boolean,              //Indica si la alimentacion esta normal, como para saber si hay que esperar una conexion o desconexion
        FlagHabilitarODeshabilitarPrealarma As Boolean, //Indica si se estan pulsando mas de 1 segundo los dos botones del remoto, como para cambiar prealarma
        OrigenUltimoComando As Byte,                    //Indica quien genero el ultimo comando sobre la central:
                                                        //                                                          0: Desde el panel
                                                        //                                                          1-8: Uno de los telefonos
                                                        //                                                          9-16: Uno de los grupos de remotos
        PosicionEnMemoriaUltimoComando As Byte,         //Indica en que posicion de memoria de inalambricos se encuentra el remoto que ocaciono el ultimo comando
        CodigoProgramacion As Word,                     //Contiene el codigo de programacion para poder inbgresar por sms
        MaximoErroresRonda As Byte,                     //Valor de maxima cantidad de errores de ronda
        Account(4) As Byte,                             //registro que contiene el numero de cuenta de ContactID
        flagGPRSHabilitado As Boolean,                      //Flag de indicacion que el GPRS debe usarse (solo si se cargaron los datos)
        flagGPRSPorDominio As Boolean',                      //Flag de indicacion que el GPRS debe usarse por dominio
        'TestFabricaConcluido As Byte                        //Almacena en ram el valor del ultimo test realizado con exito (ninguno, sin gsm, con gsm)
        
    Private Sub CalcularCRC(ByRef pMensaje As TMensajeContactID)
        Dim 
            Ref As Byte,
            Total As Byte, 
            CheckSum As pMensaje.CheckSum,
            Contador As Byte,
            Valor As Byte
            
            Ref=0
            //sumo primero los valores 00 (10+10) de la info de particion
            Total=10+10
            //Luego el account que no es parte de la estructura
            For contador=0 To 3
                If Account(contador)=0 Then
                    Total=Total+10
                Else
                    Total=Total+Account(contador)
                EndIf
            Next
            For Contador = 0 To 6
                Valor=pMensaje.MensajeCompleto(contador)
                If Valor=0 Then
                    Total=Total+10
                Else
                    Total=Total+Valor
                EndIf
            Next 
            While Ref<Total
                Ref=Ref+15
            Wend
            CheckSum=Ref-Total
            If CheckSum=0 Then
                CheckSum=15
            EndIf
    End Sub
    
    //Cancela el mensaje mas antiguo y lo borra de la cola de mensajes
    Public Sub BorrarPrimerMensaje()
        //TODO: Cancelar el envio de el mensaje si esta en curso
        
        //Corro el puntero del primero a la siguiente posicion
        Inc(PrimerElementoCola)
        //Si esta por fuera del rango del vector arranco desde cero otra vez
        If PrimerElementoCola>(LongitudCola-1) Then
            PrimerElementoCola=0
        EndIf
    End Sub
    
    //Cancela el mensaje mas antiguo y lo borra de la cola de mensajes de GPRS
    Public Sub BorrarPrimerMensajeGPRS()
        //TODO: Cancelar el envio de el mensaje si esta en curso
        
        //Corro el puntero del primero a la siguiente posicion
        Inc(PrimerElementoColaGPRS)
        //Si esta por fuera del rango del vector arranco desde cero otra vez
        If PrimerElementoColaGPRS>(LongitudColaGPRS-1) Then
            PrimerElementoColaGPRS=0
        EndIf
    End Sub
    
    //Cancela los mensajes de un tipo determinado
    Public Sub CancelarMensajes(pTipo As Byte)
        Dim contador As Byte
        
        
        //Cargo el Primer elemento
        contador=PrimerElementoCola
        
        //Mientras no llegue al final de la cola
        While contador <> UltimoElementoCola
            //Si el elemnto en esta posicion es del tipo seleccionado
            If ColaDeMensajes(contador).Tipo=pTipo Then
                //Borro los errores para que no haya reintentos
                ColaDeMensajes(contador).ErroresTelefonos=0
                ColaDeMensajes(contador).ErroresRonda=0
                //Borro los telefonos a los que no se envio todavia
                ColaDeMensajes(contador).Telefonos=0
                //Me aseguro que si era nuevo no se procese
                ColaDeMensajes(contador).EsNuevo=0
            EndIf
            //Paso al siguiente mensaje
            Inc(contador)
            //Si esta por fuera del rango del vector arranco desde cero otra vez
            If contador>(LongitudCola-1) Then
                contador=0
            EndIf
        Wend
    End Sub
    
    //coloca un mensaje en la cola de mensajes de GPRS
    Public Sub PonerMensajeEnColaGPRS(ByRef pMensaje As TMensajeContactID)
        CalcularCRC(pMensaje)
        ColaDeMensajesGPRS(UltimoElementoColaGPRS)=pMensaje
        
        //Corro el puntero del ultimo a la siguiente posicion
        Inc(UltimoElementoColaGPRS)
        //Si esta por fuera del rango del vector arranco desde cero otra vez
        If UltimoElementoColaGPRS>(LongitudColaGPRS-1) Then
            UltimoElementoColaGPRS=0
        EndIf
        
        //Si hay desbordamiento de la cola (ultimo=primero)
        If PrimerElementoColaGPRS = UltimoElementoColaGPRS Then
            //Cancelo si esta en curso y borro el primer mensaje de la cola
            BorrarPrimerMensajeGPRS()
        EndIf
        
    End Sub
    
    //Coloca el mensaje de texto en cola de envio
    Public Sub PonerMensajeEnCola(pTipo As Byte, pInfoAdicional As Byte, pPosicionEnMemoria As Byte)
    
        Dim MensajeDeTexto As TMensajeDeTexto
        
        With MensajeDeTexto
            EsNuevo=1                               //Lo marco como nuevo para que el manager pueda cargarle los telefonos mas adelante
                                                    //No los cargo en esta instancia porque desde este modulo no tengo acceso a la rom.
            Tipo=pTipo                              //Denota el tipo de mensaje prucido de los citados en el modulo de central.
            InfoAdicional=pInfoAdicional            //Cualquier info adicional que sea necesaria para enviar al usuario (por ej grupo de remotos)
            PosicionEnMemoria=pPosicionEnMemoria    //La posicion en memoria del elemento inalambrico que genero el mensaje, si se aplica
            Telefonos=0                             //Campo en blanco para que el modulo GSM cargue los telefonos a los que tiene que enviar el mensaje
            ErroresTelefonos=0                      //Campo en blanco para que el modulo GSM marque los numeros de telefono que dieron error y resolver a partir de ahi
            ErroresRonda=0                          //Campo en blanco para que el modulo GSM cuente los 4errores de ronda completa
            TimeStamp=Tick                          //Marca de timpo para saber cuanto tiempo mantenerlo vivo o purgarlo de la cola
        End With
        
        //Escribo el mensaje en la cola
        ColaDeMensajes(UltimoElementoCola)=MensajeDeTexto
        
        //Corro el puntero del ultimo a la siguiente posicion
        Inc(UltimoElementoCola)
        //Si esta por fuera del rango del vector arranco desde cero otra vez
        If UltimoElementoCola>(LongitudCola-1) Then
            UltimoElementoCola=0
        EndIf
        
        //Si hay desbordamiento de la cola (ultimo=primero)
        If PrimerElementoCola = UltimoElementoCola Then
            //Cancelo si esta en curso y borro el primer mensaje de la cola
            BorrarPrimerMensaje()
        EndIf
        
    End Sub

    //Coloca el mensaje de texto en cola de envio
    Public Sub PonerMensajeEnCola(pMensaje As TMensajeDeTexto)
    
'        With MensajeDeTexto
'            EsNuevo=1                       //Lo marco como nuevo para que el manager pueda cargarle los telefonos mas adelante
'                                            //No los cargo en esta instancia porque desde este modulo no tengo acceso a la rom.
'            Tipo=pTipo                      //Denota el tipo de mensaje prucido de los citados en el modulo de central.
'            InfoAdicional=pInfoAdicional    //Cualquier info adicional que sea necesaria para enviar al usuario (por ej grupo de remotos)
'            Telefonos=0                     //Campo en blanco para que el modulo GSM cargue los telefonos a los que tiene que enviar el mensaje
'            ErroresTelefonos=0              //Campo en blanco para que el modulo GSM marque los numeros de telefono que dieron error y resolver a partir de ahi
'            ErroresRonda=0                  //Campo en blanco para que el modulo GSM cuente los 4errores de ronda completa
'            TimeStamp=Tick                  //Marca de timpo para saber cuanto tiempo mantenerlo vivo o purgarlo de la cola
'        End With
        
        //Escribo el mensaje en la cola
        ColaDeMensajes(UltimoElementoCola)=pMensaje
        
        //Corro el puntero del ultimo a la siguiente posicion
        Inc(UltimoElementoCola)
        //Si esta por fuera del rango del vector arranco desde cero otra vez
        If UltimoElementoCola>(LongitudCola-1) Then
            UltimoElementoCola=0
        EndIf
        
        //Si hay desbordamiento de la cola (ultimo=primero)
        If PrimerElementoCola = UltimoElementoCola Then
            //Cancelo si esta en curso y borro el primer mensaje de la cola
            BorrarPrimerMensaje()
        EndIf
        
    End Sub

    //Beep de central con condicionamiento de seleccion de sirenas    
    Public Sub Beep()
        IO.SirenaInterior=IO.SirenaApagada
        IO.SirenaExterior=IO.SirenaApagada
        Delay(70)
        If CENTRAL.BeepsPorSirenaInterior Then
            IO.SirenaInterior=IO.SirenaEncendida
        EndIf
        If CENTRAL.BeepsPorSirenaExterior Then
            IO.SirenaExterior=IO.SirenaEncendida
        EndIf
        BeepCorto()
        IO.SirenaInterior=IO.SirenaApagada
        IO.SirenaExterior=IO.SirenaApagada
    End Sub
    
    //Inicia el proceso de activacion de la central que dura el tiempo de salida
    Public Sub ActivarCentral(pOrigen As Byte, pCableadas As Byte = 0, pInalambricas As Byte=0)
        OrigenUltimoComando=pOrigen
        PosicionEnMemoriaUltimoComando=HT6P20.PosicionEnMemoria
        Beep()
        'SALTEC.CentralActivando()
        Estado=EstadoCentralActivando
        TimerSalida=TiempoSalida
        TimerSirenas=0
        ContBuenos=0
        TimerBateriaBaja=0
        IO.ParpadearLedEstado500ms()
        //Si recibi la solicitud de un telefono agrego el mensaje de respuesta a la cola
        If pOrigen >=1 And pOrigen <=8 Then
            PonerMensajeEnCola(Mensaje_Respuesta_Activar, pOrigen, 0)
        EndIf
        If WALA.EstadoActivacionWala=WALA.Desactivada Or WALA.EstadoActivacionWala=WALA.Indeterminado Then
            WALA.ComandoWala(WALA.ActivarByte, 8)
        EndIf
        Delay(1000)
        HuboDisparo=false
        ContadorRegistroDisparos=0
        If pCableadas<>0 Or pInalambricas<>0 Then
            //Guardo los valores oficiales de cancelacion de zonas
            'ZonasCableadasCancelablesBak=ZonasCableadasCancelables
            'ZonasInalambricasCancelablesBak=ZonasInalambricasCancelables
            //Seteo los nuevos valores de zonas cancelables
            ZonasCableadasCancelables=pCableadas
            ZonasInalambricasCancelables=pInalambricas
            CancelacionActivada=true
            //Ademas aviso a wala que es activacion parcial
            WALA.ComandoWala(WALA.ActivarParcialByte, 8)
        Else
            CancelacionActivada=false
        EndIf
        
    End Sub

    //Situa la central en estado de desactivada
    Public Sub DesactivarCentral(pOrigen As Byte)
        Dim contador As Byte
        Dim MensajeGPRS As TMensajeContactID
        
        Beep()
        Beep()
        'SALTEC.CentralDesactivada()
        TimerSirenas=0
        //Si hay mensajes pendientes de central activada los cancelo
        CancelarMensajes(Mensaje_Activado)

        //Si hay mensajes pendientes de robo los cancelo
        For contador=Mensaje_Robo_Z1C To Mensaje_Robo_Z8I
            CancelarMensajes(contador)
        Next

        //Si la central estaba activada envio el mensaje de central desactivada
        If Estado=EstadoCentralActivada Or Estado=EstadoCentralDemora Then
            //Genero el mensaje en cola y anoto quien fue el que origino el mensaje
            PonerMensajeEnCola(Mensaje_Desactivado, pOrigen, HT6P20.PosicionEnMemoria)
            //Genero el mensaje para GPRS
            MensajeGPRS.EventQualifier=1
            
            MensajeGPRS.EventCode(0)=4
            MensajeGPRS.EventCode(1)=0
            Select pOrigen
                Case 0 
                    //Desde el panel...
                    MensajeGPRS.EventCode(2)=9
                    //Cargo el usuario en 000
                    MensajeGPRS.Zone(0)=0
                    MensajeGPRS.Zone(1)=0
                    MensajeGPRS.Zone(2)=0
                Case > 8
                    //Desde un remoto
                    MensajeGPRS.EventCode(2)=1
                    MensajeGPRS.Zone(0)=0
                    MensajeGPRS.Zone(1)=0
                    MensajeGPRS.Zone(2)=pOrigen-8
                Else
                    //Desde un telefono....
                    MensajeGPRS.EventCode(2)=7
                    MensajeGPRS.Zone(0)=0
                    MensajeGPRS.Zone(1)=0
                    MensajeGPRS.Zone(2)=pOrigen
            End Select
            //Lo cargo en la cola de mensajes
            PonerMensajeEnColaGPRS(MensajeGPRS)
            
            //Desactivo tambien la prealarma
            PrealarmaActivada=false
        EndIf
        
        Estado=EstadoCentralDesactivada
        ContBuenos=0
        IO.ApagarLedEstado()
        //Si recibi la solicitud de un telefono agrego el mensaje de respuesta a la cola
        If pOrigen >=1 And pOrigen <=8 Then
            PonerMensajeEnCola(Mensaje_Respuesta_Desactivar, pOrigen, 0)
        EndIf
        WALA.ComandoWala(WALA.DesactivarByte, 8)
        WALA.ContadorPulsosLed=0
        Delay(1000)
        ZonasAutoExcluidas=0
        //restauro los valores oficiales de zonas cancelables
        ZonasCableadasCancelables=ZonasCableadasCancelablesBak
        ZonasInalambricasCancelables=ZonasInalambricasCancelablesBak
        //Deshabilito la cancelacion para que se indiquen las zonas en display
        CancelacionActivada=false
        //Si hay registro de que hubo disparos
        If ContadorRegistroDisparos > 0 Then
            Beep()
            Beep()
            Beep()
        EndIf

        //Me encargo que cualquier pulsacion de teclas sea ignorada en el cambio de estado
        TiempoBtnProgPulsado=0
        TiempoBtnSelPulsado=0
    End Sub
    
    //Se corre cuando la central termina el tiempo de salida y queda feacientemente activada
    Public Sub DejarActivada()
        Dim MensajeGPRS As TMensajeContactID
        
        //Aviso como quedo a wala
        If CancelacionActivada Then
            WALA.ComandoWala(WALA.ActivarParcialByte, 8)
        Else
            WALA.ComandoWala(WALA.ActivarByte, 8)
        EndIf 
                
        //Si hay mensajes pendientes de central desactivada los cancelo
        CancelarMensajes(Mensaje_Desactivado)
        //Genero el mensaje en cola y anoto quien fue el que origino el mensaje
        PonerMensajeEnCola(Mensaje_Activado, OrigenUltimoComando, PosicionEnMemoriaUltimoComando)
        
        //Genero el mensaje para GPRS
        MensajeGPRS.EventQualifier=3
        
        MensajeGPRS.EventCode(0)=4
        MensajeGPRS.EventCode(1)=0
        Select OrigenUltimoComando
            Case 0 
                //Desde el panel...
                MensajeGPRS.EventCode(2)=9
                //Cargo el usuario en 000
                MensajeGPRS.Zone(0)=0
                MensajeGPRS.Zone(1)=0
                MensajeGPRS.Zone(2)=0
            Case > 8
                //Desde un remoto
                MensajeGPRS.EventCode(2)=1
                MensajeGPRS.Zone(0)=0
                MensajeGPRS.Zone(1)=0
                MensajeGPRS.Zone(2)=OrigenUltimoComando-8
            Else
                //Desde un telefono....
                MensajeGPRS.EventCode(2)=7
                MensajeGPRS.Zone(0)=0
                MensajeGPRS.Zone(1)=0
                MensajeGPRS.Zone(2)=OrigenUltimoComando
        End Select
        MensajeGPRS.Errores=0
        //Lo cargo en la cola de mensajes
        PonerMensajeEnColaGPRS(MensajeGPRS)

        IO.SalidaBuzzer=BuzzerApagado
        Estado=EstadoCentralActivada
        IO.EncenderLedEstado()
        DISPLAY.ApagarDisplay()
        DISPLAY.ApagarPunto()
        Delay(250)
        BeepLargo()
        ZonasAutoExcluidas=ZonasCableadasActuadas
        'SALTEC.CentralActivada()
    End Sub
    
    //Me aseguro de que este al comienzo de la secuencia de disparo y que este seteada la primera pausa a 3 seg
    'Sub InicializarDisparo()
        'EstadoDisparo=EstadoDisparoInicio
        'TimerDisparo=3
        'ContadorTelefonos=0
        //ContadorRondasDisparo=0
        //MEMORIA.DetenerReproduccion()
    'End Sub

'    Public Sub CancelarDisparo()
    {
        Select TipoDisparo
            Case MEMORIA.mensajeRobo
                MEMORIA.DetenerReproduccion()
                IO.Linea=0
                EstadoDisparo=EstadoDisparoFinal
                TimerSirenas=0
            Case MEMORIA.mensajeAsalto, MEMORIA.mensajeEmergencia, MEMORIA.mensajeIncendio
                If Estado=EstadoCentralActivando Then
                    MEMORIA.DetenerReproduccion()
                    IO.Linea=0
                    EstadoDisparo=EstadoDisparoFinal
                    TimerSirenas=0
                EndIf
        End Select
        }
        'EstadoDisparo=EstadoDisparoFinal
        'TimerSirenas=0
'    End Sub
    
    Sub RegistrarDisparo()
        If ContadorRegistroDisparos<Bound(RegistroDisparos)+1 Then
            RegistroDisparos(ContadorRegistroDisparos).Zona=UltimaZonaDisparo
            RegistroDisparos(ContadorRegistroDisparos).EsCableado=DisparoCableado
            Inc(ContadorRegistroDisparos)
            flagDisparoSinGuardar=true
        EndIf
    End Sub
    
    Public Sub GenerarDisparoRobo()
        Dim MensajeGPRS As TMensajeContactID,
            tipomensaje As Byte
        
        If TimerSirenas=0 Then
            IO.ParpadearLedEstado250ms()
            DISPLAY.MostrarDigito(CENTRAL.UltimaZonaDisparo)
            WALA.Disparo(CENTRAL.DisparoCableado, CENTRAL.UltimaZonaDisparo)
            If CENTRAL.DisparoCableado Then
                DISPLAY.EncenderPunto()
                If CENTRAL.UltimaZonaDisparo=DISPLAY.DisplayAntidesarme Then
                    PonerMensajeEnCola(Mensaje_Antidesarme, 0, 0)
                    //Genero el mensaje para GPRS
                    MensajeGPRS.EventQualifier=1
                    
                    MensajeGPRS.EventCode(0)=1
                    MensajeGPRS.EventCode(1)=3
                    MensajeGPRS.EventCode(2)=7
                    MensajeGPRS.Zone(0)=0
                    MensajeGPRS.Zone(1)=0
                    MensajeGPRS.Zone(2)=0
                    MensajeGPRS.Errores=0
                    //Lo cargo en la cola de mensajes
                    PonerMensajeEnColaGPRS(MensajeGPRS)
    
                    'SALTEC.AntidesarmeDisparado()
                Else
                    Select UltimaZonaDisparo
                        Case 1
                            tipomensaje=Mensaje_Robo_Z1C
                        Case 2
                            tipomensaje=Mensaje_Robo_Z2C
                        Case 3
                            tipomensaje=Mensaje_Robo_Z3C
                        Case 4
                            tipomensaje=Mensaje_Robo_Z4C
                        Case 5
                            tipomensaje=Mensaje_Robo_Z5C
                        Case 6
                            tipomensaje=Mensaje_Robo_Z6C
                        Case 7
                            tipomensaje=Mensaje_Robo_Z7C
                        Case 8
                            tipomensaje=Mensaje_Robo_Z8C
                    End Select
                    
                    PonerMensajeEnCola(tipomensaje, 0, 0)
                    
                    //Genero el mensaje para GPRS
                    MensajeGPRS.EventQualifier=1
                    
                    MensajeGPRS.EventCode(0)=1
                    MensajeGPRS.EventCode(1)=3
                    //si es zona normal el codigo es 130
                    MensajeGPRS.EventCode(2)=0
                    //Si zona cancelable el codigo es 132
                    If ZonasCableadasCancelables.booleans(UltimaZonaDisparo-1) Then
                        MensajeGPRS.EventCode(2)=2
                    EndIf
                    //Si zona de 24 horas el codigo es 133
                    If ZonasCableadas24Horas.booleans(UltimaZonaDisparo-1) Then
                        MensajeGPRS.EventCode(2)=3
                    EndIf
                    MensajeGPRS.Zone(0)=0
                    MensajeGPRS.Zone(1)=0
                    MensajeGPRS.Zone(2)=UltimaZonaDisparo
                    MensajeGPRS.Errores=0
                    //Lo cargo en la cola de mensajes
                    PonerMensajeEnColaGPRS(MensajeGPRS)

                    'SALTEC.ZonaCableadaDisparada(CENTRAL.UltimaZonaDisparo)
                EndIf
            Else
                DISPLAY.ApagarPunto()

                Select UltimaZonaDisparo
                    Case 1
                        tipomensaje=Mensaje_Robo_Z1I
                    Case 2
                        tipomensaje=Mensaje_Robo_Z2I
                    Case 3
                        tipomensaje=Mensaje_Robo_Z3I
                    Case 4
                        tipomensaje=Mensaje_Robo_Z4I
                    Case 5
                        tipomensaje=Mensaje_Robo_Z5I
                    Case 6
                        tipomensaje=Mensaje_Robo_Z6I
                    Case 7
                        tipomensaje=Mensaje_Robo_Z7I
                    Case 8
                        tipomensaje=Mensaje_Robo_Z8I
                End Select

                PonerMensajeEnCola(tipomensaje, HT6P20.ZonaInalambrica, HT6P20.PosicionEnMemoria)

                //Genero el mensaje para GPRS
                MensajeGPRS.EventQualifier=1
                
                MensajeGPRS.EventCode(0)=1
                MensajeGPRS.EventCode(1)=3
                //si es zona normal el codigo es 130
                MensajeGPRS.EventCode(2)=0
                //Si zona cancelable el codigo es 132
                If ZonasCableadasCancelables.booleans(UltimaZonaDisparo-1) Then
                    MensajeGPRS.EventCode(2)=2
                EndIf
                //Si zona de 24 horas el codigo es 133
                If ZonasCableadas24Horas.booleans(UltimaZonaDisparo-1) Then
                    MensajeGPRS.EventCode(2)=3
                EndIf
                MensajeGPRS.Zone(0)=0
                MensajeGPRS.Zone(1)=1
                MensajeGPRS.Zone(2)=UltimaZonaDisparo
                MensajeGPRS.Errores=0
                //Lo cargo en la cola de mensajes
                PonerMensajeEnColaGPRS(MensajeGPRS)


                'SALTEC.ZonaInalambricaDisparada(CENTRAL.UltimaZonaDisparo)
            EndIf
            TimerSirenas=TiempoSirenas
            //Guarda en el vector de disparos y avisa al modulo de manejo de disparos que se debe guardar en eeprom
            RegistrarDisparo()
        EndIf

    End Sub

    Sub GenerarDisparoAsalto()
        Dim MensajeGPRS As TMensajeContactID
        
        If TimerBloqueoAsalto=0 Then
            DISPLAY.MostrarDigito(DISPLAY.DisplayAsalto)
            'WALA.Disparo(CENTRAL.DisparoCableado, CENTRAL.UltimaZonaDisparo)
            If CENTRAL.DisparoCableado Then
                DISPLAY.EncenderPunto()
                PonerMensajeEnCola(Mensaje_AsaltoC, 0, 0)
                MensajeGPRS.Zone(1)=0
                MensajeGPRS.Zone(2)=0
            Else
                DISPLAY.ApagarPunto()
                PonerMensajeEnCola(Mensaje_AsaltoI, HT6P20.ZonaInalambrica, HT6P20.PosicionEnMemoria)
                MensajeGPRS.Zone(1)=1
                MensajeGPRS.Zone(2)=HT6P20.ZonaInalambrica-8
            EndIf
            //Genero el mensaje para GPRS
            MensajeGPRS.EventQualifier=1
            MensajeGPRS.EventCode(0)=1
            MensajeGPRS.EventCode(1)=2
            MensajeGPRS.EventCode(2)=1
            MensajeGPRS.Zone(0)=0
            MensajeGPRS.Errores=0
            //Lo cargo en la cola de mensajes
            PonerMensajeEnColaGPRS(MensajeGPRS)
            TimerBloqueoAsalto=TiempoBloqueoAsalto
        EndIf
    End Sub
    
    Sub GenerarDisparoEmergencia()
        Dim MensajeGPRS As TMensajeContactID
        
        If TimerBloqueoEmergencia=0 Then
            IO.ParpadearLedEstado250ms()
            DISPLAY.MostrarDigito(DISPLAY.DisplayEmergencia)
            'WALA.Disparo(CENTRAL.DisparoCableado, CENTRAL.UltimaZonaDisparo)
            If CENTRAL.DisparoCableado Then
                DISPLAY.EncenderPunto()
                PonerMensajeEnCola(Mensaje_EmergenciaC, 0, 0)
                //Genero el mensaje para GPRS
                MensajeGPRS.Zone(1)=0
            Else
                DISPLAY.ApagarPunto()
                PonerMensajeEnCola(Mensaje_EmergenciaI, HT6P20.ZonaInalambrica, HT6P20.PosicionEnMemoria)
                //Genero el mensaje para GPRS
                MensajeGPRS.Zone(1)=1
            EndIf
            //Genero el mensaje para GPRS
            MensajeGPRS.EventQualifier=1
            MensajeGPRS.EventCode(0)=1
            MensajeGPRS.EventCode(1)=0
            MensajeGPRS.EventCode(2)=0
            MensajeGPRS.Zone(0)=0
            MensajeGPRS.Zone(2)=0
            MensajeGPRS.Errores=0
            //Lo cargo en la cola de mensajes
            PonerMensajeEnColaGPRS(MensajeGPRS)
            TimerBloqueoEmergencia=TiempoBloqueoEmergencia
        EndIf
    End Sub
    
    Sub GenerarDisparoIncendio()
        Dim MensajeGPRS As TMensajeContactID
        
        If TimerBloqueoIncendio=0 Then
            IO.ParpadearLedEstado250ms()
            DISPLAY.MostrarDigito(DISPLAY.DisplayIncendio)
            'WALA.Disparo(CENTRAL.DisparoCableado, CENTRAL.UltimaZonaDisparo)
            If CENTRAL.DisparoCableado Then
                DISPLAY.EncenderPunto()
                PonerMensajeEnCola(Mensaje_IncendioC, 0, 0)
                //Genero el mensaje para GPRS
                MensajeGPRS.Zone(1)=0
            Else
                DISPLAY.ApagarPunto()
                PonerMensajeEnCola(Mensaje_IncendioI, HT6P20.ZonaInalambrica, HT6P20.PosicionEnMemoria)
                //Genero el mensaje para GPRS
                MensajeGPRS.Zone(1)=1
            EndIf
            //Genero el mensaje para GPRS
            MensajeGPRS.EventQualifier=1
            MensajeGPRS.EventCode(0)=1
            MensajeGPRS.EventCode(1)=1
            MensajeGPRS.EventCode(2)=0
            MensajeGPRS.Zone(0)=0
            MensajeGPRS.Zone(2)=0
            MensajeGPRS.Errores=0
            //Lo cargo en la cola de mensajes
            PonerMensajeEnColaGPRS(MensajeGPRS)
            TimerSirenas=TiempoSirenas
            TimerBloqueoIncendio=TiempoBloqueoIncendio
        EndIf
    End Sub
    
    Sub GenerarDisparoPrealarma()
        
        If TimerPrealarma=0 Then
            IO.ParpadearLedEstado250ms()
            DISPLAY.MostrarDigito(DISPLAY.DisplayP)
            DISPLAY.EncenderPunto()
            TimerPrealarma=TiempoPrealarma
            ContadorBeepsSirenas=5
        EndIf
    End Sub
    
    Function DevolverNumeroZona(pZonas As Byte) As Byte
        result=99
        If pZonas.7=1 Then 
            result=8
        EndIf
        If pZonas.6=1 Then 
            result=7
        EndIf
        If pZonas.5=1 Then 
            result=6
        EndIf
        If pZonas.4=1 Then 
            result=5
        EndIf
        If pZonas.3=1 Then 
            result=4
        EndIf
        If pZonas.2=1 Then 
            result=3
        EndIf
        If pZonas.1=1 Then 
            result=2
        EndIf
        If pZonas.0=1 Then 
            result=1
        EndIf
    End Function

    //Procesamiento general de de las salidas
    Public Sub ProcesarSalidas()
        //Bloque de control de la salida 1
        //Si la salida opera por nivel
        If FlagSalida1PorNivel Then
            //Si hay una solicitud de activarla
            If flagSalidaTransistor1Activar Then
                //Activo la salida
                IO.SalidaTransistor1=IO.SalidaActivada
                //Borro la solicitud
                flagSalidaTransistor1Activar=false
            EndIf
            //Si hay una solicitud de desactivarla
            If flagSalidaTransistor1Desactivar Then
                //Desactivo la salida
                IO.SalidaTransistor1=IO.SalidaDesactivada
                //Borro la solicitud
                flagSalidaTransistor1Desactivar=false
            EndIf
        Else
            //Si hay una solicitud de activarla
            If flagSalidaTransistor1Activar Then
                //Cargo el timer con el tiempo del pulso
                Select UnidadesTiempoPulsoSalida1
                    //Si esta en milisegundos
                    Case UnidadMs
                        TimerSalida1=TiempoPulsoSalida1*4
                    //Si esta en segundos
                    Case UnidadSeg
                        TimerSalida1=TiempoPulsoSalida1*1000
                    //Si esta en milisegundos
                    Case UnidadMin
                        TimerSalida1=TiempoPulsoSalida1*60000
                    //Si esta en milisegundos
                    Case UnidadHoras
                        TimerSalida1=TiempoPulsoSalida1*3600000
                End Select
                //Borro la solicitud
                flagSalidaTransistor1Activar=false
            EndIf
            //Si hay una solicitud de desactivarla
            If flagSalidaTransistor1Desactivar Then
                //La descarto por estar trabajando por pulso
                flagSalidaTransistor1Desactivar=false
            EndIf
            //Si no estoy operando la salida
            If TimerSalida1=0 Then
                //Me aseguro que este en el estado normal
                If FlagSalida1EstadoNormalDesactivado Then
                    IO.SalidaTransistor1=IO.SalidaDesactivada
                Else
                    IO.SalidaTransistor1=IO.SalidaActivada
                EndIf
            //Sino
            Else
                //Estoy en tiempo de actuacion de la salida
                If FlagSalida1EstadoNormalDesactivado Then
                    IO.SalidaTransistor1=IO.SalidaActivada
                Else
                    IO.SalidaTransistor1=IO.SalidaDesactivada
                EndIf
            EndIf
        EndIf
        
        //Bloque de control de la salida 2
        //Si la salida opera por nivel
        If FlagSalida2PorNivel Then
            //Si hay una solicitud de activarla
            If flagSalidaTransistor2Activar Then
                //Activo la salida
                IO.SalidaTransistor2=IO.SalidaActivada
                //Borro la solicitud
                flagSalidaTransistor2Activar=false
            EndIf
            //Si hay una solicitud de desactivarla
            If flagSalidaTransistor2Desactivar Then
                //Desactivo la salida
                IO.SalidaTransistor2=IO.SalidaDesactivada
                //Borro la solicitud
                flagSalidaTransistor2Desactivar=false
            EndIf
        Else
            //Si hay una solicitud de activarla
            If flagSalidaTransistor2Activar Then
                //Cargo el timer con el tiempo del pulso
                Select UnidadesTiempoPulsoSalida2
                    //Si esta en milisegundos
                    Case UnidadMs
                        TimerSalida2=TiempoPulsoSalida2*4
                    //Si esta en segundos
                    Case UnidadSeg
                        TimerSalida2=TiempoPulsoSalida2*1000
                    //Si esta en milisegundos
                    Case UnidadMin
                        TimerSalida2=TiempoPulsoSalida2*60000
                    //Si esta en milisegundos
                    Case UnidadHoras
                        TimerSalida2=TiempoPulsoSalida2*3600000
                End Select
                //Borro la solicitud
                flagSalidaTransistor2Activar=false
            EndIf
            //Si hay una solicitud de desactivarla
            If flagSalidaTransistor2Desactivar Then
                //La descarto por estar trabajando por pulso
                flagSalidaTransistor2Desactivar=false
            EndIf
            //Si no estoy operando la salida
            If TimerSalida2=0 Then
                //Me aseguro que este en el estado normal
                If FlagSalida2EstadoNormalDesactivado Then
                    IO.SalidaTransistor2=IO.SalidaDesactivada
                Else
                    IO.SalidaTransistor2=IO.SalidaActivada
                EndIf
            //Sino
            Else
                //Estoy en tiempo de actuacion de la salida
                If FlagSalida2EstadoNormalDesactivado Then
                    IO.SalidaTransistor2=IO.SalidaActivada
                Else
                    IO.SalidaTransistor2=IO.SalidaDesactivada
                EndIf
            EndIf
        EndIf
        
    End Sub
    
    //Controla la linea de deteccion de alimentacion
    //Tambien el timer de test periodico
    //y genera los mensajes en cola segun corresponda
    Public Sub ControlarAlimentacion()
        Dim
            MensajeGPRS As TMensajeContactID,
            MensajePendiente As Boolean,
            contador As Byte
        
        If TimerTest=0 Then
            //Verifico si no hay mensajes de test pendientes de envio
            contador=PrimerElementoColaGPRS
            MensajePendiente = false
            While contador<>UltimoElementoColaGPRS
                If ColaDeMensajesGPRS(contador).EventCode(0)=6 Then
                    MensajePendiente=true
                EndIf
                Inc(contador)
                If contador>(LongitudColaGPRS-1) Then
                    contador=0
                EndIf
            Wend
            
            //Si no hay otro mensaje de test pendiente y no hay una llamada de audio en curso.....
            If Not (MensajePendiente Or flagLlamadaEnCurso) Then
                //Genero el mensaje para GPRS
                MensajeGPRS.EventQualifier=1
                
                MensajeGPRS.EventCode(0)=6
                MensajeGPRS.EventCode(1)=0
                MensajeGPRS.EventCode(2)=2
                MensajeGPRS.Zone(0)=0
                MensajeGPRS.Zone(1)=0
                MensajeGPRS.Zone(2)=0
                MensajeGPRS.Errores=0
                //Lo cargo en la cola de mensajes
                PonerMensajeEnColaGPRS(MensajeGPRS)
            EndIf
            
            TimerTest=TiempoTest
        EndIf
                
        //Si la alimentacion esta normal....
        If FlagAlimentacionNormal Then
            //Controlo la Desconexion
            //Si el timer de desconexion llego a 10 seg
            If TimerAlimentacionDesconectada = 10 Then
                //Aviso a los telefono por inmediato
                PonerMensajeEnCola(Mensaje_Desconexion_Alimentacion_Inmediata, 0, 0)
                //Genero el mensaje para GPRS
                MensajeGPRS.EventQualifier=1
                MensajeGPRS.EventCode(0)=3
                MensajeGPRS.EventCode(1)=0
                MensajeGPRS.EventCode(2)=1
                MensajeGPRS.Zone(0)=0
                MensajeGPRS.Zone(1)=0
                MensajeGPRS.Zone(2)=0
                MensajeGPRS.Errores=0
                //Lo cargo en la cola de mensajes
                PonerMensajeEnColaGPRS(MensajeGPRS)
                FlagAlimentacionNormal=false
            EndIf
        //Sino....
        Else
            //Si el timer llego al valor seteado para la temporizacion
            If Not FlagHuboDesconexionTemporizada And TimerAlimentacionDesconectada=TiempoTemporizadoDesconexion Then
                //Aviso a los telefono por inmediato
                PonerMensajeEnCola(Mensaje_Desconexion_Alimentacion_Temporizada, 0, 0)
                //Genero el mensaje para GPRS
                MensajeGPRS.EventQualifier=1
                MensajeGPRS.EventCode(0)=3
                MensajeGPRS.EventCode(1)=0
                MensajeGPRS.EventCode(2)=2
                MensajeGPRS.Zone(0)=0
                MensajeGPRS.Zone(1)=0
                MensajeGPRS.Zone(2)=0
                MensajeGPRS.Errores=0
                //Lo cargo en la cola de mensajes
                PonerMensajeEnColaGPRS(MensajeGPRS)
                //Marco que hubo una desconexion temporizada
                FlagHuboDesconexionTemporizada=true
            EndIf

            //Controlo la Reconexion
            //Si el timer de reconexion llego a 40 seg
            If TimerAlimentacionConectada = 40 Then
                //Si hubo desconexion temporizada aviso de la reconexion a temporizados
                If FlagHuboDesconexionTemporizada Then
                    //Aviso a los telefono por inmediato indicando que hubo desconexion temporizada
                    PonerMensajeEnCola(Mensaje_Reconexion_Alimentacion_Inmediata, 1, 0)
                    //Genero el mensaje para GPRS
                    MensajeGPRS.EventQualifier=3
                    MensajeGPRS.EventCode(0)=3
                    MensajeGPRS.EventCode(1)=0
                    MensajeGPRS.EventCode(2)=1
                    MensajeGPRS.Zone(0)=0
                    MensajeGPRS.Zone(1)=0
                    MensajeGPRS.Zone(2)=0
                    MensajeGPRS.Errores=0
                    //Lo cargo en la cola de mensajes
                    PonerMensajeEnColaGPRS(MensajeGPRS)
                    //Aviso a los telefonos temporizados
                    PonerMensajeEnCola(Mensaje_Reconexion_Alimentacion_Temporizada, 0, 0)
                    //Genero el mensaje para GPRS
                    MensajeGPRS.EventQualifier=3
                    MensajeGPRS.EventCode(0)=3
                    MensajeGPRS.EventCode(1)=0
                    MensajeGPRS.EventCode(2)=2
                    MensajeGPRS.Zone(0)=0
                    MensajeGPRS.Zone(1)=0
                    MensajeGPRS.Zone(2)=0
                    MensajeGPRS.Errores=0
                    //Lo cargo en la cola de mensajes
                    PonerMensajeEnColaGPRS(MensajeGPRS)
                    //Borro el flag
                    FlagHuboDesconexionTemporizada=false
                Else
                    //Aviso a los telefono por inmediato indicando que no hubo desconexion temporizada
                    PonerMensajeEnCola(Mensaje_Reconexion_Alimentacion_Inmediata, 0, 0)
                    //Genero el mensaje para GPRS
                    MensajeGPRS.EventQualifier=3
                    MensajeGPRS.EventCode(0)=3
                    MensajeGPRS.EventCode(1)=0
                    MensajeGPRS.EventCode(2)=1
                    MensajeGPRS.Zone(0)=0
                    MensajeGPRS.Zone(1)=0
                    MensajeGPRS.Zone(2)=0
                    MensajeGPRS.Errores=0
                    //Lo cargo en la cola de mensajes
                    PonerMensajeEnColaGPRS(MensajeGPRS)
                EndIf
                FlagAlimentacionNormal=true
            EndIf
        EndIf
    End Sub
    
    Public Sub ControlarDebounce()
        Dim Zona, contador As Byte
        
        Zona = 1
        For contador=0 To 7
            If IO.TimerZonaCableadaH(contador)>50 Then
                IO.ZonasCableadasDebounce = IO.ZonasCableadasDebounce Or Zona
            EndIf
            If IO.TimerZonaCableadaL(contador)>50 Then
                IO.ZonasCableadasDebounce = IO.ZonasCableadasDebounce And (Zona Xor 255)
            EndIf
            Zona=Zona<<1    
        Next
        
        IO.ZonaAntidesarmeDebounce = 0
        If IO.TimerZonaAntidesarme>50 Then
            IO.ZonaAntidesarmeDebounce = 1
        EndIf
    End Sub
    
    //Controla las zonas cableadas y establece los cambios de estado en caso de actuacion
    Public Sub ControlarZonasCableadas()
        Dim
            ZonasMascara As Byte,                               //Mascara con las zonas acuadas
            ZonaAsaltoCableado As ZonasMascara.bits(5),         //Zona de asalto cableado
            ZonaEmergenciaCableada As ZonasMascara.bits(6),     //Zona de emergencia cableado
            ZonaIncendioCableado As ZonasMascara.bits(7)        //Zona de incendio cableado
            
        'UltimaZonaDisparo=99
        
        ControlarDebounce()
        //Leo las entradas cableadas
        ZonasMascara=IO.ZonasCableadasDebounce
        
        //Aplico el cambio para las zonas normalmenete abiertas
        ZonasMascara=ZonasMascara Xor ZonasNormalAbiertas
    
        //Controlo si es que estan habilitadas las zonas de asalto, emergencia e incendio
        //Verifico las entradas pertinentes y si hay una actuada sale con el estado modificado
        If AsaltoCableadoHabilitado Then
            If ZonaAsaltoCableado=1 Then
                //Marco el disparo como cableado
                DisparoCableado=true
                UltimaZonaDisparo=DISPLAY.DisplayAsalto
                GenerarDisparoAsalto()
                Exit
            EndIf
        EndIf
        If EmergenciaCableadaHabilitada Then
            If ZonaEmergenciaCableada=1 Then
                //Marco el disparo como cableado
                DisparoCableado=true
                UltimaZonaDisparo=DISPLAY.DisplayEmergencia
                GenerarDisparoEmergencia()
                Exit
            EndIf
        EndIf
        If IncendioCableadoHabilitado Then
            If ZonaIncendioCableado=1 Then
                //Marco el disparo como cableado
                DisparoCableado=true
                UltimaZonaDisparo=DISPLAY.DisplayIncendio
                GenerarDisparoIncendio()
                Exit
            EndIf
        EndIf
        If AntidesarmeHabilitado Then
            If IO.ZonaAntidesarmeDebounce=IO.ZonaAntidesarmeActuada Then
                //Marco el disparo como cableado
                DisparoCableado=true
                UltimaZonaDisparo=DISPLAY.DisplayAntidesarme
                GenerarDisparoRobo()
                Exit
            EndIf
        EndIf
        If Not flagEntradaWalaHabilitada Then
            If PrealarmaActivada Then
                If IO.ZonaPrealarma=IO.ZonaPrealarmaActuada Then
                    //Marco el disparo como cableado
                    GenerarDisparoPrealarma()
                    Exit
                EndIf
            EndIf
        EndIf
        
        //Excluyo las zonas autoexcluidas
        ZonasMascara=ZonasMascara And (ZonasAutoExcluidas Xor %11111111)
        
        //Controlo si es zona de 24 horas si esta actuada indico que hay que disparar
        UltimaZonaDisparo=DevolverNumeroZona(ZonasMascara And ZonasCableadas24Horas)
        If (UltimaZonaDisparo <> 99) Then
            DisparoCableado=true
            GenerarDisparoRobo()
            Exit
        EndIf

        'Controlo si es zona cancelable
        If CancelacionActivada Then
            'Aislo la zona de interes
            'Si hay que cancelarla la elimino de la mascara
            ZonasMascara = ZonasMascara Xor (ZonasCableadasCancelables And ZonasMascara)
        EndIf
        
        If Estado=EstadoCentralActivada Then
            //Si llego hasta aca con alguna zona marcada entonces hago un disparo
            If DevolverNumeroZona(ZonasMascara)<>99 Then
    
                DisparoCableado=true
                UltimaZonaDisparo=DevolverNumeroZona(ZonasMascara)
    
                //Controlo si hay que demorar el disparo
                If ((ZonasMascara And ZonasCableadasDemoradas) <> 0) Then
                    Estado=EstadoCentralDemora
                    ZonaConDemora=DevolverNumeroZona(ZonasMascara And ZonasCableadasDemoradas)
                    ZonaConDemoraEsCableada=true
                    DISPLAY.MostrarDigito(ZonaConDemora)
                    DISPLAY.EncenderPunto()
                    'SALTEC.ZonaCableadaDemorada(ZonaConDemora)
                    TimerEntrada=TiempoEntrada
                    
                //Sino hay que generar disparo
                Else
                    GenerarDisparoRobo()
                EndIf
            EndIf
        EndIf
        ZonasCableadasActuadas=ZonasMascara
        
        //Control de salida de deteccion de alimentacion
        SalidaSinAlimentacion = Not DeteccionAlimentacion
    End Sub
    
    //Rutina que cambia el estado de la cancelacion de zonas y muetra el estado actual
    Sub HabilitarODeshabilitarCancelacion()
        //Modifico el estado
        CancelacionActivada=Not CancelacionActivada
        DISPLAY.ApagarDisplay()
        DISPLAY.MostrarDigito(DISPLAY.DisplayComportamientoCancelables)
        If CancelacionActivada Then
            DISPLAY.EncenderPunto()
            WALA.ComandoWala(WALA.ActivarParcialByte, 8)
            'SALTEC.EnviarInfoTeclado(tecZonasCanceladasI, ZonasInalambricasCancelables)
            'SALTEC.EnviarInfoTeclado(tecZonasCanceladasC, ZonasCableadasCancelables)
            ''SALTEC.ZonasCanceladasInalambricas(ZonasInalambricasCancelables)
            ''SALTEC.zonascableadascanceladas(ZonasCableadasCancelables)
        Else
            WALA.ComandoWala(WALA.ActivarByte, 8)
            'SALTEC.EnviarInfoTeclado(tecZonasCanceladasI, 0)
            'SALTEC.EnviarInfoTeclado(tecZonasCanceladasC, 0)
            ''SALTEC.ZonasInalambricasCanceladas(0)
            ''SALTEC.zonascableadascanceladas(0)
            BeepLargo()
        EndIf
        BeepLargo()
        BeepLargo()
        BeepLargo()
    End Sub
    
    //Rutina que cambia el estado de la habilitacion de la prealarma
    Public Sub HabilitarODeshabilitarPrealarma()
        If Not flagEntradaWalaHabilitada Then
            //Modifico el estado
            PrealarmaActivada=Not PrealarmaActivada
            DISPLAY.ApagarDisplay()
            DISPLAY.MostrarDigito(DISPLAY.DisplayP)
            If PrealarmaActivada Then
                DISPLAY.EncenderPunto()
            Else
                BeepLargo()
            EndIf
            BeepLargo()
        EndIf
    End Sub
    
    Public Sub ControlarInalambricos()
        HT6P20.LeerTren

        //Tareas dependientes de los estados de la central
        Select Estado
            //Si la central esta activada
            Case EstadoCentralActivada
                //Llamada a la desactivacion si se pulsa el boton 1
                If HT6P20.RecibiendoCanal1 And Not RecibiendoCanal2 Then
                    DesactivarCentral(HT6P20.ZonaInalambrica)
                    'CancelarDisparo()
                EndIf
                //Llamada a la desactivacion y dispara asalto si se pulsa el boton 2
                If AsaltoPorBoton2Habilitado Then
                    If Not HT6P20.RecibiendoCanal1 And RecibiendoCanal2 Then
                        DesactivarCentral(HT6P20.ZonaInalambrica)
                        //Marco el disparo como inalambrico
                        DisparoCableado=false
                        'CancelarDisparo()
                        GenerarDisparoAsalto()
                    EndIf
                EndIf
            Case EstadoCentralDesactivada
                //llamada a la activacion
                If HT6P20.RecibiendoCanal1 And Not RecibiendoCanal2 Then
                    ActivarCentral(HT6P20.ZonaInalambrica)
                    'CancelarDisparo()
                EndIf
            Case EstadoCentralActivando
                //Llamada a la desactivacion
                If HT6P20.RecibiendoCanal1 And Not RecibiendoCanal2 Then
                    DesactivarCentral(HT6P20.ZonaInalambrica)
                    'CancelarDisparo()
                EndIf
                //Cancelacion de zonas
                If Not HT6P20.RecibiendoCanal1 And RecibiendoCanal2 Then
                    HT6P20.ContBuenos=0
                    HT6P20.RecibiendoCanal2=false
                    HT6P20.TimerRecibiendoCanal2=0
                    HabilitarODeshabilitarCancelacion()
                EndIf
            Case EstadoCentralDemora
                //Llamada a la desactivacion
                If HT6P20.RecibiendoCanal1 And Not RecibiendoCanal2 Then
                    DesactivarCentral(HT6P20.ZonaInalambrica)
                    'CancelarDisparo()
                EndIf
        End Select
        
        //Control de para el disparo de asalto inalambrico
        If Not RecibiendoCanal1 And RecibiendoCanal2 And HT6P20.TimerRecibiendoCanal2 >= 3 Then
            //Marco el disparo como inalambrico
            DisparoCableado=false
            UltimaZonaDisparo=DISPLAY.DisplayAsalto
            GenerarDisparoAsalto()
            Exit
        EndIf

        //Control de para el disparo de emergencia medica inalambrica
        If HT6P20.Recibiendo6P20 And HT6P20.EstaEnLista And HT6P20.EsSensorEmergencia Then
            //Marco el disparo como inalambrico
            DisparoCableado=false
            UltimaZonaDisparo=DISPLAY.DisplayEmergencia
            GenerarDisparoEmergencia()
            Exit
        EndIf

        //Control de para el disparo de incendio inalambrico
        If HT6P20.Recibiendo6P20 And HT6P20.EstaEnLista And HT6P20.EsSensorIncendio Then
            //Marco el disparo como inalambrico
            DisparoCableado=false
            UltimaZonaDisparo=DISPLAY.DisplayIncendio
            GenerarDisparoIncendio()
            Exit
        EndIf
        
        //Controlo si estoy recibiendo zona de robo
        If HT6P20.Recibiendo6P20 And HT6P20.EstaEnLista And Not HT6P20.EsRemoto Then
            //Controlo si es de 24 horas y si es asi genero el disparo
            If ZonasInalambricas24Horas.booleans(HT6P20.ZonaInalambrica - 1) Then
                //Marco el disparo como inalambrico
                DisparoCableado=false
                UltimaZonaDisparo=HT6P20.ZonaInalambrica
                GenerarDisparoRobo()
                Exit
            EndIf
            If Estado=EstadoCentralActivada Then
                'Controlo si es zona cancelable
                If CancelacionActivada Then
                    'Si es cancelable salgo
                    If ZonasInalambricasCancelables.booleans(HT6P20.ZonaInalambrica - 1) Then
                        Exit
                    EndIf
                EndIf
                //Controlo si hay que demorar el disparo
                If ZonasInalambricasDemoradas.booleans(HT6P20.ZonaInalambrica - 1) Then
                    //Marco el disparo como inalambrico
                    DisparoCableado=false
                    UltimaZonaDisparo=HT6P20.ZonaInalambrica
                    Estado=EstadoCentralDemora
                    ZonaConDemora=UltimaZonaDisparo
                    ZonaConDemoraEsCableada=false
                    DISPLAY.MostrarDigito(ZonaConDemora)
                    DISPLAY.ApagarPunto()
                    'SALTEC.ZonaInalambricaDemorada(ZonaConDemora)
                    TimerEntrada=TiempoEntrada
                //Sino hay que generar disparo
                Else
                    //Marco el disparo como inalambrico
                    DisparoCableado=false
                    UltimaZonaDisparo=HT6P20.ZonaInalambrica
                    GenerarDisparoRobo()
                EndIf
            EndIf
        EndIf
    End Sub
    
    Public Sub EntrarEnPanico()
        //pongo a parpader el led
        IO.ParpadearLedEstado250ms()
        //Enciendo las sirenas
        IO.SirenaInterior=IO.SirenaEncendida
        IO.SirenaExterior=IO.SirenaEncendida
        //Si no estaba ya en panico
        If Estado<>EstadoCentralPanico Then
            //Guardo el estado anterior de panico
            EstadoAnterior=Estado
            //y entro en panico
            Estado=EstadoCentralPanico
        EndIf
        WALA.Disparo(false, 9)
    End Sub

    Public Sub SalirDePanico()
        IO.ApagarLedEstado()
        IO.SirenaInterior=IO.SirenaApagada
        IO.SirenaExterior=IO.SirenaApagada
        Estado=EstadoAnterior
        HT6P20.ContBuenos=0
        Delay(1000)
    End Sub

    Public Sub ControlarEstadoInicio()
        Dim activar As Boolean
        
        activar=false

        Select TipoInicioCentral
            //Si arranca activada....
            Case EstadoCentralActivada
                //Marco para activar
                activar=true
            //Si arranca en  el ultimo estado...
            Case 0
                //Verifico el estado anterior
                If UltimoEstadoCentralEEPROM=EstadoCentralActivada Then
                    //Marco para activar
                    activar=true
                EndIf
        End Select
                
        //En cualquiera de las otras condiciones no se activa.....
        
        //Si esta la marca de activar....
        If activar Then
            //Genero la activacion desde el panel...
            DISPLAY.ApagarDisplay()
            CENTRAL.ActivarCentral(0)
            'CENTRAL.CancelarDisparo()
        EndIf
            
    End Sub

    //Inicializacion del modulo
    EstadoAnterior=EstadoCentralDesactivada
    Estado=EstadoCentralDesactivada
    ZonasAutoExcluidas=0
    CancelacionActivada=false
    HuboDisparo=false
    UltimaZonaDisparo=99
    TimerSirenas=0
    TimerPrealarma=0
    TimerBeepsSirenas=0
    ContadorBeepsSirenas=0
    TimerEntrada=0
    TimerSalida=0
    TimerBateriaBaja=0
    TimerMostrarRSSI=0
    TimerAlimentacionDesconectada=0
    TimerAlimentacionConectada=0
    HuboDisparo=false
    DisparoCableado=false
    'TipoDisparo=MEMORIA.mensajeRobo
    ContadorRegistroDisparos=0
    flagDisparoSinGuardar=false
    Clear(RegistroDisparos)
    FlagMostrarAutoexcluidas=true
    FlagEnviarEstadoAlTeclado=true
    FlagAlimentacionNormal=(DeteccionAlimentacion=1)
    FlagHuboDesconexionTemporizada=false
    
    //Inicializo las cola de mensajes    
    PrimerElementoCola=(LongitudCola-1)
    UltimoElementoCola=(LongitudCola-1)    
    PrimerElementoColaGPRS=(LongitudColaGPRS-1)
    UltimoElementoColaGPRS=(LongitudColaGPRS-1)    
    
    //Inicializo los timers de bloqueo
    TimerBloqueoAsalto=0
    TimerBloqueoEmergencia=0
    TimerBloqueoIncendio=0   
    TimerBloqueoBateriaBaja=0 

    //Inicializo los timers de las salidas
    TimerSalida1=0
    TimerSalida2=0
    
    //Inicializo los flags de control de las salidas
    flagSalidaTransistor1Activar=false
    flagSalidaTransistor2Activar=false
    flagSalidaTransistor1Desactivar=false
    flagSalidaTransistor2Desactivar=false
    
    FlagHabilitarODeshabilitarPrealarma=false
    PrealarmaActivada=false
    TimerPanico=0
    
    TimerDelay=0
