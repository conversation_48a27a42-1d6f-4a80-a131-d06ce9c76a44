{
*****************************************************************************
*  Name    : TECLADO.BAS                                                    *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2010 Scanner Alarmas                             *
*          : All Rights Reserved                                            *
*  Date    : 22/01/2010                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

Module TECLADO
Include "ADC.bas"
Include "DISPLAY.bas"
Include "BUZZER.bas"
Include "MATH.bas"
'Include "SYSTEM.bas"

//Declaracion de las variables locales
Dim EntradaTeclado As porta.1

//Declaracion de constantes locales
'Constantes para la lectura de teclado
Const minTecla1=54561+500
Const minTecla2=52577+500
Const minTecla3=48033+500
Const minTecla4=42465+500
Const minTecla5=39681+500
Const minTecla6=35873+500
Const minTecla7=29473+500
Const minTecla8=23329+500
Const minTecla9=17857+500
Const minTecla0=8737+500
Const minTeclaAst=13377+500
Const minTeclaNum=3168+500

Const maxTecla1=56415-500
Const maxTecla2=54560-500
Const maxTecla3=52756-500
Const maxTecla4=48032-500
Const maxTecla5=42464-500
Const maxTecla6=39680-500
Const maxTecla7=35872-500
Const maxTecla8=29472-500
Const maxTecla9=23328-500
Const maxTecla0=13376-500
Const maxTeclaAst=17856-500
Const maxTeclaNum=8736-500

//Declaracion de las variables publicas
Public Dim Teclas As Word
Public Dim Tecla0 As Teclas.0
Public Dim Tecla1 As Teclas.1
Public Dim Tecla2 As Teclas.2
Public Dim Tecla3 As Teclas.3
Public Dim Tecla4 As Teclas.4
Public Dim Tecla5 As Teclas.5
Public Dim Tecla6 As Teclas.6
Public Dim Tecla7 As Teclas.7
Public Dim Tecla8 As Teclas.8
Public Dim Tecla9 As Teclas.9
Public Dim TeclaAst As Teclas.10
Public Dim TeclaNum As Teclas.11
Public Dim TeclaPulsada As Boolean
Public Dim UltimaTeclaPulsada As Byte
Public Dim tmrPulsandoTeclas As Word
'Public Dim CodigoProgramacion(4) As Byte
Public Dim CodigoProgramacionIngresado As Boolean
Public Dim CodigoIngresado(4) As Byte

//Constantes publicas
Public Const ultimaTecla0=0
Public Const ultimaTecla1=1
Public Const ultimaTecla2=2
Public Const ultimaTecla3=3
Public Const ultimaTecla4=4
Public Const ultimaTecla5=5
Public Const ultimaTecla6=6
Public Const ultimaTecla7=7
Public Const ultimaTecla8=8
Public Const ultimaTecla9=9
Public Const ultimaTeclaAst=10
Public Const ultimaTeclaNum=11

Sub SetearUltimaTeclaPulsada()
    UltimaTeclaPulsada=99
    If Teclas.0=1 Then
        UltimaTeclaPulsada=ultimaTecla0
    EndIf
    If Teclas.1=1 Then
        UltimaTeclaPulsada=ultimaTecla1
    EndIf
    If Teclas.2=1 Then
        UltimaTeclaPulsada=ultimaTecla2
    EndIf
    If Teclas.3=1 Then
        UltimaTeclaPulsada=ultimaTecla3
    EndIf
    If Teclas.4=1 Then
        UltimaTeclaPulsada=ultimaTecla4
    EndIf
    If Teclas.5=1 Then
        UltimaTeclaPulsada=ultimaTecla5
    EndIf
    If Teclas.6=1 Then
        UltimaTeclaPulsada=ultimaTecla6
    EndIf
    If Teclas.7=1 Then
        UltimaTeclaPulsada=ultimaTecla7
    EndIf
    If Teclas.8=1 Then
        UltimaTeclaPulsada=ultimaTecla8
    EndIf
    If Teclas.9=1 Then
        UltimaTeclaPulsada=ultimaTecla9
    EndIf
    If Teclas.10=1 Then
        UltimaTeclaPulsada=ultimaTeclaAst
    EndIf
    If Teclas.11=1 Then
        UltimaTeclaPulsada=ultimaTeclaNum
    EndIf
End Sub

Public Sub LeerTeclado()
    Dim
        Medicion As Word
        'Medicion1 As Word
    
    'Asumo todas las teclas sin pulsar
    Teclas=0

'    medicion=0
'    Repeat
'        medicion1=medicion
        'delay(1)
        Medicion=ADC.ReadMedian(ADC.AN1)
'    adcon0=%00000111
'    While adcon0.1=1
'    Wend
'    Medicion.HighByte=ADRESH
'    Medicion.LowByte=ADRESL
'    Until abs(medicion-medicion1)<10
    
    Select Medicion
        Case minTecla1 To maxTecla1
            Tecla1=1
        Case minTecla2 To maxTecla2
            Tecla2=1
        Case minTecla3 To maxTecla3
            Tecla3=1
        Case minTecla4 To maxTecla4
            Tecla4=1
        Case minTecla5 To maxTecla5
            Tecla5=1
        Case minTecla6 To maxTecla6
            Tecla6=1
        Case minTecla7 To maxTecla7
            Tecla7=1
        Case minTecla8 To maxTecla8
            Tecla8=1
        Case minTecla9 To maxTecla9
            Tecla9=1
        Case minTeclaAst To maxTeclaAst
            TeclaAst=1
        Case minTeclaNum To maxTeclaNum
            TeclaNum=1
        Case minTecla0 To maxTecla0
            Tecla0=1
    End Select 

    If Teclas<>0 Then
        TeclaPulsada=true
        tmrPulsandoTeclas=6
        SetearUltimaTeclaPulsada()
    Else
        TeclaPulsada=false
'        If tmrPulsandoTeclas>0 Then
'            Dec(tmrPulsandoTeclas)
'        EndIf
        If tmrPulsandoTeclas=1 Then 
            SetearUltimaTeclaPulsada()
            CodigoProgramacionIngresado=false
            tmrPulsandoTeclas=0
        EndIf
    EndIf
End Sub

Public Sub EsperarSoltarTeclas()
    Repeat
        LeerTeclado()
    Until Not TeclaPulsada
End Sub

Public Sub EsperarPulsarTecla()
    Repeat
        LeerTeclado()
    Until TeclaPulsada
End Sub

Public Sub ControlarCodigos()
    LeerTeclado()
    If TeclaPulsada Then
        Select UltimaTeclaPulsada
            //si se pulso un numero lo cargo en el buffer y hago beep
            Case <=ultimaTecla9
                CodigoIngresado(3)=CodigoIngresado(2)
                CodigoIngresado(2)=CodigoIngresado(1)
                CodigoIngresado(1)=CodigoIngresado(0)
                CodigoIngresado(0)=UltimaTeclaPulsada
                CodigoProgramacionIngresado=CodigoIngresado(0)=CodigoProgramacion(0) And CodigoIngresado(1)=CodigoProgramacion(1) And CodigoIngresado(2)=CodigoProgramacion(2) And CodigoIngresado(3)=CodigoProgramacion(3)
                BUZZER.BeepCorto
            //Si se pulso numeral hago beep
            Case ultimaTeclaNum
                BUZZER.BeepCorto
            //Si se pulso asterisco y no hay un codigo valido hago beep
            Case ultimaTeclaAst
                If Not CodigoProgramacionIngresado Then 
                    BUZZER.BeepCorto
                EndIf
        End Select
        EsperarSoltarTeclas()
    EndIf
End Sub

//Inicializacion del modulo
'ADC.SetConvTime(FOSC_32)
'Input(EntradaTeclado)
'ADC.SetAcqTime(20)
TeclaPulsada=false
tmrPulsandoTeclas=2
CodigoIngresado(3)=99
CodigoIngresado(2)=99
CodigoIngresado(1)=99
CodigoIngresado(0)=99
UltimaTeclaPulsada=99
