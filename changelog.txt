14/3/22		2.2.0		Se arregla loop de apagado
				Se corrige envio fallido por falta de deteccion de ">"
				Se modifica timeout para lectura de sms para uc200t
				Se hacen pruebas con ec21
				Se corrige el flag de no registrado para que asuma registro ni bien levanta el modulo
				Se extiende el timeout de envio de mensaje a 2 minutos, en caso de falla tarda eso
				Se modifica la rutina de recepcion de 6p20 para que ignore el codigo 000000
				En caso de que aparezca una falla en EEPROM, el codigo es ignorado
15/06/23	2.2.1		Se ensaya modulo ec200a-au
				Se modifican las rutinas de arranque y configuracion del modulo
				Se espera ahora a que el modulo entregue CFUN y CPIN antes de enviar configuraciones
				Solo despues se solicita el IMEI, la solicitud fallaba por ruido en la fuente al arranque
				Se corre la Config de CLIP junto a la lectura del IMEI
				Se compila para 46k22, 46k20 y 4620
30/10/23	2.2.2		Se ensaya modulo ec21-au con actualizaion de firmware
				Se modifica rutina de envio de gprs, si devuelve error a at+qisend, al igual que el timeout, genera error
				Se compila para 46k22, 46k20 y 4620
23/07/24	2.2.3		Se modifica la inicializacion del modulo de gsm para compatibilizar con el UC200A-GL
				Ahora se espera el rdy y un tiempo prudencial o cfun y cpin
				Este modulo no genera los ultimos dos mensajes.
				Se compila para 46k22, 46k20 y 4620
19/8/2024	2.2.4		Se Agrega la cancelacion parcial sincronizada con la wala
				Si wala pasa a parcial, GSM tambien y viceversa
				Se pone en "" el texto de OrigenDelComando si la accion viene de un remoto
22/02/2025	2.2.5		Se agrega que si la activacion es parcial, entonces el mensaje a wala en ActivarCentral, es ActivarParcial
				Intercambiadas las asignaciones de pines de las sirenas:
				  * SirenaExterior reasignada a PORTE.2 (pin 10)
				  * SirenaInterior reasignada a PORTE.1 (pin 9)
1/4/2025	2.2.6		Se mueve el limpiado de la bandera de error del procesador flagErrorRecibido, a antes de intentar enviar el mensaje
				Esta modificacion se hace porque los modulos UC200A-GL generan un ERROR antes de suministrarles el cuerpo del mensaje
				Antes se limpiaba el flag inmediatamente despues de enviar, pero esto hacia que no se lea el ERROR y terminaba esperando el timeout
20/4/2025	2.2.7		Se agrega config de velocidad a 115200 y sin control de flujo al inicio
				Se agrega borrado de todos los mensajes en la configuracion del modulo con AT+CMGD=1,4
				Se modifica la rutina de borrado para que use AT+CMGD=1,4
				Se agrega la deteccion de CPINNOTREADY que manda el modulo a apagar
				Se quita la deteccion de +CMGL (ex procesamiento de mensajes en memoria)
				Se invierten las condiciones de si hay que chequear el RSSI, primero si tiene que parar y luego si tiene que arrancar
				Se baja de 120s a 30s el timer de resgistro en la red
				Se modifica la deteccion de ">" (solicitud de cuerpo de mensaje) solo se controla un caracter antes era Left(MiMensaje, 2)="> " ahora MiMensaje(0)=">"

