{
*****************************************************************************
*  Name    : wala.BAS                                                       *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2020 Spectrum Alarmas                            *
*          : All Rights Reserved                                            *
*  Date    : 28/1/2020                                                      *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

// the module name...
Module WALA

// import helper modules...
Include "IO.bas"
Include "EEPROM.bas"
Include "DISPLAY.bas"

//Declaracion de variables publicas
Public Dim
    HighByte As Byte,                   // Habilitacion de las interrupciones de alta prioridad
    TimerActivada As Word,
    TimerDesactivada As Word,
    ContadorPulsosLed As Byte,
    EstadoActivacionWala As Byte,
    ContadorTiempo As Byte,             // Habilitacion de las interrupciones de alta prioridad
    Salida As Bit,
    ContadorEnvios As Byte,             // Habilitacion de las interrupciones de alta prioridad
    EstadoWala As Byte

//Declaracion de variables privadas
Dim
    TMR2IE As PIE1.1,                   // TMR2 interrupt enable
    EsRemoto As Boolean,
    MidHighByte As Byte,                // Habilitacion de las interrupciones de alta prioridad
    MidLowByte As Byte,                 // Habilitacion de las interrupciones de alta prioridad
    LowByte As Byte,                    // Habilitacion de las interrupciones de alta prioridad
    ContadorBit As Byte,                // Habilitacion de las interrupciones de alta prioridad
    ContadorByte As Byte,               // Habilitacion de las interrupciones de alta prioridad
    temp As Byte


Public Const
    ActivarByte = %00001000,
    DesactivarByte = %00000100,
    ActivarParcialByte = %00000010,
    SosByte = %0000001,
    DispararByte = %00011011

// private constants and variables�
Public Const
    MinBit = 49,
    MaxBit = 199,
    Activada = 1,
    Desactivada = 0,
    ActivadaParcial = 2,
    Indeterminado = 3
    
Public Sub ComandoWala(comando As Byte, duracion As Byte)
    TimerActivada=0
    TimerDesactivada=0
    EstadoActivacionWala=Indeterminado
    EsRemoto=true
    MidHighByte=%00110100
    'MidLowByte=%00001001
    LowByte=comando
    ContadorEnvios=duracion
    TMR2IE=1
End Sub

Public Sub Disparo(cableado As Boolean, zona As Byte)
    'TimerActivada=0
    'TimerDesactivada=0
    'EstadoActivacionWala=Indeterminado
    EsRemoto=false
    If cableado Then
       MidLowByte=%10101010
    Else
       MidLowByte=%01010100
    EndIf
    MidHighByte=zona
    LowByte=DispararByte
    ContadorEnvios=15
    TMR2IE=1
End Sub
    
Sub SetSalidaWala()
    Salida=1
    If temp.7 = 1 Then
        ContadorTiempo=3
    Else
        ContadorTiempo=1
    EndIf
End Sub

Sub ResetSalidaWala()
    Salida=0
    If temp.7 = 1 Then
        ContadorTiempo=1
    Else
        ContadorTiempo=3
    EndIf
End Sub
    
Public Sub ProcesarWala()
    If ContadorEnvios>0 Then
        Output(SalidaWala)
        Select EstadoWala
            //Espera para comenzar
            Case 0
                Select ContadorByte
                    Case 0
                        temp=HighByte
                    Case 1
                        temp=MidHighByte
                    Case 2
                        temp=MidLowByte
                    Case 3
                        temp=LowByte
                End Select
                ContadorBit=0
                Salida=0
                Inc(EstadoWala)
                SetSalidaWala()
            //Espero final de high half bit
            Case 1
                If ContadorTiempo=0 Then
                    Inc(EstadoWala)
                    ResetSalidaWala()
                EndIf
            //Espero final de high half bit
            Case 2
                If ContadorTiempo=0 Then
                    Inc(EstadoWala)
                    Inc(ContadorBit)
                    SetSalidaWala()
                EndIf
            Case 3
                temp=temp<<1
                EstadoWala=1
                SetSalidaWala()
                
                If ContadorBit=8 Then
                    EstadoWala=0
                    ContadorBit=0
                    Inc(ContadorByte)
                    If ContadorByte=2 And EsRemoto Then
                        Inc(ContadorByte)
                    EndIf
                    If ContadorByte=4 Then
                        ContadorByte=0
                        EstadoWala=4
                    EndIf
                EndIf
            Case 4
                Salida=1
                ContadorTiempo=1
                Inc(EstadoWala)
            Case 5
                If ContadorTiempo=0 Then
                    Salida=0
                    ContadorTiempo=27
                    Inc(EstadoWala)
                EndIf
            Case 6
                If ContadorTiempo=0 Then
                    EstadoWala=0
                    Dec(ContadorEnvios)
                EndIf
        End Select
    Else
        Input(SalidaWala)
        TMR2IE=0
'        If LowByte=DispararByte Then
'            ComandoWala(SosByte, 50)
'        EndIf
    EndIf
End Sub

Sub EsperarSoltarBotones()
    While tmrBtnProg>0 Or tmrBtnSel>0
        ProcesarWala()
    Wend
    While tmrBtnSel<10
        ProcesarWala()
    Wend
End Sub

Public Sub TransmitirCodigos()
    Dim zona As Byte

    DISPLAY.MostrarDigito(DisplayRemoto)
    'HighByte=EE.ReadByte($3fa)
    EsperarSoltarBotones()
    'Envio remoto
    ComandoWala(WALA.ActivarByte, 8)

    DISPLAY.MostrarDigito(1)
    EsperarSoltarBotones()
    
    For zona=1 To 8
        'Envio zonas cableadas
        WALA.Disparo(true, zona)
        DISPLAY.MostrarDigito(zona+1)
        If zona=8 Then
            DISPLAY.MostrarDigito(1)
        EndIf
        EsperarSoltarBotones()
    Next
    DISPLAY.MostrarDigito(1)
    For zona=1 To 8
        'Envio zonas cableadas
        WALA.Disparo(false, zona)
        DISPLAY.MostrarDigito(zona+1)
        'If zona<8 Then
            EsperarSoltarBotones()
        'EndIf
    Next
End Sub

ContadorBit=0
ContadorByte=0
ContadorEnvios=0
ContadorTiempo=0
HighByte=%00010010
MidHighByte=%00110100
MidLowByte=%00110100
LowByte=%00010110
Salida=0
EstadoWala=0
TimerDesactivada=0
TimerActivada=0
EstadoActivacionWala=Indeterminado
ContadorPulsosLed=0
