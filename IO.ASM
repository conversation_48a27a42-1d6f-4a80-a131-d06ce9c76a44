; Swordfish Structured BASIC Compiler for PICmicros.
; Version 1.1.5.1 Copyright Mecanique 2007
;
; D:\Sync\Proyectos Swordfish\Spectrum GSM 2015 - 18f46k22\IO.BAS 20/2/2025
;
 PROCESSOR PIC18F452
 RADIX DEC
 ERRORLEVEL 2
 EXPAND ON
; SPECIAL FUNCTION REGISTERS...
LATA             EQU 0X0F89
LATB             EQU 0X0F8A
LATC             EQU 0X0F8B
LATD             EQU 0X0F8C
LATE             EQU 0X0F8D
DDRA             EQU 0X0F92
TRISA            EQU 0X0F92
DDRB             EQU 0X0F93
TRISB            EQU 0X0F93
DDRC             EQU 0X0F94
TRISC            EQU 0X0F94
DDRD             EQU 0X0F95
TRISD            EQU 0X0F95
DDRE             EQU 0X0F96
TRISE            EQU 0X0F96
PIE1             EQU 0X0F9D
PIR1             EQU 0X0F9E
IPR1             EQU 0X0F9F
PIE2             EQU 0X0FA0
PIR2             EQU 0X0FA1
IPR2             EQU 0X0FA2
EECON1           EQU 0X0FA6
EECON2           EQU 0X0FA7
EEDATA           EQU 0X0FA8
EEADR            EQU 0X0FA9
RCSTA            EQU 0X0FAB
TXSTA            EQU 0X0FAC
TXREG            EQU 0X0FAD
RCREG            EQU 0X0FAE
SPBRG            EQU 0X0FAF
T3CON            EQU 0X0FB1
TMR3L            EQU 0X0FB2
TMR3LH           EQU 0X0FB3
TMR3H            EQU 0X0FB3
CCP2CON          EQU 0X0FBA
CCPR2            EQU 0X0FBB
CCPR2L           EQU 0X0FBB
CCPR2LH          EQU 0X0FBC
CCPR2H           EQU 0X0FBC
CCP1CON          EQU 0X0FBD
CCPR1            EQU 0X0FBE
CCPR1L           EQU 0X0FBE
CCPR1LH          EQU 0X0FBF
CCPR1H           EQU 0X0FBF
ADCON1           EQU 0X0FC1
ADCON0           EQU 0X0FC2
ADRES            EQU 0X0FC3
ADRESL           EQU 0X0FC3
ADRESLH          EQU 0X0FC4
ADRESH           EQU 0X0FC4
SSPCON2          EQU 0X0FC5
SSPCON1          EQU 0X0FC6
SSPSTAT          EQU 0X0FC7
SSPADD           EQU 0X0FC8
SSPBUF           EQU 0X0FC9
T2CON            EQU 0X0FCA
PR2              EQU 0X0FCB
TMR2             EQU 0X0FCC
T1CON            EQU 0X0FCD
TMR1L            EQU 0X0FCE
TMR1LH           EQU 0X0FCF
TMR1H            EQU 0X0FCF
RCON             EQU 0X0FD0
WDTCON           EQU 0X0FD1
LVDCON           EQU 0X0FD2
OSCCON           EQU 0X0FD3
T0CON            EQU 0X0FD5
TMR0L            EQU 0X0FD6
TMR0LH           EQU 0X0FD7
TMR0H            EQU 0X0FD7
STATUS           EQU 0X0FD8
FSR2L            EQU 0X0FD9
FSR2LH           EQU 0X0FDA
FSR2H            EQU 0X0FDA
PLUSW2           EQU 0X0FDB
PREINC2          EQU 0X0FDC
POSTDEC2         EQU 0X0FDD
POSTINC2         EQU 0X0FDE
INDF2            EQU 0X0FDF
BSR              EQU 0X0FE0
FSR1L            EQU 0X0FE1
FSR1LH           EQU 0X0FE2
FSR1H            EQU 0X0FE2
PLUSW1           EQU 0X0FE3
PREINC1          EQU 0X0FE4
POSTDEC1         EQU 0X0FE5
POSTINC1         EQU 0X0FE6
INDF1            EQU 0X0FE7
WREG             EQU 0X0FE8
FSR0L            EQU 0X0FE9
FSR0LH           EQU 0X0FEA
FSR0H            EQU 0X0FEA
PLUSW0           EQU 0X0FEB
PREINC0          EQU 0X0FEC
POSTDEC0         EQU 0X0FED
POSTINC0         EQU 0X0FEE
INDF0            EQU 0X0FEF
INTCON3          EQU 0X0FF0
INTCON2          EQU 0X0FF1
INTCON           EQU 0X0FF2
INTCON1          EQU 0X0FF2
PROD             EQU 0X0FF3
PRODL            EQU 0X0FF3
PRODLH           EQU 0X0FF4
PRODH            EQU 0X0FF4
TABLAT           EQU 0X0FF5
TBLPTR           EQU 0X0FF6
TBLPTRL          EQU 0X0FF6
TBLPTRLH         EQU 0X0FF7
TBLPTRH          EQU 0X0FF7
TBLPTRU          EQU 0X0FF8
PC               EQU 0X0FF9
PCL              EQU 0X0FF9
PCLATH           EQU 0X0FFA
PCLATU           EQU 0X0FFB
STKPTR           EQU 0X0FFC
TOS              EQU 0X0FFD
TOSL             EQU 0X0FFD
TOSLH            EQU 0X0FFE
TOSH             EQU 0X0FFE
TOSU             EQU 0X0FFF
; SYSTEM PORTS...
PORTE            EQU 0X0F84
PORTD            EQU 0X0F83
PORTC            EQU 0X0F82
PORTB            EQU 0X0F81
PORTA            EQU 0X0F80
; PORTA
RA0 = 0
RA1 = 1
RA2 = 2
RA3 = 3
RA4 = 4
RA5 = 5
RA6 = 6
AN0 = 0
AN1 = 1
AN2 = 2
AN3 = 3
AN4 = 5
OSC2 = 6
VREFM = 2
VREFP = 3
T0CKI = 4
SS = 5
CLKO = 6
LVDIN = 5
; PORTB
RB0 = 0
RB1 = 1
RB2 = 2
RB3 = 3
RB4 = 4
RB5 = 5
RB6 = 6
RB7 = 7
INT0 = 0
INT1 = 1
INT2 = 2
CCP2_PORTB = 3
PGM = 5
PGC = 6
PGD = 7
CCP2A = 3
; PORTC
RC0 = 0
RC1 = 1
RC2 = 2
RC3 = 3
RC4 = 4
RC5 = 5
RC6 = 6
RC7 = 7
T1OSO = 0
T1OSI = 1
SCK = 3
SDI = 4
SDO = 5
TX = 6
RX = 7
T1CKI = 0
CCP2_PORTC = 1
CCP1 = 2
SCL = 3
SDA = 4
CK = 6
; DT = 7
; PORTD
RD0 = 0
RD1 = 1
RD2 = 2
RD3 = 3
RD4 = 4
RD5 = 5
RD6 = 6
RD7 = 7
PSP0 = 0
PSP1 = 1
PSP2 = 2
PSP3 = 3
PSP4 = 4
PSP5 = 5
PSP6 = 6
PSP7 = 7
; PORTE
RE0 = 0
RE1 = 1
RE2 = 2
AN5 = 0
AN6 = 1
AN7 = 2
RD = 0
WR = 1
CS = 2
; LATA
LATA0 = 0
LATA1 = 1
LATA2 = 2
LATA3 = 3
LATA4 = 4
LATA5 = 5
LATA6 = 6
; LATB
LATB0 = 0
LATB1 = 1
LATB2 = 2
LATB3 = 3
LATB4 = 4
LATB5 = 5
LATB6 = 6
LATB7 = 7
; LATC
LATC0 = 0
LATC1 = 1
LATC2 = 2
LATC3 = 3
LATC4 = 4
LATC5 = 5
LATC6 = 6
LATC7 = 7
; LATD
LATD0 = 0
LATD1 = 1
LATD2 = 2
LATD3 = 3
LATD4 = 4
LATD5 = 5
LATD6 = 6
LATD7 = 7
; LATE
LATE0 = 0
LATE1 = 1
LATE2 = 2
; DDRA
TRISA0 = 0
TRISA1 = 1
TRISA2 = 2
TRISA3 = 3
TRISA4 = 4
TRISA5 = 5
TRISA6 = 6
RA0 = 0
RA1 = 1
RA2 = 2
RA3 = 3
RA4 = 4
RA5 = 5
RA6 = 6
; TRISA
TRISA0 = 0
TRISA1 = 1
TRISA2 = 2
TRISA3 = 3
TRISA4 = 4
TRISA5 = 5
TRISA6 = 6
RA0 = 0
RA1 = 1
RA2 = 2
RA3 = 3
RA4 = 4
RA5 = 5
RA6 = 6
; DDRB
TRISB0 = 0
TRISB1 = 1
TRISB2 = 2
TRISB3 = 3
TRISB4 = 4
TRISB5 = 5
TRISB6 = 6
TRISB7 = 7
RB0 = 0
RB1 = 1
RB2 = 2
RB3 = 3
RB4 = 4
RB5 = 5
RB6 = 6
RB7 = 7
CCP2_DDRB = 3
; TRISB
TRISB0 = 0
TRISB1 = 1
TRISB2 = 2
TRISB3 = 3
TRISB4 = 4
TRISB5 = 5
TRISB6 = 6
TRISB7 = 7
RB0 = 0
RB1 = 1
RB2 = 2
RB3 = 3
RB4 = 4
RB5 = 5
RB6 = 6
RB7 = 7
CCP2_TRISB = 3
; DDRC
TRISC0 = 0
TRISC1 = 1
TRISC2 = 2
TRISC3 = 3
TRISC4 = 4
TRISC5 = 5
TRISC6 = 6
TRISC7 = 7
RC0 = 0
RC1 = 1
RC2 = 2
RC3 = 3
RC4 = 4
RC5 = 5
RC6 = 6
RC7 = 7
CCP2_DDRC = 1
; TRISC
TRISC0 = 0
TRISC1 = 1
TRISC2 = 2
TRISC3 = 3
TRISC4 = 4
TRISC5 = 5
TRISC6 = 6
TRISC7 = 7
RC0 = 0
RC1 = 1
RC2 = 2
RC3 = 3
RC4 = 4
RC5 = 5
RC6 = 6
RC7 = 7
CCP2_TRISC = 1
; DDRD
TRISD0 = 0
TRISD1 = 1
TRISD2 = 2
TRISD3 = 3
TRISD4 = 4
TRISD5 = 5
TRISD6 = 6
TRISD7 = 7
RD0 = 0
RD1 = 1
RD2 = 2
RD3 = 3
RD4 = 4
RD5 = 5
RD6 = 6
RD7 = 7
; TRISD
TRISD0 = 0
TRISD1 = 1
TRISD2 = 2
TRISD3 = 3
TRISD4 = 4
TRISD5 = 5
TRISD6 = 6
TRISD7 = 7
RD0 = 0
RD1 = 1
RD2 = 2
RD3 = 3
RD4 = 4
RD5 = 5
RD6 = 6
RD7 = 7
; DDRE
TRISE0 = 0
TRISE1 = 1
TRISE2 = 2
PSPMODE = 4
IBOV = 5
OBF = 6
IBF = 7
RE0 = 0
RE1 = 1
RE2 = 2
; TRISE
TRISE0 = 0
TRISE1 = 1
TRISE2 = 2
PSPMODE = 4
IBOV = 5
OBF = 6
IBF = 7
RE0 = 0
RE1 = 1
RE2 = 2
; PIE1
TMR1IE = 0
TMR2IE = 1
CCP1IE = 2
SSPIE = 3
TXIE = 4
RCIE = 5
ADIE = 6
PSPIE = 7
; PIR1
TMR1IF = 0
TMR2IF = 1
CCP1IF = 2
SSPIF = 3
TXIF = 4
RCIF = 5
ADIF = 6
PSPIF = 7
; IPR1
TMR1IP = 0
TMR2IP = 1
CCP1IP = 2
SSPIP = 3
TXIP = 4
RCIP = 5
ADIP = 6
PSPIP = 7
; PIE2
CCP2IE = 0
TMR3IE = 1
LVDIE = 2
BCLIE = 3
EEIE = 4
; PIR2
CCP2IF = 0
TMR3IF = 1
LVDIF = 2
BCLIF = 3
EEIF = 4
; IPR2
CCP2IP = 0
TMR3IP = 1
LVDIP = 2
BCLIP = 3
EEIP = 4
; EECON1
RD = 0
WR = 1
WREN = 2
WRERR = 3
FREE = 4
CFGS = 6
EEPGD = 7
; RCSTA
RX9D = 0
OERR = 1
FERR = 2
ADDEN = 3
CREN = 4
SREN = 5
RX9 = 6
SPEN = 7
RCD8 = 0
RC8_9 = 6
NOT_RC8 = 6
RC9 = 6
; TXSTA
TX9D = 0
TRMT = 1
BRGH = 2
SYNC = 4
TXEN = 5
TX9 = 6
CSRC = 7
TXD8 = 0
TX8_9 = 6
NOT_TX8 = 6
; T3CON
TMR3ON = 0
TMR3CS = 1
NOT_T3SYNC = 2
T3CCP1 = 3
T3CCP2 = 6
RD16 = 7
T3SYNC = 2
T3CKPS0 = 4
T3CKPS1 = 5
T3INSYNC = 2
; CCP2CON
CCP2M0 = 0
CCP2M1 = 1
CCP2M2 = 2
CCP2M3 = 3
DC2B0 = 4
DC2B1 = 5
CCP2Y = 4
CCP2X = 5
DCCPX = 5
; CCP1CON
CCP1M0 = 0
CCP1M1 = 1
CCP1M2 = 2
CCP1M3 = 3
DC1B0 = 4
DC1B1 = 5
CCP1Y = 4
CCP1X = 5
; ADCON1
ADCS2 = 6
ADFM = 7
PCFG0 = 0
PCFG1 = 1
PCFG2 = 2
PCFG3 = 3
; ADCON0
ADON = 0
GO_NOT_DONE = 2
GO = 2
CHS0 = 3
CHS1 = 4
CHS2 = 5
ADCS0 = 6
ADCS1 = 7
NOT_DONE = 2
DONE = 2
GO_DONE = 2
; SSPCON2
SEN = 0
RSEN = 1
PEN = 2
RCEN = 3
ACKEN = 4
ACKDT = 5
ACKSTAT = 6
GCEN = 7
; SSPCON1
CKP = 4
SSPEN = 5
SSPOV = 6
WCOL = 7
SSPM0 = 0
SSPM1 = 1
SSPM2 = 2
SSPM3 = 3
; SSPSTAT
BF = 0
UA = 1
R_NOT_W = 2
S = 3
P = 4
D_NOT_A = 5
CKE = 6
SMP = 7
I2C_READ = 2
I2C_START = 3
I2C_STOP = 4
I2C_DATA = 5
R = 2
D = 5
READ_WRITE = 2
DATA_ADDRESS = 5
NOT_WRITE = 2
NOT_ADDRESS = 5
NOT_W = 2
NOT_A = 5
R_W = 2
D_A = 5
I2C_DAT = 5
; T2CON
TMR2ON = 2
T2CKPS0 = 0
T2CKPS1 = 1
TOUTPS0 = 3
TOUTPS1 = 4
TOUTPS2 = 5
TOUTPS3 = 6
; T1CON
TMR1ON = 0
TMR1CS = 1
NOT_T1SYNC = 2
T1OSCEN = 3
RD16 = 7
T1SYNC = 2
T1CKPS0 = 4
T1CKPS1 = 5
T1INSYNC = 2
; RCON
NOT_BOR = 0
NOT_POR = 1
NOT_PD = 2
NOT_TO = 3
NOT_RI = 4
IPEN = 7
BOR = 0
POR = 1
PD = 2
TO = 3
RI = 4
NOT_IPEN = 7
; WDTCON
SWDTEN = 0
SWDTE = 0
; LVDCON
LVDEN = 4
IRVST = 5
LVDL0 = 0
LVDL1 = 1
LVDL2 = 2
LVDL3 = 3
; OSCCON
SCS = 0
; T0CON
PSA = 3
T0SE = 4
T0CS = 5
T08BIT = 6
TMR0ON = 7
T0PS0 = 0
T0PS1 = 1
T0PS2 = 2
; STATUS
C = 0
DC = 1
Z = 2
OV = 3
N = 4
; INTCON3
INT1IF = 0
INT2IF = 1
INT1IE = 3
INT2IE = 4
INT1IP = 6
INT2IP = 7
INT1F = 0
INT2F = 1
INT1E = 3
INT2E = 4
INT1P = 6
INT2P = 7
; INTCON2
RBIP = 0
TMR0IP = 2
INTEDG2 = 4
INTEDG1 = 5
INTEDG0 = 6
NOT_RBPU = 7
T0IP = 2
RBPU = 7
; INTCON
RBIF = 0
INT0IF = 1
TMR0IF = 2
RBIE = 3
INT0IE = 4
TMR0IE = 5
PEIE_GIEL = 6
GIE_GIEH = 7
INT0F = 1
T0IF = 2
INT0E = 4
T0IE = 5
PEIE = 6
GIE = 7
GIEL = 6
GIEH = 7
; INTCON1
RBIF = 0
INT0IF = 1
TMR0IF = 2
RBIE = 3
INT0IE = 4
TMR0IE = 5
PEIE_GIEL = 6
GIE_GIEH = 7
INT0F = 1
T0IF = 2
INT0E = 4
T0IE = 5
PEIE = 6
GIE = 7
GIEL = 6
GIEH = 7
; STKPTR
STKUNF = 6
STKFUL = 7
STKPTR0 = 0
STKPTR1 = 1
STKPTR2 = 2
STKPTR3 = 3
STKPTR4 = 4
STKOVF = 7
SP0 = 0
SP1 = 1
SP2 = 2
SP3 = 3
SP4 = 4
 #DEFINE W 0
 #DEFINE F 1
 #DEFINE A 0
 #DEFINE _CLOCK 20000000
 #DEFINE CLRW CLRF 4072,0
 #DEFINE NEGW NEGF 4072,0
 #DEFINE SKPC BTFSS 4056,0,0
 #DEFINE SKPNC BTFSC 4056,0,0
 #DEFINE CLRC BCF 4056,0,0
 #DEFINE SETC BSF 4056,0,0
 #DEFINE SKPZ BTFSS 4056,2,0
 #DEFINE SKPNZ BTFSC 4056,2,0
 #DEFINE CLRZ BCF 4056,2,0
 #DEFINE SETZ BSF 4056,2,0
SB_SV0 EQU 0
SB_SV0H EQU 1
SB_SV0HH EQU 2
SB_SV0HHH EQU 3
SB_SV1 EQU 4
SB_SV1H EQU 5
SB_SV2 EQU 6
SB_SV2H EQU 7
SB_SV2HH EQU 8
SB_SV2HHH EQU 9
SB_SV3 EQU 10
SB_SV3H EQU 11
SB_SV3HH EQU 12
SB_SV3HHH EQU 13
SB_SV4 EQU 14
SB_SV4H EQU 15
SB_SV5 EQU 16
SB_SV5H EQU 17
SB_SV5HH EQU 18
SB_SV5HHH EQU 19
SB_SV6 EQU 20
SB_SV6H EQU 21
SB_SV6HH EQU 22
SB_SV6HHH EQU 23
SB_SV7 EQU 24
A0 EQU 4073
A0H EQU 4074
A1 EQU 4065
A1H EQU 4066
A2 EQU 4057
A2H EQU 4058
TBPTR EQU 4086
TBPTRH EQU 4087
M0_U01 EQU 25
M1_U01 EQU 26
M2_U08 EQU 27
M3_U16 EQU 28
M3_U16H EQU 29
M5_U16 EQU 30
M5_U16H EQU 31
M7_U16 EQU 32
M7_U16H EQU 33
M9_U16 EQU 34
M9_U16H EQU 35
M11_U16 EQU 36
M11_U16H EQU 37
        ORG 0X00
        GOTO SBCDSTD
        ORG 0X08
SBCDSTD
?I000000_F000_000248_M000000 ; L#MK FLAGPARPADEARLEDESTADO500MS=FALSE
        BCF M0_U01,4,0
?I000001_F000_000249_M000000 ; L#MK FLAGPARPADEARLEDESTADO250MS=FALSE
        BCF M0_U01,5,0
?I000002_F000_000250_M000000 ; L#MK FLAGPARPADEARLEDGSM500MS=FALSE
        BCF M0_U01,6,0
?I000003_F000_000251_M000000 ; L#MK FLAGPARPADEARLEDGSM250MS=FALSE
        BCF M0_U01,7,0
?I000004_F000_000252_M000000 ; L#MK FLAGPARPADEARLEDGSM100MS=FALSE
        BCF M1_U01,0,0
?I000005_F000_000253_M000000 ; L#MK FLAGPARPADEARLEDGSM50MS=FALSE
        BCF M1_U01,1,0
?I000006_F000_000255_M000000 ; L#MK BTNPROGPULSADO=FALSE
        BCF M0_U01,0,0
?I000007_F000_000256_M000000 ; L#MK BTNSELPULSADO=FALSE
        BCF M0_U01,1,0
?I000008_F000_000257_M000000 ; L#MK BTNPROGSOLTADO=FALSE
        BCF M0_U01,2,0
?I000009_F000_000258_M000000 ; L#MK BTNSELSOLTADO=FALSE
        BCF M0_U01,3,0
?I000010_F000_000260_M000000 ; L#MK TMRBTNPROG=0
        CLRF M3_U16H,0
        CLRF M3_U16,0
?I000011_F000_000261_M000000 ; L#MK TMRBTNSEL=0
        CLRF M5_U16H,0
        CLRF M5_U16,0
?I000012_F000_000262_M000000 ; L#MK TIEMPOBTNPROGPULSADO=0
        CLRF M7_U16H,0
        CLRF M7_U16,0
?I000013_F000_000263_M000000 ; L#MK TIEMPOBTNSELPULSADO=0
        CLRF M9_U16H,0
        CLRF M9_U16,0
?I000014_F000_000266_M000000 ; L#MK LOW(LEDESTADO)
        BCF LATA,2,0
        BCF TRISA,2,0
?I000015_F000_000267_M000000 ; L#MK LOW(LEDGSM)
        BCF LATD,6,0
        BCF TRISD,6,0
?I000016_F000_000278_M000000 ; L#MK HIGH(SEGMENTOA)
        BSF LATC,0,0
        BCF TRISC,0,0
?I000017_F000_000279_M000000 ; L#MK HIGH(SEGMENTOB)
        BSF LATC,1,0
        BCF TRISC,1,0
?I000018_F000_000280_M000000 ; L#MK HIGH(SEGMENTOC)
        BSF LATC,5,0
        BCF TRISC,5,0
?I000019_F000_000281_M000000 ; L#MK HIGH(SEGMENTOD)
        BSF LATD,1,0
        BCF TRISD,1,0
?I000020_F000_000282_M000000 ; L#MK HIGH(SEGMENTOE)
        BSF LATD,0,0
        BCF TRISD,0,0
?I000021_F000_000283_M000000 ; L#MK HIGH(SEGMENTOF)
        BSF LATD,3,0
        BCF TRISD,3,0
?I000022_F000_000284_M000000 ; L#MK HIGH(SEGMENTOG)
        BSF LATD,2,0
        BCF TRISD,2,0
?I000023_F000_000285_M000000 ; L#MK HIGH(PUNTO)
        BSF LATC,3,0
        BCF TRISC,3,0
?I000024_F000_000288_M000000 ; L#MK LOW(SALIDABUZZER)
        BCF LATA,4,0
        BCF TRISA,4,0
?I000025_F000_000290_M000000 ; L#MK LOW(SIRENAINTERIOR)
        BCF LATE,2,0
        BCF TRISE,2,0
?I000026_F000_000291_M000000 ; L#MK LOW(SIRENAEXTERIOR)
        BCF LATE,1,0
        BCF TRISE,1,0
?I000027_F000_000293_M000000 ; L#MK LOW(SALIDATRANSISTOR1)
        BCF LATE,0,0
        BCF TRISE,0,0
?I000028_F000_000294_M000000 ; L#MK LOW(SALIDATRANSISTOR2)
        BCF LATA,5,0
        BCF TRISA,5,0
?I000029_F000_000296_M000000 ; L#MK LOW(SALIDASINALIMENTACION)
        BCF LATA,3,0
        BCF TRISA,3,0
?I000030_F000_000297_M000000 ; L#MK LOW(POWERKEY)
        BCF LATD,7,0
        BCF TRISD,7,0
?I000031_F000_000299_M000000 ; L#MK INPUT(ZONAANTIDESARME)
        BSF TRISC,4,0
?I000032_F000_000300_M000000 ; L#MK INPUT(ZONAPREALARMA)
        BSF TRISE,3,0
?I000033_F000_000302_M000000 ; L#MK INTCON2.7=0
        BCF INTCON2,7,0
?I000034_F000_000305_M000000 ; L#MK TICK=65450
        SETF M11_U16H,0
        MOVLW 170
        MOVWF M11_U16,0
?I000035_F000_000307_M000000 ; L#MK ZONASCABLEADASDEBOUNCE=ZONASCABLEADAS
        MOVFF PORTB,M2_U08
 CONFIG OSC = HS
 CONFIG OSCS = OFF
 CONFIG PWRT = ON
 CONFIG BOR = ON
 CONFIG BORV = 20
 CONFIG WDT = OFF
 CONFIG WDTPS = 128
 CONFIG STVR = ON
 CONFIG LVP = OFF
 CONFIG DEBUG = OFF
SB#ASMEND
        END
