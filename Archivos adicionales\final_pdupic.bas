'****************************************************************
'*  Name    : pdupic.BAS   < TESTED on Pic 16fF628 >            *
'*  Author  : (c)2004 <PERSON><PERSON><PERSON>                    *
'*  Notice  : Converting sms between txt and pdu format is      *
'*          : actually a matter of "septet" to octet convertion * 
'*          : i.e: 7-bit data to 8-bit data and the oposite!    * 
'*  Date    : 18/3/2004                                         *
'*  Version : 1.0c Final                                        *
'****************************************************************
include "modedefs.bas"

DEFINE OSC 20       '20Mhz crystal
DEFINE LCD_LINES 2

CMCON = %00000111   'PortA digital
;OPTION_REG.7 = 1   ' Disable PORTB pull-ups
clear
char    VAR BYTE [8]'text data
pdu     var byte [8]'pdu data
tmp     var byte [8]'temporary array
ch      var byte [8]'temporary array
b0      var byte
x       VAR BYTE


;---------your code from here...------------------
;fill-in the array with test data
start:
FOR X=0 TO 7
LOOKUP X,["hellohel"],B0 '8bytes textsms that will be converted in Pdu bytes
CHAR(X)=B0
NEXT X
FOR X=0 TO 7
LOOKup2 X,[232,50,155,253,70,151,217],B0 'pdu data:E8329Bfd4697D9...
pdu(X)=B0
NEXT X

;output the conversion results to LCD { 1stline>pdu2txt , 2ndline>txt2pdu }
;the Lcd I had was 2x8 ,so I can output a few bytes only.
gosub pdu2txt
lcdout $fe,$1,char(0),char(1),char(2),char(3),char(4),char(5),char(6),char(7)
gosub txt2pdu 
lcdout $fe,$c0,hex pdu(0),hex pdu(1),hex pdu(2),hex pdu(3)
loop:
goto loop
;---------your code until here.----------------------

;-----CONVERSION ROUTINES-------
;---TXT2PDU--(sms_encoding)-----
TXT2PDU:
tmp(1)=char(1) << 7 
tmp(2)=char(2) << 6 
tmp(3)=char(3) << 5 
tmp(4)=char(4) << 4 
tmp(5)=char(5) << 3 
tmp(6)=char(6) << 2 
tmp(7)=char(7) << 1

ch(0)=chaR(0)                  
ch(1)=chaR(1) >> 1 
ch(2)=chaR(2) >> 2 
ch(3)=chaR(3) >> 3 
ch(4)=chaR(4) >> 4 
ch(5)=chaR(5) >> 5 
ch(6)=chaR(6) >> 6 
                      
pdu(0)=tmp(1) + ch(0)
pdu(1)=tmp(2) + ch(1)
pdu(2)=tmp(3) + ch(2)
pdu(3)=tmp(4) + ch(3)
pdu(4)=tmp(5) + ch(4)
pdu(5)=tmp(6) + ch(5)
pdu(6)=tmp(7) + ch(6)
RETURN

;---PDU2TXT--(sms_decoding)---
PDU2TXT:
tmp(0)=pdu(0) >> 7 
tmp(1)=pdu(1) >> 6 
tmp(2)=pdu(2) >> 5 
tmp(3)=pdu(3) >> 4 
tmp(4)=pdu(4) >> 3 
tmp(5)=pdu(5) >> 2 
tmp(6)=pdu(6) >> 1 

ch(0)=pdu(0) & $7f                  
ch(1)=pdu(1) & $3f:CH(1)=CH(1) << 1 
ch(2)=pdu(2) & $1f:CH(2)=CH(2) << 2 
ch(3)=pdu(3) & $0f:CH(3)=CH(3) << 3  
ch(4)=pdu(4) & $07:CH(4)=CH(4) << 4  
ch(5)=pdu(5) & $03:CH(5)=CH(5) << 5  
ch(6)=pdu(6) & $01:CH(6)=CH(6) << 6  

char(0)=ch(0)
char(1)=ch(1) + tmp(0)
char(2)=ch(2) + tmp(1)
char(3)=ch(3) + tmp(2)
char(4)=ch(4) + tmp(3)
char(5)=ch(5) + tmp(4)
char(6)=ch(6) + tmp(5)
char(7)=tmp(6) 
RETURN
END

 

