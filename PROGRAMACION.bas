{
*****************************************************************************
*  Name    : PROGRAMACION.BAS                                               *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2010 Scanner Alarmas                             *
*          : All Rights Reserved                                            *
*  Date    : 02/02/2010                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

Module PROGRAMACION

    'Include "6P20.bas"
    Include "H6P20.bas"
    Include "DISPLAY.bas"
    Include "ROM.bas"
    Include "BUZZER.bas"
//    Include "TECLADO.bas"
    Include "CENTRAL.bas"
    Include "STRING.bas"
//    Include "MEMORIA.bas"
//    Include "DTMF.bas"
    Include "IO.bas"
    Include "Convert.bas"
    Include "System.bas"

    //Declaracion de varibles locales
    
    //Declaracion de conatantes locales
    Const UltimoPaso = 20,
    TiempoProgramacion = 30
    
        
    //Declaracion de varibles publicas
    Public Dim
        TimerProgramacion As Byte,
        TimerBorrarInalambricos As Byte,
        flag250ms As Boolean
    
    //Declaracion de conatantes publicas
    
    //Beep + pausa de 500 ms
    Sub BeepPausa()
        BeepCorto()
        Delay(500)
    End Sub

    //Beep + pausa de 500 ms
    Sub BeepLargoPausa()
        BeepLargo()
        Delay(500)
    End Sub

    //Sub para seleccionar un valor por teclas
    Private Function SolicitarValor(pMinimo, pMaximo As Byte, pTiempo As Byte = 27) As Byte
        result=pMinimo
        BeepCorto()
        DISPLAY.MostrarDigito(pMinimo)
        
        EsperarSoltarbtnProg()
        TimerProgramacion=TiempoProgramacion

        Repeat
            //Si se pulsa el boton de seleccion
            If IO.btnSelPulsado Then
                //Paso al siguiente valor
                Inc(result)
                //Si esta por arriba del maximo
                If result>pMaximo Then
                    //Vuelvo al minimo
                    result=pMinimo
                EndIf
                
                //Muestro el nuevo valor en el display
                DISPLAY.MostrarDigito(result)
                BeepCorto()
                
                Repeat
                    //Reinicio el contador de salida
                    TimerProgramacion=TiempoProgramacion
                Until Not IO.btnSelPulsado
            EndIf
        Until TimerProgramacion<pTiempo Or IO.btnProgPulsado
        
        //Si pulse el boton de prog
        If btnProgPulsado Then
            //Devuelvo un 255 indicando la cancelacion
            result=255
        EndIf
    End Function
    
    //Sub para seleccionar un tiempo por teclas
    Private Function SolicitarTiempo(pMinimo, pMaximo, pActual As Byte) As Byte
        result=pActual
        BeepCorto()
        DISPLAY.MostrarDigito(pActual)
        
        EsperarSoltarbtnProg()
        TimerProgramacion=TiempoProgramacion

        Repeat
            //Si se pulsa el boton de seleccion
            If IO.btnSelPulsado Then
                //Paso al siguiente valor
                Inc(result)
                //Si esta por arriba del maximo
                If result>pMaximo Then
                    //Vuelvo al minimo
                    result=pMinimo
                EndIf
                
                //Muestro el nuevo valor en el display
                DISPLAY.MostrarDigito(result)
                BeepCorto()
                
                EsperarSoltarbtnSel()
                //Reinicio el contador de salida
                TimerProgramacion=TiempoProgramacion
            EndIf
        Until TimerProgramacion<27 Or IO.btnProgPulsado
        
        //Si pulse el boton de prog o no cambie el valor
        If btnProgPulsado Or pActual=result Then
            //Devuelvo un 255 indicando la cancelacion
            result=255
        EndIf
    End Function


    //Muestra un digito y hace beep con pausa
    //Recibe un digito en ascii y lo convierte al ordinal correspondiente
    Sub MostrarDigitoBeepPausa(digito As Byte)
        DISPLAY.MostrarDigito(digito-48)
        BeepPausa()
    End Sub
    
    
    //Muestra la posicion en memoria en la que se encuentra el ultimo inalambrico recibido
    Sub MostrarPosicionEnMemoria()
        Dim numero As String,
            unidad, decena, centena As Byte
        
        DISPLAY.ApagarDisplay()
        Delay(500)
        
        numero=DecToStr(HT6P20.PosicionEnMemoria,3)
        
        unidad=numero(2)
        decena=numero(1)
        centena=numero(0)
            
        MostrarDigitoBeepPausa(centena)
        MostrarDigitoBeepPausa(decena)
        MostrarDigitoBeepPausa(unidad)
    End Sub

    //Programacion de controles remotos, sensores incendio o sensores emergencia
    Sub ProgramarInalambricos(pZona As Byte)
    
        Dim ZonaSeleccionada As Byte,
            contador As Byte
        
        DISPLAY.ApagarDisplay()
        DISPLAY.ApagarPunto()
{
        If pZona=HT6P20.Zona1Remotos Then
            DISPLAY.MostrarDigito(DISPLAY.DisplayRemoto)
        EndIf
        If pZona=HT6P20.ZonaIncendioInalambrica Then
            DISPLAY.MostrarDigito(DISPLAY.DisplayIncendio)
        EndIf
        If pZona=HT6P20.ZonaEmergenciaInalambrica Then
            DISPLAY.MostrarDigito(DISPLAY.DisplayEmergencia)
        EndIf
        If pZona=HT6P20.Zona1Inalambrica Then
            DISPLAY.MostrarDigito(DISPLAY.DisplaySensoresRobo)
        EndIf
}        
        BeepLargoPausa()
        'Delay(1000)

        EsperarSoltarbtnProg()
        ContBuenos=0
        TimerProgramacion=TiempoProgramacion
        DISPLAY.ParpadearPunto

        Repeat
            //Leo 6p20 y teclado
            HT6P20.LeerTren()

            //Si estoy recibiendo algo...
            If HT6P20.Recibiendo6P20 Then
                //Me quedo en prog por un rato...
                TimerProgramacion=TiempoProgramacion
                //Si esta en memoria....
                If HT6P20.EstaEnLista Then
                    If HT6P20.EsRemoto Then
                        //Si es remoto muestro la r y el grupo al que pertenece
                        DISPLAY.MostrarDigito(DISPLAY.DisplayRemoto)
                        BeepPausa()
                        DISPLAY.MostrarDigito(HT6P20.ZonaInalambrica-8)
                        BeepPausa()
                    Else
                        If HT6P20.EsSensorIncendio Then
                            //Si es sensor de incendio...
                            DISPLAY.MostrarDigito(DISPLAY.DisplayIncendio)
                            BeepPausa()
                        Else
                            If HT6P20.EsSensorEmergencia Then
                                //Si es sensor de emergencia....
                                DISPLAY.MostrarDigito(DISPLAY.DisplayEmergencia)
                                BeepPausa()
                            Else
                                //Sino es un sensor de robo y muestro la zona
                                DISPLAY.MostrarDigito(DISPLAY.DisplaySensoresRobo)
                                BeepPausa()
                                DISPLAY.MostrarDigito(HT6P20.ZonaInalambrica)
                                BeepPausa()
                            EndIf
                        EndIf
                    EndIf
                    //Muestro la posicion en memoria inalambrica del dispositivo detectado
                    MostrarPosicionEnMemoria()
                Else
                    //no esta en lista pregunto si lo almaceno y si corresponde en que zona...
                    DISPLAY.MostrarDigito(DISPLAY.DisplayInterrogacion)
                    BeepCorto()

                    Repeat
                        ClrWDT()
                    Until TimerProgramacion<25 Or IO.btnProgPulsado Or IO.btnSelPulsado

                    If TimerProgramacion<25 Or IO.btnProgPulsado Then
                        //Si se cancelo por tiempo o tecla ast...
                        BeepCorto()
                    Else
                        //Si se acepto...
                        If pZona = HT6P20.ZonaIncendioInalambrica Or pZona = HT6P20.ZonaEmergenciaInalambrica Then
                            //Intento guardarlo....
                            If ROM.GuardarInalambrico(pZona) Then
                                //Si hay lugar aviso que se guardo....
                                BeepLargo()
                                //Muestro la posicion en memoria inalambrica del dispositivo detectado y guardado
                                MostrarPosicionEnMemoria()
                            Else
                                //Sino aviso que no hay lugar
                                BeepCorto()
                                BeepCorto()
                                BeepCorto()
                                BeepCorto()
                            EndIf
                        Else
                            //Pido un valor entre 1 y 8
                            ZonaSeleccionada=SolicitarValor(1, 8)
                            
                            //Si se cancela
                            If ZonaSeleccionada = 255 Then
                                BeepCorto()
                            Else

                                //Si estamos queriendo guardar remotos el offset es 8
                                If pZona=HT6P20.Zona1Remotos Then
                                    ZonaSeleccionada = ZonaSeleccionada + 8
                                EndIf
                                
                                //Intento guardarlo....
                                If ROM.GuardarInalambrico(ZonaSeleccionada) Then
                                    //Si hay lugar aviso que se guardo....
                                    BeepLargo()
                                    //Muestro la posicion en memoria inalambrica del dispositivo detectado y guardado
                                    MostrarPosicionEnMemoria()
                                Else
                                    //Sino aviso que no hay lugar
                                    BeepCorto()
                                    BeepCorto()
                                    BeepCorto()
                                    BeepCorto()
                                EndIf
                                
                            EndIf
                        EndIf
                    EndIf
                EndIf                
                //Vacio el buffer de recibidos
                ContBuenos=0
            Else
                //si estoy pulsando el boton de seleccion
                If btnSelPulsado Then
                    //Muestro la funcion de borrar
                    DISPLAY.MostrarDigito(DisplayBorrar)
                    //Actualizo el timer de salida porque hay accion por parte del usuario
                    TimerProgramacion=TiempoProgramacion
                    
                    //Si lo tuve pulsado por mas de 5 segundos
                    If tmrBtnSel>500 Then

                        Select pzona
                            Case HT6P20.ZonaIncendioInalambrica
                                ROM.BorrarInalambrico(HT6P20.ZonaIncendioInalambrica)
                            Case HT6P20.ZonaEmergenciaInalambrica
                                ROM.BorrarInalambrico(HT6P20.ZonaEmergenciaInalambrica)
                            Else
                                ZonaSeleccionada=SolicitarValor(0, 8)
                                Select ZonaSeleccionada
                                    Case 0
                                        //Borro todos los elementos desde el primero
'                                        ROM.BorrarInalambrico(pZona)
'                                        ROM.BorrarInalambrico(pZona+1)
'                                        ROM.BorrarInalambrico(pZona+2)
'                                        ROM.BorrarInalambrico(pZona+3)
'                                        ROM.BorrarInalambrico(pZona+4)
'                                        ROM.BorrarInalambrico(pZona+5)
'                                        ROM.BorrarInalambrico(pZona+6)
'                                        ROM.BorrarInalambrico(pZona+7)
                                        For contador = 0 To 7
                                            ROM.BorrarInalambrico(pZona+contador)
                                        Next

                                    Case 255
                                        BeepCorto()
                                    Else
                                        //Sino solo la zona seleccionada desde el offset de la zona que llamo a la rutina
                                        ROM.BorrarInalambrico(ZonaSeleccionada+pZona-1)
                                End Select
                        End Select
                        
                        If ZonaSeleccionada<>255 Then
                            BeepLargo()
                            DISPLAY.ApagarDisplay()
                            BeepLargo()
                            DISPLAY.MostrarDigito(DISPLAY.DisplayBorrar)
                            BeepLargo()
                            DISPLAY.ApagarDisplay()
                            BeepLargo()
                            DISPLAY.MostrarDigito(DISPLAY.DisplayBorrar)
                            BeepLargo()                   
                        EndIf
                        EsperarSoltarbtnSel()


                    EndIf
                Else
                    //Apago el display
                    DISPLAY.ApagarDisplay()
                EndIf
            EndIf
            
            If IO.btnProgPulsado Then
                TimerProgramacion=0
            EndIf

        Until TimerProgramacion=0
        BeepLargo()
        DISPLAY.ApagarDisplay
        DISPLAY.ApagarPunto
        EsperarSoltarbtnProg()
    End Sub

    //Programa los tiempos recibe el minimo, maximo, multiplicador y destino de la modifiacion devuelve true si se cambio
    Function ProgramarTiempos (pMinimo, pMaximo, pMultiplicador As Byte, ByRef pTiempo As Byte) As Boolean
    
        Dim
            TiempoSeleccionado As Byte,
            TiempoActual As Byte
        

        TimerProgramacion=TiempoProgramacion

        TiempoActual = pTiempo/pMultiplicador

        //Asumo que no se modifica
        result=false
        
        //Solicito el valor
        TiempoSeleccionado = SolicitarTiempo(pMinimo, pMaximo, TiempoActual)
        
        //Si no devolvio un 255, se selecciono algo valido
        If TiempoSeleccionado<>255 Then
            //Calculo y actualizo el tiempo en ram
            pTiempo=TiempoSeleccionado*pMultiplicador
            //Seteo la bandera para avisar que se modifico
            result=true
        EndIf

        BeepLargo()
        EsperarSoltarbtnProg()
        DISPLAY.ApagarDisplay
    End Function
    
    //Muestra los tiempos escalados segun el divisor
    Sub MostrarTiempos(pTiempo, pDivisor As Byte)
        DISPLAY.ApagarDisplay()
        Delay(500)
        DISPLAY.MostrarDigito(pTiempo/pDivisor)
        BeepLargo()
    End Sub
    
{
    //Mustra las alarmas para un determinado telefono
    Sub MostrarAlarmasTelefono(pNumero As Byte)
        Dim
            Robo, Asalto, Emergencia, Incendio As Boolean
            
        ROM.LeerAlarmas(pNumero, Robo, Asalto, Emergencia, Incendio)
            
        beeplargo()
        DISPLAY.ApagarDisplay()
        Delay(500)
        
        If Robo Then
            DISPLAY.EncenderPunto()
        Else
            DISPLAY.ApagarPunto()
        EndIf
        DISPLAY.MostrarDigito(DISPLAY.DisplayRobo)
        beepcorto()
        Delay(500)
        
        If Asalto Then
            DISPLAY.EncenderPunto()
        Else
            DISPLAY.ApagarPunto()
        EndIf
        DISPLAY.MostrarDigito(DISPLAY.DisplayAsalto)
        beepcorto()
        Delay(500)
        
        If Emergencia Then
            DISPLAY.EncenderPunto()
        Else
            DISPLAY.ApagarPunto()
        EndIf
        DISPLAY.MostrarDigito(DISPLAY.DisplayEmergencia)
        beepcorto()
        Delay(500)
        
        If Incendio Then
            DISPLAY.EncenderPunto()
        Else
            DISPLAY.ApagarPunto()
        EndIf
        DISPLAY.MostrarDigito(DISPLAY.DisplayIncendio)
        beepcorto()
        Delay(500)

        DISPLAY.ApagarDisplay()
    End Sub
}    
    //Toma la linea disca un 1 muestra en el display el digito y reproduice el mensaje
    
    {      
    Sub VerificarMensaje(pMensaje As Byte)
        Select pMensaje
            Case MEMORIA.mensajeRobo
                DISPLAY.MostrarDigito(DISPLAY.DisplayRobo)
            Case MEMORIA.mensajeAsalto
                DISPLAY.MostrarDigito(DISPLAY.DisplayAsalto)
            Case MEMORIA.mensajeEmergencia
                DISPLAY.MostrarDigito(DISPLAY.DisplayEmergencia)
            Case MEMORIA.mensajeIncendio
                DISPLAY.MostrarDigito(DISPLAY.DisplayIncendio)
        End Select
        DTMF.DiscarVerificacion()
        Delay(2000)
        DISPLAY.ParpadearPunto()
        MEMORIA.ReproducirMensaje(pMensaje)
        TimerProgramacion=tiempoprogramacion
        Repeat
            TECLADO.LeerTeclado()
        Until TECLADO.TeclaAst=1 Or (TimerProgramacion=21)
        If TECLADO.TeclaAst=1 Then
            MEMORIA.DetenerReproduccion()
        EndIf
        TECLADO.EsperarSoltarTeclas()
        DISPLAY.ApagarPunto()
        DTMF.Colgar()
    End Sub
        }
    
    {
    //Muestra en el display el digito y graba el mensaje correspondiente
    Sub GrabarMensaje(pMensaje As Byte)
        Select pMensaje
            Case MEMORIA.mensajeRobo
                DISPLAY.MostrarDigito(DISPLAY.DisplayRobo)
            Case MEMORIA.mensajeAsalto
                DISPLAY.MostrarDigito(DISPLAY.DisplayAsalto)
            Case MEMORIA.mensajeEmergencia
                DISPLAY.MostrarDigito(DISPLAY.DisplayEmergencia)
            Case MEMORIA.mensajeIncendio
                DISPLAY.MostrarDigito(DISPLAY.DisplayIncendio)
        End Select
        MEMORIA.GrabarMensaje(pMensaje)
    End Sub
        }
    
{
    //Programacion de numero de telefono
    Sub ProgramarNumeroTelefono(pNumero As Byte)
        Dim
            Telefono As String(64),
            Caracter As Char
        
        DISPLAY.ApagarDisplay()
        beeplargo()
        DISPLAY.MostrarDigito(DISPLAY.DisplayInterrogacion)
        TECLADO.EsperarSoltarTeclas()
        TimerProgramacion=tiempoprogramacion
        Telefono=null
        Repeat
            TECLADO.LeerTeclado()
            If TECLADO.TeclaPulsada Then
                DISPLAY.MostrarDigito(TECLADO.UltimaTeclaPulsada)
                beepcorto()
                Select TECLADO.UltimaTeclaPulsada
                    Case TECLADO.ultimaTeclaAst
                        Telefono=Telefono + "*"
                    Case TECLADO.ultimaTeclaNum
                        Telefono=Telefono + "P"
                    Else
                        Caracter=TECLADO.UltimaTeclaPulsada+48
                        Telefono=Telefono+Caracter
                End Select 
                TECLADO.EsperarSoltarTeclas()
                TimerProgramacion=tiempoprogramacion
            EndIf
        Until TimerProgramacion<25 Or Length(Telefono)=63
        If Telefono <> null Then
            ROM.EscribirTelefono(pNumero, Telefono)
        EndIf
        DISPLAY.ApagarDisplay()
        beeplargo()
    End Sub
}
    
    //Programacion de las alarmas correspondientes a un numero de telefono
    Sub ProgramarAlarmasTelefono(pNumero As Byte)
{    
        Dim
            ContadorAlarmas As Byte,
            Alarmas As Byte,
            Robo As Alarmas.booleans(0),
            Asalto As Alarmas.booleans(1),
            Emergencia As Alarmas.booleans(2),
            Incendio As Alarmas.booleans(3)
    
        DISPLAY.ApagarDisplay()
        beeplargo()
        Delay(200)
        TECLADO.EsperarSoltarTeclas()
    
        ROM.LeerAlarmas(pNumero, Robo, Asalto, Emergencia, Incendio)
        ContadorAlarmas=0
        TimerProgramacion=tiempoprogramacion
        Repeat
            TECLADO.LeerTeclado()
            If TECLADO.TeclaPulsada Then
                beepcorto()
                TimerProgramacion=tiempoprogramacion
            EndIf
            Select ContadorAlarmas
                Case 0
                    DISPLAY.MostrarDigito(DISPLAY.DisplayRobo)
                    If Robo Then
                        DISPLAY.EncenderPunto()
                    Else
                        DISPLAY.ApagarPunto()
                    EndIf
                    If TECLADO.TeclaNum=1 Then
                        Robo=Not Robo
                        TECLADO.EsperarSoltarTeclas()
                    EndIf
                Case 1
                    DISPLAY.MostrarDigito(DISPLAY.DisplayAsalto)
                    If Asalto Then
                        DISPLAY.EncenderPunto()
                    Else
                        DISPLAY.ApagarPunto()
                    EndIf
                    If TECLADO.TeclaNum=1 Then
                        Asalto=Not Asalto
                        TECLADO.EsperarSoltarTeclas()
                    EndIf
                Case 2
                    DISPLAY.MostrarDigito(DISPLAY.DisplayEmergencia)
                    If Emergencia Then
                        DISPLAY.EncenderPunto()
                    Else
                        DISPLAY.ApagarPunto()
                    EndIf
                    If TECLADO.TeclaNum=1 Then
                        Emergencia=Not Emergencia
                        TECLADO.EsperarSoltarTeclas()
                    EndIf
                Case 3
                    DISPLAY.MostrarDigito(DISPLAY.DisplayIncendio)
                    If Incendio Then
                        DISPLAY.EncenderPunto()
                    Else
                        DISPLAY.ApagarPunto()
                    EndIf
                    If TECLADO.TeclaNum=1 Then
                        Incendio=Not Incendio
                        TECLADO.EsperarSoltarTeclas()
                    EndIf
            End Select
            If TECLADO.Tecla0=1 Then
                Inc(ContadorAlarmas)
                If ContadorAlarmas=4 Then
                    ContadorAlarmas=0
                EndIf
                TECLADO.EsperarSoltarTeclas()
            EndIf
        Until TimerProgramacion<25 Or TECLADO.TeclaAst=1
        If TimerProgramacion<25 Then
            ROM.EscribirAlarmas(pNumero, Robo, Asalto, Emergencia, Incendio)
        EndIf
        DISPLAY.ApagarDisplay()
        beeplargo()
}
    End Sub   
    
    //Programar metodo de marcado
    Sub ProgramarMetodoMarcado(pMetodo As Byte)
        CENTRAL.MetodoMarcado=pMetodo
        ROM.GuardarSeteos()
        If pMetodo=CENTRAL.MetodoMarcadoTonos Then
            DISPLAY.MostrarDigito(DISPLAY.DisplayMetodoMarcadoTonos)
        Else
            DISPLAY.MostrarDigito(DISPLAY.DisplayMetodoMarcadoPulsos)
        EndIf
        ROM.GuardarSeteos()
        BeepLargo()
    End Sub
    
    //Muestra informacion del comportamiento de las zonas (demoradas, cancelables y 24 horas) tanto cableadas como inalambricas y normal abiertas
    Sub MostrarComportamientoZonas(pDigitoAMostrar As Byte, pPunto As Boolean, pZonas As Byte)
        Dim
            ContadorZonas As Byte
            
        DISPLAY.MostrarDigito(pDigitoAMostrar)
        If pPunto Then
            DISPLAY.EncenderPunto()
        Else
            DISPLAY.ApagarPunto()
        EndIf
        BeepLargoPausa()
        'Delay(200)
        DISPLAY.ApagarDisplay()
        Delay(500)
        For ContadorZonas = 0 To 7
            DISPLAY.MostrarDigito(ContadorZonas+1)
            If pZonas.booleans(ContadorZonas) Then
                DISPLAY.EncenderPunto()
            Else
                DISPLAY.ApagarPunto()
            EndIf
            BeepPausa()
            Delay(500)
        Next
        DISPLAY.ApagarDisplay()
    End Sub
    
    //Programa el comportamiento de las zonas (demoradas, cancelables y 24 horas) tanto cableadas como inalambricas y normal abiertas
    Sub ProgramarComportamientoZonas(ByRef pZonas As Byte)
        Dim
            ZonasTemporal As Byte,
            ContadorZonas As Byte
            
        BeepLargoPausa()
        'Delay(200)
        DISPLAY.ApagarDisplay()
        Delay(500)
        
        ZonasTemporal=pZonas
        TimerProgramacion=TiempoProgramacion
        
        ContadorZonas=0
        DISPLAY.MostrarDigito(1)
        If ZonasTemporal.booleans(0) Then
            DISPLAY.EncenderPunto()
        Else
            DISPLAY.ApagarPunto()
        EndIf
        
        //Espero que se suelte el boton de prog y marco como no soltado al de seleccion
        EsperarSoltarbtnProg()
        btnSelSoltado=false

        Repeat
        
            //Si se solto el boton de seleccion
            If btnSelSoltado Then
                //Si estuvo pulsado por mas de 100ms
                If TiempoBtnSelPulsado > 10 Then
                    Inc(ContadorZonas)
                    If ContadorZonas=8 Then
                        ContadorZonas=0
                    EndIf
                    DISPLAY.MostrarDigito(ContadorZonas+1)
                    If ZonasTemporal.booleans(ContadorZonas) Then
                        DISPLAY.EncenderPunto()
                    Else
                        DISPLAY.ApagarPunto()
                    EndIf
                    BeepCorto()
                EndIf
                btnSelSoltado=false
                //Me aseguro que me quedo en prog por otros 5 segundos
                TimerProgramacion=TiempoProgramacion
            EndIf
            
            //Si se pulso el boton de seleccion por mas de 500ms
            If btnSelPulsado And tmrBtnSel > 50 Then
                //Cambio el estado para la zona
                If ZonasTemporal.booleans(ContadorZonas) Then
                    ZonasTemporal.booleans(ContadorZonas)=false
                    DISPLAY.ApagarPunto()
                Else
                    ZonasTemporal.booleans(ContadorZonas)=true
                    DISPLAY.EncenderPunto()
                EndIf
                BeepLargo()

                EsperarSoltarbtnSel()
                //pongo el timer en 0 otra vez
                tmrBtnSel=0
                //Lo marco como no soltado para evitar se tome como un click corto
                btnSelSoltado=false
                //Me aseguro que me quedo en prog por otros 5 segundos
                TimerProgramacion=TiempoProgramacion
            EndIf


        Until TimerProgramacion<25 Or btnProgPulsado
        If TimerProgramacion<25 Then
            pZonas=ZonasTemporal
            ROM.GuardarSeteos()
            ROM.LeerSeteos()
        EndIf
        DISPLAY.ApagarDisplay()
        BeepLargo()

    End Sub
    
    //Programar las sirenas para los beeps de indicacion de activada o desactivada
    Sub ProgramarSirenas()
    
        Dim EstadoSirenas As Byte,
            digito As Byte
    
        DISPLAY.ApagarDisplay
        BeepLargo()
    
        //Si solo esta habilitada la sirena interior
        If CENTRAL.BeepsPorSirenaInterior=true And CENTRAL.BeepsPorSirenaExterior=false Then
            //el estado es 1
            EstadoSirenas=1
            DISPLAY.MostrarDigito(DISPLAY.DisplaySirenaInterior)
        EndIf

        //Si solo esta habilitada la sirena exterior
        If CENTRAL.BeepsPorSirenaInterior=false And CENTRAL.BeepsPorSirenaExterior=true Then
            //el estado es 2
            EstadoSirenas=2
            DISPLAY.MostrarDigito(DISPLAY.DisplaySirenaExterior)
        EndIf

        //Si estan habilitadas las dos
        If CENTRAL.BeepsPorSirenaInterior=true And CENTRAL.BeepsPorSirenaExterior=true Then
            //el estado es 3
            EstadoSirenas=3
            DISPLAY.MostrarDigito(DISPLAY.DisplayAmbasSirenas)
        EndIf

        //Si estan deshabilitadas las dos
        If CENTRAL.BeepsPorSirenaInterior=false And CENTRAL.BeepsPorSirenaExterior=false Then
            //el estado es 4
            EstadoSirenas=4
        EndIf
        
        Repeat 
            //Si se pulsa el boton de seleccion
            If IO.btnSelPulsado Then
                //Paso al siguiente valor
                Inc(estadoSirenas)
                //Si esta por arriba del maximo
                If EstadoSirenas>4 Then
                    //Vuelvo al minimo
                    EstadoSirenas=1
                EndIf
                
                //Muestro el nuevo valor en el display
                //Y actualizo los flags de indicacion
                Select EstadoSirenas
                    Case 1
                        CENTRAL.BeepsPorSirenaInterior=true
                        CENTRAL.BeepsPorSirenaExterior=false
                        digito=DISPLAY.DisplaySirenaInterior
                    Case 2
                        CENTRAL.BeepsPorSirenaInterior=false
                        CENTRAL.BeepsPorSirenaExterior=true
                        digito=DISPLAY.DisplaySirenaExterior
                    Case 3
                        CENTRAL.BeepsPorSirenaInterior=true
                        CENTRAL.BeepsPorSirenaExterior=true
                        digito=DISPLAY.DisplayAmbasSirenas
                    Case 4
                        CENTRAL.BeepsPorSirenaInterior=false
                        CENTRAL.BeepsPorSirenaExterior=false
                        digito=DISPLAY.DisplayNada
                End Select
                DISPLAY.MostrarDigito(digito)

                //Genero un beep con buzzer y las sirenas habilitadas
                CENTRAL.Beep()
                
                EsperarSoltarbtnSel()
                //Reinicio el contador de salida
                TimerProgramacion=TiempoProgramacion
            EndIf
            
        Until TimerProgramacion<27 Or IO.btnProgPulsado

        If IO.btnProgPulsado Then
            ROM.LeerSeteos()
        Else
            ROM.GuardarSeteos()
        EndIf
        
        BeepLargoPausa()
        'Delay(500)
        DISPLAY.ApagarDisplay()
    End Sub
    
    //Programacion de las entradas cableadas especiales, antidesarme, asalto (zona6), emergencia (zona7) e incendio (zona8)
    Sub ProgramarCableadaEspecial(pDigito As Byte, pHabilitado As Boolean)
        Select pDigito
            Case DISPLAY.DisplayAntidesarme
                CENTRAL.AntidesarmeHabilitado=pHabilitado
            Case DISPLAY.DisplayAsalto
                CENTRAL.AsaltoCableadoHabilitado=pHabilitado
            Case DISPLAY.DisplayEmergencia
                CENTRAL.EmergenciaCableadaHabilitada=pHabilitado
            Case DISPLAY.DisplayIncendio
                CENTRAL.IncendioCableadoHabilitado=pHabilitado
        End Select
        ROM.GuardarSeteos()
        DISPLAY.MostrarDigito(pDigito)
        If pHabilitado Then
            DISPLAY.EncenderPunto()
        Else
            DISPLAY.ApagarPunto()
        EndIf
        BeepLargoPausa()
        'Delay(500)
        DISPLAY.ApagarDisplay()
        DISPLAY.ApagarPunto()
    End Sub
    
    Sub MostrarDisparos()
        Dim
            ContadorDisparos As Byte,
            Zona As Byte,
            EsCableada As Boolean
            
        DISPLAY.ApagarDisplay()
        BeepLargoPausa()
        'Delay(500)
        For ContadorDisparos = 0 To 7
            If ROM.LeerDisparo(ContadorDisparos, Zona, EsCableada) Then
                DISPLAY.MostrarDigito(Zona)
                If EsCableada Then
                    DISPLAY.EncenderPunto()
                Else
                    DISPLAY.ApagarPunto()
                EndIf
                BeepPausa()
            EndIf
        Next
        DISPLAY.ApagarDisplay()
    End Sub
    
{
    //Programa un codigo - recibe cual codigo y cuantos digitos
    Sub ProgramarCodigo()
        Dim ContadorDigitos As Byte
        
        TimerProgramacion=tiempoprogramacion
        ContadorDigitos=4
        DISPLAY.MostrarDigito(DISPLAY.DisplayInterrogacion)
        beeplargo()
        Repeat
            TECLADO.LeerTeclado()
            //si se pulsa asterisco sale...
            If TECLADO.TeclaAst=1 Then
                TimerProgramacion=0
            EndIf
            //Si se ingresa algo valido devuelto el numero ingresado escalado y pongo el result en true
            If TECLADO.TeclaPulsada And TECLADO.UltimaTeclaPulsada<=TECLADO.ultimaTecla9 And TECLADO.UltimaTeclaPulsada>=TECLADO.ultimaTecla0 Then
                Dec(ContadorDigitos)
                TECLADO.CodigoProgramacion(ContadorDigitos)=TECLADO.UltimaTeclaPulsada
                DISPLAY.MostrarDigito(TECLADO.UltimaTeclaPulsada)
                beepcorto()
                TECLADO.EsperarSoltarTeclas()
                If ContadorDigitos=0 Then
                    TimerProgramacion=0
                EndIf
            EndIf
        Until TimerProgramacion=0
        If ContadorDigitos=0 Then
            ROM.GuardarSeteos()
        EndIf
        ROM.LeerSeteos()
        beeplargo()
        DISPLAY.ApagarDisplay
        DISPLAY.ApagarPunto
    End Sub
}    

    Private Sub MostrarPasoActual(pPaso, pDigito As Byte)
        Select pPaso
            Case 0      //Programacion de remotos
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayC)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayR)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.ApagarPunto()
            Case 1      //Programacion de Sensores de robo
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayS)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayR)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.ApagarPunto()
            Case 2      //Programacion de Emergencia medica
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayS)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayE)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.ApagarPunto()
            Case 3      //Programacion de Sensores de Incendio
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayS)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayI)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.ApagarPunto()
            Case 4      //Programacion de Demoradas inalambricas
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayD)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayI)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.ApagarPunto()
            Case 5      //Programacion de Cancelables inalambricas
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayC)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayI)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.ApagarPunto()
            Case 6      //Programacion de 24 horas inalambricas
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(2)
                    Case 1
                        DISPLAY.MostrarDigito(4)
                    Case 2
                        DISPLAY.MostrarDigito(DISPLAY.DisplayH)
                    Case 3
                        DISPLAY.MostrarDigito(DISPLAY.DisplayI)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.ApagarPunto()
            Case 7      //Programacion de Asalto por coaccion al desactivar con el boton derecho
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayA)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayD)
                    Case 2
                        DISPLAY.MostrarDigito(DISPLAY.DisplayC)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                If AsaltoPorBoton2Habilitado=true Then
                    DISPLAY.EncenderPunto()
                Else
                    DISPLAY.ApagarPunto()
                EndIf
            Case 8      //Programacion de Zonas Normal Abiertas
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayN)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayA)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.EncenderPunto()
            Case 9      //Programacion de Demoradas cableadas
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayD)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayC)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.EncenderPunto()
            Case 10     //Programacion de Cancelables cableadas
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayC)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayC)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.EncenderPunto()
            Case 11     //Programacion de 24 horas cableadas
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(2)
                    Case 1
                        DISPLAY.MostrarDigito(4)
                    Case 2
                        DISPLAY.MostrarDigito(DISPLAY.DisplayH)
                    Case 3
                        DISPLAY.MostrarDigito(DISPLAY.DisplayC)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.EncenderPunto()
    
            Case 12     //Programacion de Antidesarme
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayA)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayD)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                If AntidesarmeHabilitado=true Then
                    DISPLAY.EncenderPunto()
                Else
                    DISPLAY.ApagarPunto()
                EndIf
                
            Case 13     //Programacion de Asalto cableado
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayA)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayC)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                If AsaltoCableadoHabilitado=true Then
                    DISPLAY.EncenderPunto()
                Else
                    DISPLAY.ApagarPunto()
                EndIf

            Case 14     //Programacion de Emergencia cableada
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayE)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayC)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                If EmergenciaCableadaHabilitada=true Then
                    DISPLAY.EncenderPunto()
                Else
                    DISPLAY.ApagarPunto()
                EndIf

            Case 15     //Programacion de Incendio cableado
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayI)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayC)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                If IncendioCableadoHabilitado=true Then
                    DISPLAY.EncenderPunto()
                Else
                    DISPLAY.ApagarPunto()
                EndIf
    
            Case 16     //Programacion de Tiempo de entrada (1-9 x 10 segundos)
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayT)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayE)
                    Case 2
                        DISPLAY.MostrarDigito(DISPLAY.DisplayN)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.ApagarPunto()
            Case 17     //Programacion de Tiempo de salida (1-9 x 10 segundos)
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayT)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayS)
                    Case 2
                        DISPLAY.MostrarDigito(DISPLAY.DisplayA)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.ApagarPunto()
            Case 18     //Programacion de Tiempo de sirena (1-4 x 1 minuto)
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayT)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayS)
                    Case 2
                        DISPLAY.MostrarDigito(DISPLAY.DisplayI)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.ApagarPunto()
    
            Case 19     //Programacion de Seleccion de sirenas
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayB)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayS)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.EncenderPunto()
    
            Case 20     //Programacion de Registro de disparos
                Select pDigito
                    Case 0
                        DISPLAY.MostrarDigito(DISPLAY.DisplayR)
                    Case 1
                        DISPLAY.MostrarDigito(DISPLAY.DisplayD)
                    Else
                        DISPLAY.ApagarDisplay()
                End Select
                DISPLAY.ApagarPunto()
        End Select
    End Sub

    //Entrada a la rutina general de programacion
    {
    Definicion de los menues
        Paso 0 : Programacion de remotos
        Paso 1 : Programacion de Sensores de robo
        Paso 2 : Programacion de Emergencia medica
        Paso 3 : Programacion de Sensores de Incendio
        Paso 4 : Programacion de Demoradas inalambricas
        Paso 5 : Programacion de Cancelables inalambricas
        Paso 6 : Programacion de 24 horas inalambricas
        Paso 7 : Programacion de Asalto por coaccion al desactivar con el boton derecho

        Paso 8 : Programacion de Zonas Normal Abiertas
        Paso 9 : Programacion de Demoradas inalambricas
        Paso 10: Programacion de Cancelables inalambricas
        Paso 11: Programacion de 24 horas inalambricas

        Paso 12: Programacion de Antidesarme
        Paso 13: Programacion de Asalto cableado
        Paso 14: Programacion de Emergencia cableada
        Paso 15: Programacion de Incendio cableado

        Paso 16: Programacion de Tiempo de entrada (1-9 x 10 segundos)
        Paso 17: Programacion de Tiempo de salida (1-9 x 10 segundos)
        Paso 18: Programacion de Tiempo de sirena (1-4 x 1 minuto)

        Paso 19: Programacion de Seleccion de sirenas

        Paso 20: Programacion de Registro de disparos

}

    Public Sub EntrarEnProgramacion()
    
        Dim
            PasoProgramacion As Byte,
            Digito As Byte,
            ActualizarDisplay As Boolean
    
        DISPLAY.MostrarDigito(DISPLAY.DisplayProgramacion)
        DISPLAY.ApagarPunto()
        BeepLargo()
        BeepLargo()
        BeepLargo()
        DISPLAY.ApagarDisplay
        
        EsperarSoltarbtnProg()
        btnProgSoltado=false
        TimerProgramacion=TiempoProgramacion
        PasoProgramacion=0
        Digito=4
        ActualizarDisplay=false

        Repeat

            //Si se solto el boton de prog y estuvo pulsado por 100 milisegundos
            If btnProgSoltado Then
                If TiempoBtnProgPulsado>10 Then

                    //Reinicio el timer que me mantiene en programacion
                    TimerProgramacion=TiempoProgramacion
    
                    Select PasoProgramacion
                        Case 0      //Programacion de remotos
                            ProgramarInalambricos(HT6P20.Zona1Remotos)
                        Case 1      //Programacion de Sensores de robo
                            ProgramarInalambricos(HT6P20.Zona1Inalambrica)
                        Case 2      //Programacion de Emergencia medica
                            ProgramarInalambricos(HT6P20.ZonaEmergenciaInalambrica)
                        Case 3      //Programacion de Sensores de Incendio
                            ProgramarInalambricos(HT6P20.ZonaIncendioInalambrica)
                            
                        Case 4      //Programacion de Demoradas inalambricas
                            ProgramarComportamientoZonas(CENTRAL.ZonasInalambricasDemoradas)
                        Case 5      //Programacion de Cancelables inalambricas
                            ProgramarComportamientoZonas(CENTRAL.ZonasInalambricasCancelables)
                        Case 6      //Programacion de 24 horas inalambricas
                            ProgramarComportamientoZonas(CENTRAL.ZonasInalambricas24Horas)

                        Case 7      //Programacion de Asalto por coaccion al desactivar con el boton derecho
                            //Cambio el estado del seteo
                            AsaltoPorBoton2Habilitado = Not AsaltoPorBoton2Habilitado
                            //Guardo en rom
                            ROM.GuardarSeteos
                            //Actualizo el display
                            MostrarPasoActual(PasoProgramacion, Digito)
                            //Hago un beep largo
                            BeepLargo()

                        Case 8      //Programacion de Zonas Normal Abiertas
                            ProgramarComportamientoZonas(CENTRAL.ZonasNormalAbiertas)
                        Case 9      //Programacion de Demoradas cableadas
                            ProgramarComportamientoZonas(CENTRAL.ZonasCableadasDemoradas)
                        Case 10     //Programacion de Cancelables cableadas
                            ProgramarComportamientoZonas(CENTRAL.ZonasCableadasCancelables)
                        Case 11     //Programacion de 24 horas cableadas
                            ProgramarComportamientoZonas(CENTRAL.ZonasCableadas24Horas)
                
                        Case 12     //Programacion de Antidesarme
                            //Cambio el estado del seteo
                            AntidesarmeHabilitado = Not AntidesarmeHabilitado
                            //Guardo en rom
                            ROM.GuardarSeteos
                            //Actualizo el display
                            MostrarPasoActual(PasoProgramacion, Digito)
                            //Hago un beep largo
                            BeepLargo()

                        Case 13     //Programacion de Asalto cableado
                            //Cambio el estado del seteo
                            AsaltoCableadoHabilitado = Not AsaltoCableadoHabilitado
                            //Guardo en rom
                            ROM.GuardarSeteos
                            //Actualizo el display
                            MostrarPasoActual(PasoProgramacion, Digito)
                            //Hago un beep largo
                            BeepLargo()

                        Case 14     //Programacion de Emergencia cableada
                            //Cambio el estado del seteo
                            EmergenciaCableadaHabilitada = Not EmergenciaCableadaHabilitada
                            //Guardo en rom
                            ROM.GuardarSeteos
                            //Actualizo el display
                            MostrarPasoActual(PasoProgramacion, Digito)
                            //Hago un beep largo
                            BeepLargo()

                        Case 15     //Programacion de Incendio cableado
                            //Cambio el estado del seteo
                            IncendioCableadoHabilitado = Not IncendioCableadoHabilitado
                            //Guardo en rom
                            ROM.GuardarSeteos
                            //Actualizo el display
                            MostrarPasoActual(PasoProgramacion, Digito)
                            //Hago un beep largo
                            BeepLargo()

                
                        Case 16     //Programacion de Tiempo de entrada (1-9 x 10 segundos)
                            //Si seleccione un valor lo guardo en rom
                            If ProgramarTiempos(1, 9, 10, CENTRAL.TiempoEntrada) Then
                                ROM.GuardarSeteos()
                            EndIf
                        Case 17     //Programacion de Tiempo de salida (1-9 x 10 segundos)
                            //Si seleccione un valor lo guardo en rom
                            If ProgramarTiempos(1, 9, 10, CENTRAL.TiempoSalida) Then
                                ROM.GuardarSeteos()
                            EndIf

                        Case 18     //Programacion de Tiempo de sirena (1-4 x 1 minuto)
                            //Si seleccione un valor lo guardo en rom
                            If ProgramarTiempos(1, 4, 60, CENTRAL.TiempoSirenas) Then
                                ROM.GuardarSeteos()
                            EndIf
                
                        Case 19     //Programacion de Seleccion de sirenas
                            ProgramarSirenas()
                            
                        Case 20     //Programacion de Registro de disparos
                            MostrarDisparos()
                            
                    End Select
                    
                    //Muestro el paso actual
                    MostrarPasoActual(PasoProgramacion, Digito)
    
                    //Reinicio el timer que me mantiene en programacion
                    TimerProgramacion=TiempoProgramacion
                EndIf
                
                btnProgSoltado=false

            EndIf

            //Si se pulso el boton de seleccion por 100 milisegundos
            If IO.tmrBtnSel>10 Then
            
                //Indico con un beep
                BeepCorto()
                
                //Salto al siguiente paso
                Inc(PasoProgramacion)
            
                //Si me voy mas alla del ultimo paso
                If PasoProgramacion=UltimoPaso+1 Then
                    //Voy hacia el primero
                    PasoProgramacion=0
                EndIf
                
                //Fuerzo a que sea el paso de pausa
                //De esta meanera siempre empieza con la primer letra
                Digito=4
                
                //Muestro el paso actual
                MostrarPasoActual(PasoProgramacion, Digito)
                
                //Reinicio el timer que me mantiene en programacion
                TimerProgramacion=TiempoProgramacion
                
                IO.tmrBtnSel=0
            EndIf

            //Si se solto el boton de prog y estuvo pulsado por mas de 5 segundos
            If tmrBtnProg>500 Then
                //Provoco la salida de programacion
                TimerProgramacion=0
            EndIf
            
            //si flag es cero
            If Not flag250ms Then
                ActualizarDisplay=true
            EndIf
            
            //Si hay flag de medio segundo 
            If flag250ms And ActualizarDisplay Then
                //Paso al digito siguiente
                Inc(Digito)
                //Si el digito es 5
                If Digito=5 Then
                    //vuelvo al cero
                    Digito=0
                EndIf
                
                //Actualizo el display
                MostrarPasoActual(PasoProgramacion, Digito)
                //Borro el flag de actualizardisplay
                ActualizarDisplay=false
            EndIf
        
        Until TimerProgramacion=0
        DISPLAY.ApagarDisplay()
        'delay(500)
        BeepLargo()
        BeepLargo()
        BeepLargo()

        EsperarSoltarbtnProg()
        btnProgSoltado=false
    End Sub

{                            
    Public Sub EntrarEnProgramacion()
        DISPLAY.ApagarDisplay
        beeplargo()
        beeplargo()
        beeplargo()
        DISPLAY.MostrarDigito(DISPLAY.DisplayProgramacion)
        TECLADO.CodigoIngresado(0)=99
        TECLADO.UltimaTeclaPulsada=99
        TimerProgramacion=tiempoprogramacion
        Repeat
            TECLADO.ControlarCodigos()
            If TECLADO.tmrPulsandoTeclas<=2 Then
                DISPLAY.MostrarDigito(DISPLAY.DisplayProgramacion)
                DISPLAY.ApagarPunto()
                If TECLADO.tmrPulsandoTeclas=2 Then 
                    'beeplargo()
                    TimerProgramacion=tiempoprogramacion
                    TECLADO.CodigoIngresado(2)=99
                EndIf
            Else
                If TECLADO.UltimaTeclaPulsada<=TECLADO.ultimaTecla9 Then
                    DISPLAY.MostrarDigito(TECLADO.UltimaTeclaPulsada)
                EndIf
            EndIf
            //si se pulso la tecla asterisco y hay algun codigo ingresado...
            If UltimaTeclaPulsada=ultimaTeclaAst And TECLADO.CodigoIngresado(2)<>99 Then
                Select TECLADO.CodigoIngresado(2)
                    //Codigos del discador
                    Case 1
                        Select TECLADO.CodigoIngresado(1)
                            //programar numero de telefono
                            Case 1
                                If TECLADO.CodigoIngresado(0)>=1 And TECLADO.CodigoIngresado(0)<=8 Then
                                    ProgramarNumeroTelefono(TECLADO.CodigoIngresado(0))
                                EndIf
                            //programar alarmas
                            Case 2
                                If TECLADO.CodigoIngresado(0)>=1 And TECLADO.CodigoIngresado(0)<=8 Then
                                    ProgramarAlarmasTelefono(TECLADO.CodigoIngresado(0))
                                EndIf
                            //programar mensajes
                            Case 3
                                If TECLADO.CodigoIngresado(0)>=1 And TECLADO.CodigoIngresado(0)<=4 Then
                                    GrabarMensaje(TECLADO.CodigoIngresado(0)-1)
                                EndIf
                            //programar metodo de marcado (tonos o pulsos)
                            Case 4
                                If TECLADO.CodigoIngresado(0)>=1 And TECLADO.CodigoIngresado(0)<=2 Then
                                    ProgramarMetodoMarcado(TECLADO.CodigoIngresado(0))
                                EndIf
                            //programar cantidad de rondas de discado (1-3)
                            Case 5
                                If TECLADO.CodigoIngresado(0)>=1 And TECLADO.CodigoIngresado(0)<=3 Then
                                    CENTRAL.CantidadRondasDisparo=TECLADO.CodigoIngresado(0)
                                    ROM.GuardarSeteos()
                                    beeplargo()
                                EndIf
                            //programar cantidad de mensajes por ronda (3-9)
                            Case 6
                                If TECLADO.CodigoIngresado(0)>=3 And TECLADO.CodigoIngresado(0)<=9 Then
                                    CENTRAL.CantidadMensajes=TECLADO.CodigoIngresado(0)
                                    ROM.GuardarSeteos()
                                    beeplargo()
                                EndIf
                        End Select
                    //Codigos de los inalambricos
                    Case 2
                        Select TECLADO.CodigoIngresado(1)
                            //Controles remotos
                            Case 1
                                Select TECLADO.CodigoIngresado(0)
                                    //Programacion de controles remotos
                                    Case 1
                                        ProgramarInalambricos(HT6P20.ZonaRemotos)
                                    //Programacion de sensores robo
                                    Case 2
                                        ProgramarInalambricos()
                                    //Programacion de sensores emergencia medica
                                    Case 3
                                        ProgramarInalambricos(HT6P20.ZonaEmergenciaInalambrica)
                                    //Programacion de sensores incendio
                                    Case 4
                                        ProgramarInalambricos(HT6P20.ZonaIncendioInalambrica)
                                End Select
                            //Comportamientos
                            Case 2
                                Select TECLADO.CodigoIngresado(0)
                                    //Demoradas
                                    Case 1
                                        ProgramarComportamientoZonas(DISPLAY.DisplayComportamientoDemoradas, false, CENTRAL.ZonasInalambricasDemoradas)
                                    //Cancelables
                                    Case 2
                                        ProgramarComportamientoZonas(DISPLAY.DisplayComportamientoCancelables, false, CENTRAL.ZonasInalambricasCancelables)
                                    //24 Horas
                                    Case 3
                                        ProgramarComportamientoZonas(DISPLAY.DisplayComportamiento24Horas, false, CENTRAL.ZonasInalambricas24Horas)
                                End Select
                            //Comportamientos
                            Case 3
                                If TECLADO.CodigoIngresado(0)=1 Then
                                    AsaltoPorBoton2Habilitado=true
                                    ROM.GuardarSeteos
                                    DISPLAY.MostrarDigito(DISPLAY.DisplayAsalto)
                                    DISPLAY.EncenderPunto()
                                    beeplargo()
                                EndIf
                                If TECLADO.CodigoIngresado(0)=2 Then
                                    AsaltoPorBoton2Habilitado=false
                                    ROM.GuardarSeteos
                                    DISPLAY.MostrarDigito(DISPLAY.DisplayAsalto)
                                    DISPLAY.ApagarPunto()
                                    beeplargo()
                                EndIf
                        End Select
                    //Codigos de los cableados
                    Case 3
                        Select TECLADO.CodigoIngresado(1)
                            //Especificos
                            Case 1
                                Select TECLADO.CodigoIngresado(0)
                                    //Normal Abiertas
                                    Case 1
                                        ProgramarComportamientoZonas(DISPLAY.DisplayComportamientoNormalAbiertas, true, CENTRAL.ZonasNormalAbiertas)
                                End Select
                            //Comportamiento
                            Case 2
                                Select TECLADO.CodigoIngresado(0)
                                    //Demoradas
                                    Case 1
                                        ProgramarComportamientoZonas(DISPLAY.DisplayComportamientoDemoradas, true, CENTRAL.ZonasCableadasDemoradas)
                                    //Cancelables
                                    Case 2
                                        ProgramarComportamientoZonas(DISPLAY.DisplayComportamientoCancelables, true, CENTRAL.ZonasCableadasCancelables)
                                    //24 Horas
                                    Case 3
                                        ProgramarComportamientoZonas(DISPLAY.DisplayComportamiento24Horas, true, CENTRAL.ZonasCableadas24Horas)
                                End Select
                            //Antidesarme
                            Case 3
                                Select TECLADO.CodigoIngresado(0)
                                    //Habilitar antidesarme
                                    Case 1
                                        ProgramarCableadaEspecial(DISPLAY.DisplayAntidesarme, true)
                                    //Deshabilitar antidesarme
                                    Case 2
                                        ProgramarCableadaEspecial(DISPLAY.DisplayAntidesarme, false)
                                End Select
                            //Asalto
                            Case 4
                                Select TECLADO.CodigoIngresado(0)
                                    //Habilitar Asalto
                                    Case 1
                                        ProgramarCableadaEspecial(DISPLAY.DisplayAsalto, true)
                                    //Deshabilitar Asalto
                                    Case 2
                                        ProgramarCableadaEspecial(DISPLAY.DisplayAsalto, false)
                                End Select
                            //Emergencia
                            Case 5
                                Select TECLADO.CodigoIngresado(0)
                                    //Habilitar Emergencia
                                    Case 1
                                        ProgramarCableadaEspecial(DISPLAY.DisplayEmergencia, true)
                                    //Deshabilitar Emergencia
                                    Case 2
                                        ProgramarCableadaEspecial(DISPLAY.DisplayEmergencia, false)
                                End Select
                            //Incendio
                            Case 6
                                Select TECLADO.CodigoIngresado(0)
                                    //Habilitar Incendio
                                    Case 1
                                        ProgramarCableadaEspecial(DISPLAY.DisplayIncendio, true)
                                    //Deshabilitar Incendio
                                    Case 2
                                        ProgramarCableadaEspecial(DISPLAY.DisplayIncendio, false)
                                End Select
                        End Select
                    //Codigos de los tiempos
                    Case 4
                        Select TECLADO.CodigoIngresado(1)
                            //Tiempos de entrada y salida
                            Case 1
                                Select TECLADO.CodigoIngresado(0)
                                    //Tiempo de entrada
                                    Case 1
                                        //Si seleccione un valor lo guardo en rom
                                        If ProgramarTiempos(1, 9, 10, CENTRAL.TiempoEntrada) Then
                                            ROM.GuardarSeteos()
                                        EndIf
                                    //Tiempo de salida
                                    Case 2
                                        //Si seleccione un valor lo guardo en rom
                                        If ProgramarTiempos(1, 9, 10, CENTRAL.TiempoSalida) Then
                                            ROM.GuardarSeteos()
                                        EndIf
                                End Select
                            //Otros tiempos
                            Case 2
                                Select TECLADO.CodigoIngresado(0)
                                    //Tiempo de sirenas
                                    Case 1
                                        If ProgramarTiempos(1, 4, 60, CENTRAL.TiempoSirenas) Then
                                            ROM.GuardarSeteos()
                                        EndIf
                                End Select
                        End Select
                    //Seleccion de sirenas
                    Case 5
                        Select TECLADO.CodigoIngresado(1)
                            //Seleccion de sirenas
                            Case 1
                                If TECLADO.CodigoIngresado(0)>=1 And TECLADO.CodigoIngresado(0)<=4 Then
                                    ProgramarSirenas(TECLADO.CodigoIngresado(0))
                                EndIf
                        End Select
                    //Generales
                    Case 6
                        Select TECLADO.CodigoIngresado(1)
                            //Codigos
                            Case 1
                                Select TECLADO.CodigoIngresado(0)
                                    //Codigo de programacion
                                    Case 1
                                        ProgramarCodigo()
                                End Select
                            //Registros
                            Case 2
                                Select TECLADO.CodigoIngresado(0)
                                    //Registro de disparos
                                    Case 1
                                        DISPLAY.MostrarDigito(DISPLAY.DisplayBorrar)
                                        ROM.BorrarDisparos()
                                        beeplargo()
                                End Select
                        End Select
                End Select
                //Reinicio los registros de control del teclado para que no haga otra funcion
                UltimaTeclaPulsada=99
                TECLADO.CodigoIngresado(2)=99
                TimerProgramacion=tiempoprogramacion
            EndIf
            
            //si se pulso la tecla numeral y hay algun codigo ingresado...
            If UltimaTeclaPulsada=ultimaTeclaNum And TECLADO.CodigoIngresado(2)<>99 Then
                Select TECLADO.CodigoIngresado(2)
                    //Codigos del discador
                    Case 1
                        Select TECLADO.CodigoIngresado(1)
                            //Verificar numero de telefono
                            Case 1
                                //si se eligio un numero del 1 al 8....
                                If TECLADO.CodigoIngresado(0)>=1 And TECLADO.CodigoIngresado(0)<=8 Then
                                    MostrarNumeroTelefono(TECLADO.CodigoIngresado(0))
                                EndIf
                            //Verificar alarmas
                            Case 2
                                //si se eligio un numero del 1 al 8....
                                If TECLADO.CodigoIngresado(0)>=1 And TECLADO.CodigoIngresado(0)<=8 Then
                                    MostrarAlarmasTelefono(TECLADO.CodigoIngresado(0))
                                EndIf
                            //Verificar mensajes
                            Case 3
                                If TECLADO.CodigoIngresado(0)>=1 And TECLADO.CodigoIngresado(0)<=4 Then
                                    VerificarMensaje(TECLADO.CodigoIngresado(0)-1)
                                EndIf
                            //Verificar metodo de discado (tonos o pulsos)
                            Case 4
                        End Select
                    //Codigos de los inalambricos
                    Case 2
                        Select TECLADO.CodigoIngresado(1)
                            //Comportamiento
                            Case 2
                                Select TECLADO.CodigoIngresado(0)
                                    //Demoradas
                                    Case 1
                                        MostrarComportamientoZonas(DISPLAY.DisplayComportamientoDemoradas, false, CENTRAL.ZonasInalambricasDemoradas)
                                    //Cancelables
                                    Case 2
                                        MostrarComportamientoZonas(DISPLAY.DisplayComportamientoCancelables, false, CENTRAL.ZonasInalambricasCancelables)
                                    //24 Horas
                                    Case 3
                                        MostrarComportamientoZonas(DISPLAY.DisplayComportamiento24Horas, false, CENTRAL.ZonasInalambricas24Horas)
                                End Select
                        End Select
                    //Codigos de los cableados
                    Case 3
                        Select TECLADO.CodigoIngresado(1)
                            //Especificos
                            Case 1
                                Select TECLADO.CodigoIngresado(0)
                                    //Normal Abiertas
                                    Case 1
                                        MostrarComportamientoZonas(DISPLAY.DisplayComportamientoNormalAbiertas, true, CENTRAL.ZonasNormalAbiertas)
                                End Select
                            //Comportamiento
                            Case 2
                                Select TECLADO.CodigoIngresado(0)
                                    //Demoradas
                                    Case 1
                                        MostrarComportamientoZonas(DISPLAY.DisplayComportamientoDemoradas, true, CENTRAL.ZonasCableadasDemoradas)
                                    //Cancelables
                                    Case 2
                                        MostrarComportamientoZonas(DISPLAY.DisplayComportamientoCancelables, true, CENTRAL.ZonasCableadasCancelables)
                                    //24 Horas
                                    Case 3
                                        MostrarComportamientoZonas(DISPLAY.DisplayComportamiento24Horas, true, CENTRAL.ZonasCableadas24Horas)
                                End Select
                        End Select
                    //Codigos de los tiempos
                    Case 4
                        Select TECLADO.CodigoIngresado(1)
                            //Tiempos de entrada y salida
                            Case 1
                                Select TECLADO.CodigoIngresado(0)
                                    //Tiempo de entrada
                                    Case 1
                                        MostrarTiempos(CENTRAL.TiempoEntrada, 10)
                                    //Tiempo de salida
                                    Case 2
                                        MostrarTiempos(CENTRAL.TiempoSalida, 10)
                                End Select
                            //Otros tiempos
                            Case 2
                                Select TECLADO.CodigoIngresado(0)
                                    //Tiempo de sirenas
                                    Case 1
                                        MostrarTiempos(CENTRAL.TiempoSirenas, 60)
                                End Select
                        End Select
                    //Generales
                    Case 6
                        Select TECLADO.CodigoIngresado(1)
                            //Registros
                            Case 2
                                Select TECLADO.CodigoIngresado(0)
                                    //Registro de disparos
                                    Case 1
                                        MostrarDisparos()
                                End Select
                        End Select
                End Select
                //Reinicio los registros de control del teclado para que no haga otra funcion
                UltimaTeclaPulsada=99
                TECLADO.CodigoIngresado(2)=99
                TimerProgramacion=tiempoprogramacion
            EndIf
        Until (TECLADO.UltimaTeclaPulsada=ultimaTeclaAst And TECLADO.CodigoIngresado(2)=99) Or TimerProgramacion=0
        DISPLAY.ApagarDisplay()
        delay(500)
        beeplargo()
        beeplargo()
        beeplargo()
        TECLADO.EsperarSoltarTeclas()
        TECLADO.tmrPulsandoTeclas=2
    End Sub
}    
    //Inicializacion del modulo
    TimerProgramacion=0
    TimerBorrarInalambricos=0

