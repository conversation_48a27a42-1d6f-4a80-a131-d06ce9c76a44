{
*****************************************************************************
*  Name    : IO.BAS                                                         *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2010 Scanner Alarmas                             *
*          : All Rights Reserved                                            *
*  Date    : 28/01/2010                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

Module IO

Include "System.bas"

    {
    //Constantes publicas
    Public Const
        //Declaracion de los tipos de mensajes que se pueden generar
        TipoMensajeRobo = 0,
        TipoMensajeAsalto = 1,
        TipoMensajeEmergencia = 2,
        TipoMensajeIncendio = 3
        
    }
        
//Declaracion de constantes publicas
Public Const
    #ifdef Quectel
        Version="2.2.7/U-N", 
    #else
        Version="2.2.7/S-N", 
    #endif
    LedEstadoEncendido=1,
    LedEstadoApagado=0,
    LedGSMEncendido=1,
    LedGSMApagado=0,
    SegmentoApagado=1,
    SegmentoEncendido=0,
    SirenaEncendida=1,
    SirenaApagada=0,
    BuzzerEncendido=1,
    BuzzerApagado=0,
    BotonPulsado=0,
    ZonaAntidesarmeActuada=1,
    ZonaPrealarmaActuada=1,
    SalidaActivada=1,
    SalidaDesactivada=0

//Declaracion de variables locales

//Declaracion de variables publicas
Public Dim
    LedEstado   As PORTA.2, 'pin 4
    LedGSM      As PORTD.6, 'pin 29

    SegmentoA   As PORTC.0, 'pin 15
    SegmentoB   As PORTC.1, 'pin 16
    SegmentoC   As PORTC.5, 'pin 24
    SegmentoD   As PORTD.1, 'pin 20
    SegmentoE   As PORTD.0, 'pin 19
    SegmentoF   As PORTD.3, 'pin 22
    SegmentoG   As PORTD.2, 'pin 21
    Punto       As PORTC.3, 'pin 18

    flagSegmentoA As Bit,
    flagSegmentoB As Bit,
    flagSegmentoC As Bit,
    flagSegmentoD As Bit,
    flagSegmentoE As Bit,
    flagSegmentoF As Bit,
    flagSegmentoG As Bit,
    flagPunto As Bit,

    SalidaBuzzer As PORTA.4, 'pin 6

//    SalidaTeclado As PORTA.3, 'pin 5
    SalidaSinAlimentacion As PORTA.3, 'pin 5

    SalidaWala As PORTD.5, 'pin 28
    
//    Linea As PORTE.2,
    
'    SirenaExterior As PORTA.5, 'pin 7
'    SirenaInterior As PORTE.0, 'pin 8
'
'    SalidaTransistor1 As PORTE.1, 'pin 9
'    SalidaTransistor2 As PORTE.2, 'pin 10

    SirenaExterior As PORTE.2, 'pin 10  // Cambiado de PORTE.1
    SirenaInterior As PORTE.1, 'pin 9   // Cambiado de PORTE.2

    SalidaTransistor1 As PORTE.0, 'pin 8
    SalidaTransistor2 As PORTA.5, 'pin 7

    ZonasCableadasDebounce As Byte,
    ZonaCableada1D As ZonasCableadasDebounce.bits(0),
    ZonaCableada2D As ZonasCableadasDebounce.bits(1),
    ZonaCableada3D As ZonasCableadasDebounce.bits(2),
    ZonaCableada4D As ZonasCableadasDebounce.bits(3),
    ZonaCableada5D As ZonasCableadasDebounce.bits(4),
    ZonaCableada6D As ZonasCableadasDebounce.bits(5),
    ZonaCableada7D As ZonasCableadasDebounce.bits(6),
    ZonaCableada8D As ZonasCableadasDebounce.bits(7),
    TimerZonaCableadaH(8) As Byte,
    TimerZonaCableadaL(8) As Byte,
    TimerZonaAntidesarme As Byte,
    ZonasCableadas As PORTB,
    ZonaCableada1 As ZonasCableadas.bits(0),
    ZonaCableada2 As ZonasCableadas.bits(1),
    ZonaCableada3 As ZonasCableadas.bits(2),
    ZonaCableada4 As ZonasCableadas.bits(3),
    ZonaCableada5 As ZonasCableadas.bits(4),
    ZonaCableada6 As ZonasCableadas.bits(5),
    ZonaCableada7 As ZonasCableadas.bits(6),
    ZonaCableada8 As ZonasCableadas.bits(7),
    
    ZonaPrealarma As PORTE.3, 'pin 1
    ZonaAntidesarme As PORTC.4, 'pin 23
    ZonaAntidesarmeDebounce As Bit, 'pin 23
    DeteccionAlimentacion As PORTD.4, 'pin 27
    
    PowerKey As PORTD.7, 'pin 30

    TxPC As PORTA.6, 'pin 14
    RxPC As PORTA.7, 'pin 13

    ConectadoPC As Boolean,                               //Indicacion de que se encuantra conectado a la pc

//    FPWMPin As PORTC.2,

//    do As PORTC.5,
//    clk As PORTC.3,
//    cs As PORTD.7,

    btnProg As PORTA.0, 'pin 2
    btnSel As PORTA.1, 'pin 3
    
    tmrBtnProg As Word,
    tmrBtnSel As Word,
    
    TiempoBtnProgPulsado As Word,
    TiempoBtnSelPulsado As Word,
    
    btnProgPulsado As Boolean,
    btnSelPulsado As Boolean,
    
    btnProgSoltado As Boolean,
    btnSelSoltado As Boolean,
    
    flagParpadearLedEstado500ms As Boolean,
    flagParpadearLedEstado250ms As Boolean,
    flagParpadearLedGSM500ms As Boolean,
    flagParpadearLedGSM250ms As Boolean,
    flagParpadearLedGSM100ms As Boolean,
    flagParpadearLedGSM50ms As Boolean,

    TimerDelay As Word,                             //Indica el tiempo de espera para un delay generico que no interfier con las interrupts (ms - multiplos de 10)

    Tick As Word                                       //Tick que incrementa cada 1 segundo, freerun (desborda en 65535, aprox 18hs)

    Public Sub Delay(ms As Word)
        TimerDelay=ms
        While TimerDelay>0
        Wend
    End Sub

    Public Sub EncenderLedEstado()
        flagParpadearLedEstado500ms=false
        flagParpadearLedEstado250ms=false
        LedEstado=LedEstadoEncendido
    End Sub

    Public Sub ApagarLedEstado()
        flagParpadearLedEstado500ms=false
        flagParpadearLedEstado250ms=false
        LedEstado=LedEstadoApagado
    End Sub
    
    Public Sub ParpadearLedEstado500ms()
        flagParpadearLedEstado250ms=false
        flagParpadearLedEstado500ms=true
    End Sub

    Public Sub ParpadearLedEstado250ms()
        flagParpadearLedEstado500ms=false
        flagParpadearLedEstado250ms=true
    End Sub
    
    Public Sub ApagarLedGSM()
        flagParpadearLedGSM500ms=false
        flagParpadearLedGSM250ms=false
        flagParpadearLedGSM100ms=false
        flagParpadearLedGSM50ms=false
        LedGSM=LedGSMApagado
    End Sub

    Public Sub EncenderLedGSM()
        flagParpadearLedGSM500ms=false
        flagParpadearLedGSM250ms=false
        flagParpadearLedGSM100ms=false
        flagParpadearLedGSM50ms=false
        LedGSM=LedGSMEncendido
    End Sub
    
    Public Sub ParpadearLedGSM500ms()
        flagParpadearLedGSM250ms=false
        flagParpadearLedGSM100ms=false
        flagParpadearLedGSM50ms=false
        flagParpadearLedGSM500ms=true
    End Sub
    
    Public Sub ParpadearLedGSM250ms()
        flagParpadearLedGSM500ms=false
        flagParpadearLedGSM100ms=false
        flagParpadearLedGSM50ms=false
        flagParpadearLedGSM250ms=true
    End Sub
    
    Public Sub ParpadearLedGSM100ms()
        flagParpadearLedGSM500ms=false
        flagParpadearLedGSM250ms=false
        flagParpadearLedGSM50ms=false
        flagParpadearLedGSM100ms=true
    End Sub
    
    Public Sub ParpadearLedGSM50ms()
        flagParpadearLedGSM500ms=false
        flagParpadearLedGSM250ms=false
        flagParpadearLedGSM100ms=false
        flagParpadearLedGSM50ms=true
    End Sub
    
    Public Sub EsperarSoltarbtnProg()
        Repeat
            ClrWDT()
        Until Not btnProgPulsado
    End Sub

    Public Sub EsperarSoltarbtnSel()
        Repeat
            ClrWDT()
        Until Not btnSelPulsado
    End Sub

flagParpadearLedEstado500ms=false
flagParpadearLedEstado250ms=false
flagParpadearLedGSM500ms=false
flagParpadearLedGSM250ms=false
flagParpadearLedGSM100ms=false
flagParpadearLedGSM50ms=false

btnProgPulsado=false
btnSelPulsado=false
btnProgSoltado=false
btnSelSoltado=false

tmrBtnProg=0
tmrBtnSel=0
TiempoBtnProgPulsado=0
TiempoBtnSelPulsado=0
    

Low(LedEstado)
Low(LedGSM)

'flagSegmentoA=IO.SegmentoApagado
'flagSegmentoB=IO.SegmentoApagado
'flagSegmentoC=IO.SegmentoApagado
'flagSegmentoD=IO.SegmentoApagado
'flagSegmentoE=IO.SegmentoApagado
'flagSegmentoF=IO.SegmentoApagado
'flagSegmentoG=IO.SegmentoApagado
'flagPunto=IO.SegmentoApagado

High(SegmentoA)
High(SegmentoB)
High(SegmentoC)
High(SegmentoD)
High(SegmentoE)
High(SegmentoF)
High(SegmentoG)
High(Punto)

'Low(SalidaBuzzer)
Low(SalidaBuzzer)

Low(SirenaInterior)
Low(SirenaExterior)

Low(SalidaTransistor1)
Low(SalidaTransistor2)

Low(SalidaSinAlimentacion)
Low(PowerKey)

Input(ZonaAntidesarme)
Input(ZonaPrealarma)

INTCON2.7=0

'Cargo este valor para asegurarme de que pase por la rutina que vacia los mensajes viejos de la memoria del sim (tick<30)
Tick=65450

ZonasCableadasDebounce=ZonasCableadas
