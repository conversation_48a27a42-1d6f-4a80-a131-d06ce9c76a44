{
*****************************************************************************
*  Name    : Display.BAS                                                   *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2010 Scanner Alarmas                             *
*          : All Rights Reserved                                            *
*  Date    : 22/01/2010                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

Module DISPLAY

Include "IO.bas"
Include "BUZZER.bas"
//Declaracion de variables privadas

//Declaracion de variables publicas
Public Dim
    flagParpadearDisplay As Boolean,
    flagParpadearPunto As Boolean

//Declaracion de constantes
Const
    Caracter0=48,
    <PERSON><PERSON><PERSON>=49,
    <PERSON><PERSON><PERSON>=50,
    <PERSON><PERSON><PERSON>=51,
    <PERSON><PERSON><PERSON>=52,
    <PERSON><PERSON><PERSON>=53,
    <PERSON><PERSON><PERSON>=54,
    <PERSON><PERSON>7=55,
    <PERSON><PERSON>8=56,
    <PERSON><PERSON>9=57

//Declaracion de constantes publicas
Public Const
    DisplayNada=$FF,
    DisplayAsterisco=10,
    DisplayPausa=11,
    DisplayInterrogacion=12,
    DisplayRemoto=13,
    DisplayRobo=14,                     //Igual a displayremoto
    DisplayIncendio=15,
    DisplayEmergencia=16,
    DisplaySensoresRobo=17,
    DisplayProgramacion=18,
    DisplayBorrar=19,
    DisplayAsalto=20,
    DisplayMetodoMarcadoTonos=21,
    DisplayMetodoMarcadoPulsos=22,
    DisplayComportamientoDemoradas=23,
    DisplayComportamientoCancelables=24,
    DisplayComportamiento24Horas=25,
    DisplayComportamientoNormalAbiertas=26,
    DisplaySirenaInterior=27,
    DisplaySirenaExterior=28,
    DisplayAmbasSirenas=29,
    DisplayAntidesarme=30,
    DisplayT=31,
    DisplayE=32,
    DisplayN=33,
    DisplayS=34,
    DisplayI=35,
    DisplayA=36,
    DisplayC=37,
    DisplayR=38,
    DisplayD=39,
    DisplayH=40,
    DisplayB=41,
    DisplayU=42,
    DisplayL=43,
    DisplayP=44

Public Sub MostrarDigito(digito As Byte)
    Select digito
        Case 0, Caracter0
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoApagado
        Case 1, Caracter1
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoApagado
            IO.flagSegmentoF=IO.SegmentoApagado
            IO.flagSegmentoG=IO.SegmentoApagado
        Case 2, Caracter2
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoApagado
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoApagado
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case 3, Caracter3
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoApagado
            IO.flagSegmentoF=IO.SegmentoApagado
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case 4, Caracter4
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoApagado
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case 5, Caracter5
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoApagado
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case 6, Caracter6
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case 7, Caracter7
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoApagado
            IO.flagSegmentoF=IO.SegmentoApagado
            IO.flagSegmentoG=IO.SegmentoApagado
        Case 8, Caracter8, DisplayAmbasSirenas
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case 9, Caracter9
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoApagado
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
'        Case DisplayAst
'            IO.flagSegmentoC=IO.SegmentoEncendido
'            IO.flagSegmentoD=IO.SegmentoEncendido
'            IO.flagSegmentoE=IO.SegmentoEncendido
'            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayInterrogacion
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoApagado
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoApagado
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayRemoto, DisplayRobo, DisplayR
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoApagado
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoApagado
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayIncendio, DisplayI
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoApagado
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoApagado
        Case DisplayEmergencia, DisplayComportamientoNormalAbiertas, DisplayE
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoApagado
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplaySensoresRobo, DisplayS
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoApagado
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayPausa, DisplayProgramacion, DisplayMetodoMarcadoPulsos, DisplayP
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoApagado
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayBorrar, DisplayB
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayAsterisco, DisplaySirenaExterior
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoApagado
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayAsalto, DisplayA
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayMetodoMarcadoTonos, DisplayT, DisplayAntidesarme
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoApagado
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayComportamientoDemoradas, DisplayD
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoApagado
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayComportamientoCancelables, DisplayC
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoApagado
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoApagado
        Case DisplayComportamiento24Horas, DisplayH
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplaySirenaInterior
            IO.flagSegmentoA=IO.SegmentoEncendido
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoApagado
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoApagado
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayN
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoApagado
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoApagado
            IO.flagSegmentoG=IO.SegmentoEncendido
        Case DisplayU
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoEncendido
            IO.flagSegmentoC=IO.SegmentoEncendido
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoApagado
        Case DisplayL
            IO.flagSegmentoA=IO.SegmentoApagado
            IO.flagSegmentoB=IO.SegmentoApagado
            IO.flagSegmentoC=IO.SegmentoApagado
            IO.flagSegmentoD=IO.SegmentoEncendido
            IO.flagSegmentoE=IO.SegmentoEncendido
            IO.flagSegmentoF=IO.SegmentoEncendido
            IO.flagSegmentoG=IO.SegmentoApagado
        Else
            IO.flagSegmentoA=digito.0
            IO.flagSegmentoB=digito.1
            IO.flagSegmentoC=digito.2
            IO.flagSegmentoD=digito.3
            IO.flagSegmentoE=digito.4
            IO.flagSegmentoF=digito.5
            IO.flagSegmentoG=digito.6
            IO.flagPunto=digito.7
    EndSelect
End Sub

Public Sub ApagarDisplay()
    MostrarDigito(DisplayNada)
    flagParpadearDisplay=false
End Sub

Public Sub EncenderPunto()
        flagParpadearPunto=false
        IO.flagPunto=IO.SegmentoEncendido
    End Sub

    Public Sub ApagarPunto()
        flagParpadearPunto=false
        IO.flagPunto=IO.SegmentoApagado
    End Sub
    
    Public Sub ParpadearPunto()
        flagParpadearPunto=true
    End Sub

    Public Sub SecuenciaInicio()
        IO.flagSegmentoA=IO.SegmentoEncendido
        BeepCorto()
        IO.flagSegmentoA=IO.SegmentoApagado
        IO.flagSegmentoB=IO.SegmentoEncendido
        BeepCorto()
        IO.flagSegmentoB=IO.SegmentoApagado
        IO.flagSegmentoC=IO.SegmentoEncendido
        BeepCorto()
        IO.flagSegmentoC=IO.SegmentoApagado
        IO.flagSegmentoD=IO.SegmentoEncendido
        BeepCorto()
        IO.flagSegmentoD=IO.SegmentoApagado
        IO.flagSegmentoE=IO.SegmentoEncendido
        BeepCorto()
        IO.flagSegmentoE=IO.SegmentoApagado
        IO.flagSegmentoF=IO.SegmentoEncendido
        BeepCorto()
        IO.flagSegmentoF=IO.SegmentoApagado
        IO.flagSegmentoG=IO.SegmentoEncendido
        BeepCorto()
        IO.flagSegmentoG=IO.SegmentoApagado

'    BUZZER.BeepLargo()
'    MostrarDigito(8)
'    LedEstado=LedEstadoEncendido
'    LedGSM=LedGSMEncendido
'    Delay(200)
'    ApagarDisplay()
'    LedEstado=LedEstadoApagado
'    LedGSM=LedGSMApagado
'    Delay(100)
'    MostrarDigito(8)
'    LedEstado=LedEstadoEncendido
'    LedGSM=LedGSMEncendido
'    Delay(200)
'    ApagarDisplay()
'    LedEstado=LedEstadoApagado
'    LedGSM=LedGSMApagado
'    Delay(100)
'    MostrarDigito(8)
'    LedEstado=LedEstadoEncendido
'    LedGSM=LedGSMEncendido
'    Delay(200)
'    ApagarDisplay()
'    LedEstado=LedEstadoApagado
'    LedGSM=LedGSMApagado
'    Delay(100)
'    BeepLargo()
End Sub



//Inicializacion del modulo
flagParpadearDisplay=false
flagParpadearPunto=false
