{
*****************************************************************************
*  Name    : UNTITLED.BAS                                                   *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2010 Scanner Alarmas                             *
*          : All Rights Reserved                                            *
*  Date    : 19/02/2010                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

Module DISPAROS

    //Modulos relacionados
    Include "IO.bas"
    Include "CENTRAL.bas"
//    Include "DTMF.bas"
    Include "ROM.bas"
//    Include "MEMORIA.bas"
    Include "TIMERS.bas"
    
    //Declaracion de constantes privadas
    
    //Declaracion de varibles privadas
        
    //Declaracion de variables publicas 

    //Procesamiento de los disparos discriminados para cada tipo
    Public Sub ProcesarDisparos()

        If CENTRAL.TimerSirenas>0 Then
            IO.SirenaInterior=IO.SirenaEncendida
            IO.SirenaExterior=IO.SirenaEncendida
        Else
            If TimerPrealarma>0 Then
                If ContadorBeepsSirenas>0 Then
                    If IO.SirenaExterior=IO.SirenaApagada Then
                        If TimerBeepsSirenas=0 Then
                            IO.SirenaExterior=IO.SirenaEncendida
                            IO.SalidaBuzzer=1
                            TimerBeepsSirenas=250
                        EndIf
                    Else
                        If TimerBeepsSirenas=0 Then
                            IO.SirenaExterior=IO.SirenaApagada
                            IO.SalidaBuzzer=0
                            TimerBeepsSirenas=200
                            Dec(ContadorBeepsSirenas)
                        EndIf
                    EndIf
                EndIf
            ElseIf CENTRAL.Estado<> CENTRAL.EstadoCentralPanico Then
                IO.SirenaInterior=IO.SirenaApagada
                IO.SirenaExterior=IO.SirenaApagada
            EndIf
        EndIf
        
        //Controlo si hay que guardar un disparo en memoria eeprom
        If CENTRAL.flagDisparoSinGuardar Then
            ROM.GuardarDisparo()
            CENTRAL.flagDisparoSinGuardar=false
        EndIf
        
    End Sub
    
    //Inicializacion del modulo
