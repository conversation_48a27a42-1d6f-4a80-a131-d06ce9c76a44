{
*****************************************************************************
*  Name    : XTEA                                                           *
*  Notes   : The Tiny Encryption Algorithm (TEA) is a very fast and         *
*          : efficient cryptographic algorithm. It was developed by <PERSON> and <PERSON> at the Computer Laboratory at        *
*          : Cambridge University. XTEA is a more secure variant of TEA     *
*****************************************************************************
}

// if device and clock are omitted, then the compiler defaults to 
// 18F452 @ 20MHz - they are just used here for clarity...
device = 18F452
clock = 20

// uses USART and string libraries...  
include "USART.bas"
include "String.bas"
include "convert.bas"

// golden ratio
const Delta = $9E3779B9 

// XTEA encrypt a 64 bit data block with 128 bit key... 
sub access XTeaEncrypt(byref pData() as longword, byref pKey() as longword, pNumCycles as longword = 32)
   dim Y,Z,Sum,Limit as longword
   Y = pData(0)
   Z = pData(1)
   Sum = 0 
   Limit = Delta * pNumCycles
   while Sum <> Limit
      inc(Y,((Z << 4) xor (Z >> 5)) xor (Sum + pKey(sum and 3)))
      inc(Sum,Delta)
      inc(Z,((Y << 4) xor (Y >> 5)) xor (Sum + pKey((Sum >> 11) and 3)))
   wend
   pData(0) = Y
   pData(1) = Z
end sub

// XTEA decrypt a 64 bit data block with 128 bit key... 
sub access XTeaDecrypt(byref pData() as longword, byref pKey() as longword, pNumCycles as longword = 32)
   dim Y,Z,Sum as longword
   Y = pData(0)
   Z = pData(1)
   Sum = Delta * pNumCycles
   while Sum <> 0 
      dec(Z,((Y << 4) xor (Y >> 5)) xor (Sum + pKey((Sum >> 11) and 3)))
      dec(Sum,Delta)
      dec(Y,((Z << 4) xor (Z >> 5)) xor (Sum + pKey(Sum and 3)))
   wend 
   pData(0) = Y
   pData(1) = Z
end sub

// convert a longword key into an XTea key...
sub MakeKey(pKeyOrd as longword, byref pKey() as longword)
   dim Index as byte
   for Index = 0 to bound(pKey)
      pKey(Index) = pKeyOrd
   next   
end sub

// encode a byte array using a string key - XTea is a block cipher, which
// means only data that falls within an 8 byte block will be encrypted. 
public sub Encode(pKey as longword, byref pData() as byte)
   dim Index as word        // loop counter
   dim Key(4) as longword   // 128 bit cipher key
   dim Block(2) as longword // 64 bit data block
   MakeKey(pKey,Key)
   Index = 0
   while word(Index + 7) <= bound(pData)
      Block(0) = pData(Index).AsLongWord
      Block(1) = pData(Index + 4).AsLongWord
	   XTeaEncrypt(Block, Key)
      pData(index).AsLongWord = Block(0)
      pData(Index + 4).AsLongWord = Block(1)
      inc(index, 8)
   wend
end sub

// decode a byte array using a string key - XTea is a block cipher, which
// means only data that falls within an 8 byte block will be decrypted.
public sub Decode(pKey as longword, byref pData() as byte)
   dim Index as word        // loop counter
   dim Key(4) as longword   // 128 bit cipher key
   dim Block(2) as longword // 64 bit data block  
   MakeKey(pKey,Key)
   Index = 0
   while word(Index + 7) <= bound(pData)
      Block(0) = pData(Index).AsLongWord
      Block(1) = pData(Index + 4).AsLongWord
      XTeaDecrypt(Block, Key)
      pData(index).AsLongWord = Block(0)
      pData(Index + 4).AsLongWord = Block(1)
      inc(index, 8)
   wend
end sub

// print a data array...
sub PrintData(byref pData() as byte)
   dim Index as byte
   dim WrapCount as byte
   
   WrapCount = 0
   for Index = 0 to bound(pData)
      if WrapCount > 7 then
         WrapCount = 0
         USART.Write(13, 10)
      endif
      inc(WrapCount)
	  USART.Write("$", HexToStr(pData(Index), 2), " ")
   next
   USART.Write(13, 10)
end sub

// initialise an array with some data...
sub InitData(byref pData() as byte)
   dim Index as byte
   for Index = 0 to bound(pData)
      pData(Index) = Index
   next
end sub

// array of bytes to be encrypted...
dim Data(16) as byte 

// program start
SetBaudrate(br19200)
USART.Write("Initialising...", 13, 10)
InitData(Data)
PrintData(Data)
USART.Write("Encrypted Data...", 13, 10)
Encode(1234, Data)
PrintData(Data)
USART.Write("Decrypted Data...", 13, 10)
Decode(1234, Data)
PrintData(Data)


