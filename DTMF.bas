{
*****************************************************************************
*  Name    : PWM.BAS                                                        *
*  Author  : <PERSON>                                              *
*  Notice  : Copyright (c) 2007 Mecanique                                   *
*          : All Rights Reserved                                            *
*  Date    : 23/08/2007                                                     *
*  Version : 1.0                                                            *
*  Notes   : From an idea found at http://www.eng-serve.net/pic             *                    
*          :                                                                *
*****************************************************************************
}
Module DTMF

Include "IO.bas"
Include "PWM.bas"
Include "Math.bas"
Include "String.bas"
Include "ROM.bas"
Include "CENTRAL.bas"

    //Declaracion de variables locales
    
    //Declaracion de variables publicas
    
    Const
       SinTable(32) As Byte = (16,19,22,24,27,28,30,31,31,31,30,28,27,24,22,19,16,13,10,8,5,4,2,1,1,1,2,4,5,8,10,13),
       Fila1=697*1000/560.0,
       Fila2=770*1000/560.0,
       Fila3=852*1000/560.0,
       Fila4=941*1000/560.0,
       Columna1=1209*1000/560.0,
       Columna2=1336*1000/560.0,
       Columna3=1477*1000/560.0
    
    Public Const
        Digito0="0",
        Digito1="1",
        Digito2="2",
        Digito3="3",
        Digito4="4",
        Digito5="5",
        Digito6="6",
        Digito7="7",
        Digito8="8",
        Digito9="9",
        DigitoAST="*",
        DigitoPAUSA="P"
        
    
    Public Sub DiscarDigito(digito As Char)
        Dim
           aStep, bStep As Word,
           PhaseAccumA As Word, 'use these as a 4.12 (15 bit total)
           PhaseAccumB As Word, 'phase accumulators
           Temp As Byte,
           Contador As Word,
           CantidadPulsos As Byte,
           ContadorPulsos As Byte
        Dim
           BitGIE As Bit
    
        'adjust these values when loop code is finished as the
        'output frequency depends on Repeat / Until loop timing   500=1120
        Select digito
            Case Digito0
                aStep = Columna2
                bStep = Fila4
            Case Digito1
                aStep = Columna1
                bStep = Fila1
            Case Digito2
                aStep = Columna2
                bStep = Fila1
            Case Digito3
                aStep = Columna3
                bStep = Fila1
            Case Digito4
                aStep = Columna1
                bStep = Fila2
            Case Digito5
                aStep = Columna2
                bStep = Fila2
            Case Digito6
                aStep = Columna3
                bStep = Fila2
            Case Digito7
                aStep = Columna1
                bStep = Fila3
            Case Digito8
                aStep = Columna2
                bStep = Fila3
            Case Digito9
                aStep = Columna3
                bStep = Fila3
            Case DigitoAST
                aStep = Columna1
                bStep = Fila4
            Case DigitoPAUSA
                Delay(500)
        EndSelect
    
        If digito <> DigitoPAUSA Then
            If CENTRAL.MetodoMarcado=CENTRAL.MetodoMarcadoTonos Then
                PhaseAccumA = 0
                PhaseAccumB = 0
                PWM.Start
                //Guardo el estado del flag de interrupcion y las deshabilito
                BitGIE=intcon.7
                INTCON.7=0
                For contador=0 To 3667
                   PhaseAccumA = PhaseAccumA + aStep
                   PhaseAccumB = PhaseAccumB + bStep
                   Temp = SinTable(PhaseAccumA >> 11)/2
                   Temp = Temp + SinTable(PhaseAccumB >> 11)/2
                   PWM.SetDuty(Temp<<3)
                Next
                //Restauro el flag de interrupcion
                INTCON.7=BitGIE
                PWM.Stop
            Else
                If digito<> DigitoAST Then
                    CantidadPulsos=digito 
                    Dec(CantidadPulsos, 48)
                    If CantidadPulsos=0 Then
                        CantidadPulsos=10
                    EndIf
                
                    For ContadorPulsos=1 To CantidadPulsos
                        IO.Linea=0
                        Delay(60)
                        IO.Linea=1
                        Delay(40)
                    Next
                    Delay(700)
                EndIf
            EndIf
        EndIf
        Delay(50)
    End Sub
    
    //Genera el discado de prueba, toma de linea y disca el uno
    Public Sub DiscarVerificacion()
        IO.Linea=1
        Delay(1000)
        DTMF.DiscarDigito(DTMF.Digito1)
        'delay(500)
        'MEMORIA.ReproducirMensaje(0)
    End Sub
    
    Public Sub DiscarNumero(numero As Byte)
        Dim Contador As Byte
        Dim Telefono As String(16)
        
        IO.Linea=1
        Delay(1000)
        telefono=ROM.LeerTelefono(numero)
        For contador = 0 To Length(telefono)-1
            DiscarDigito(telefono(contador))
        Next
    End Sub
    
    Public Sub Colgar()
        IO.Linea=0
    End Sub
    
    // initialise the module
    
