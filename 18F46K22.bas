{
****************************************************************
*  Name    : 18F46K22                                          *
*  Author  : <PERSON>                                 *
*  Notice  : Copyright (c) 2011 Mecanique                      *
*          : All Rights Reserved                               *
*  Date    : 10/08/2011                                        *
****************************************************************
}

Module SystemTypes

// system header...
#const _core = $0012                   // processor core
#const _ram_banks = $10                // 16 RAM bank(s) used
#variable _maxaccess = $60             // access ram is 96 bytes
#variable _maxram = $0F60              // 3936 bytes of user RAM
#variable _maxrom = $010000            // 64 KB of program ROM
#const _eeprom = $0400                 // 1024 bytes of EEPROM
#const _eeprom_start = $F00000         // EEPROM start address
#const _ports = $05                    // 5 available ports
#const _ccp = $02                      // 2 CCP module(s) available
#const _eccp = $03                     // 3 ECCP module(s) available
#const _mssp = $02                     // 2 MSSP module(s) available
#const _usart = $02                    // 2 USART(s) available
#const _adc = $10                      // 16 ADC channels available
#const _adres = $0A                    // 10 bit ADC resolution
#const _comparator = $02               // 2 comparator(s) available
#const _psp = $00                      // Parallel Slave Port (PSP) is NOT supported
#const _can = $00                      // onboard CAN is NOT supported
#const _usb = $00                      // USB is NOT supported
#const _ethernet = $00                 // onboard Ethernet is NOT supported
#const _flash_write = $01              // FLASH has write capability

// special function registers...
Public System
   ANSELA As Byte Absolute $0F38,
   ANSELB As Byte Absolute $0F39,
   ANSELC As Byte Absolute $0F3A,
   ANSELD As Byte Absolute $0F3B,
   ANSELE As Byte Absolute $0F3C,
   PMD2 As Byte Absolute $0F3D,
   PMD1 As Byte Absolute $0F3E,
   PMD0 As Byte Absolute $0F3F,
   DACCON1 As Byte Absolute $0F40,
   VREFCON2 As Byte Absolute $0F40,
   DACCON0 As Byte Absolute $0F41,
   VREFCON1 As Byte Absolute $0F41,
   FVRCON As Byte Absolute $0F42,
   VREFCON0 As Byte Absolute $0F42,
   CTMUICON As Byte Absolute $0F43,
   CTMUICONH As Byte Absolute $0F43,
   CTMUCON1 As Byte Absolute $0F44,
   CTMUCONL As Byte Absolute $0F44,
   CTMUCON0 As Byte Absolute $0F45,
   CTMUCONH As Byte Absolute $0F45,
   SRCON1 As Byte Absolute $0F46,
   SRCON0 As Byte Absolute $0F47,
   CCPTMRS1 As Byte Absolute $0F48,
   CCPTMRS0 As Byte Absolute $0F49,
   T6CON As Byte Absolute $0F4A,
   PR6 As Byte Absolute $0F4B,
   TMR6 As Byte Absolute $0F4C,
   T5GCON As Byte Absolute $0F4D,
   T5CON As Byte Absolute $0F4E,
   TMR5L As Byte Absolute $0F4F,
   TMR5H As Byte Absolute $0F50,
   T4CON As Byte Absolute $0F51,
   PR4 As Byte Absolute $0F52,
   TMR4 As Byte Absolute $0F53,
   CCP5CON As Byte Absolute $0F54,
   CCPR5 As Byte Absolute $0F55,
   CCPR5L As Byte Absolute $0F55,
   CCPR5H As Byte Absolute $0F56,
   CCP4CON As Byte Absolute $0F57,
   CCPR4 As Byte Absolute $0F58,
   CCPR4L As Byte Absolute $0F58,
   CCPR4H As Byte Absolute $0F59,
   PSTR3CON As Byte Absolute $0F5A,
   CCP3AS As Byte Absolute $0F5B,
   ECCP3AS As Byte Absolute $0F5B,
   PWM3CON As Byte Absolute $0F5C,
   CCP3CON As Byte Absolute $0F5D,
   CCPR3 As Byte Absolute $0F5E,
   CCPR3L As Byte Absolute $0F5E,
   CCPR3H As Byte Absolute $0F5F,
   SLRCON As Byte Absolute $0F60,
   WPUB As Byte Absolute $0F61,
   IOCB As Byte Absolute $0F62,
   PSTR2CON As Byte Absolute $0F63,
   CCP2AS As Byte Absolute $0F64,
   ECCP2AS As Byte Absolute $0F64,
   PWM2CON As Byte Absolute $0F65,
   CCP2CON As Byte Absolute $0F66,
   CCPR2 As Byte Absolute $0F67,
   CCPR2L As Byte Absolute $0F67,
   CCPR2H As Byte Absolute $0F68,
   SSP2CON3 As Byte Absolute $0F69,
   SSP2MSK As Byte Absolute $0F6A,
   SSP2CON2 As Byte Absolute $0F6B,
   SSP2CON1 As Byte Absolute $0F6C,
   SSP2STAT As Byte Absolute $0F6D,
   SSP2ADD As Byte Absolute $0F6E,
   SSP2BUF As Byte Absolute $0F6F,
   BAUD2CON As Byte Absolute $0F70,
   BAUDCON2 As Byte Absolute $0F70,
   RC2STA As Byte Absolute $0F71,
   RCSTA2 As Byte Absolute $0F71,
   TX2STA As Byte Absolute $0F72,
   TXSTA2 As Byte Absolute $0F72,
   TX2REG As Byte Absolute $0F73,
   TXREG2 As Byte Absolute $0F73,
   RC2REG As Byte Absolute $0F74,
   RCREG2 As Byte Absolute $0F74,
   SP2BRG As Byte Absolute $0F75,
   SPBRG2 As Byte Absolute $0F75,
   SP2BRGH As Byte Absolute $0F76,
   SPBRGH2 As Byte Absolute $0F76,
   CM12CON As Byte Absolute $0F77,
   CM2CON1 As Byte Absolute $0F77,
   CM2CON As Byte Absolute $0F78,
   CM2CON0 As Byte Absolute $0F78,
   CM1CON As Byte Absolute $0F79,
   CM1CON0 As Byte Absolute $0F79,
   PIE4 As Byte Absolute $0F7A,
   PIR4 As Byte Absolute $0F7B,
   IPR4 As Byte Absolute $0F7C,
   PIE5 As Byte Absolute $0F7D,
   PIR5 As Byte Absolute $0F7E,
   IPR5 As Byte Absolute $0F7F,
   LATA As Byte Absolute $0F89,
   LATB As Byte Absolute $0F8A,
   LATC As Byte Absolute $0F8B,
   LATD As Byte Absolute $0F8C,
   LATE As Byte Absolute $0F8D,
   DDRA As Byte Absolute $0F92,
   TRISA As Byte Absolute $0F92,
   DDRB As Byte Absolute $0F93,
   TRISB As Byte Absolute $0F93,
   DDRC As Byte Absolute $0F94,
   TRISC As Byte Absolute $0F94,
   DDRD As Byte Absolute $0F95,
   TRISD As Byte Absolute $0F95,
   DDRE As Byte Absolute $0F96,
   TRISE As Byte Absolute $0F96,
   OSCTUNE As Byte Absolute $0F9B,
   HLVDCON As Byte Absolute $0F9C,
   LVDCON As Byte Absolute $0F9C,
   PIE1 As Byte Absolute $0F9D,
   PIR1 As Byte Absolute $0F9E,
   IPR1 As Byte Absolute $0F9F,
   PIE2 As Byte Absolute $0FA0,
   PIR2 As Byte Absolute $0FA1,
   IPR2 As Byte Absolute $0FA2,
   PIE3 As Byte Absolute $0FA3,
   PIR3 As Byte Absolute $0FA4,
   IPR3 As Byte Absolute $0FA5,
   EECON1 As Byte Absolute $0FA6,
   EECON2 As Byte Absolute $0FA7,
   EEDATA As Byte Absolute $0FA8,
   EEADR As Byte Absolute $0FA9,
   EEADRH As Byte Absolute $0FAA,
   RC1STA As Byte Absolute $0FAB,
   RCSTA As Byte Absolute $0FAB,
   RCSTA1 As Byte Absolute $0FAB,
   TX1STA As Byte Absolute $0FAC,
   TXSTA As Byte Absolute $0FAC,
   TXSTA1 As Byte Absolute $0FAC,
   TX1REG As Byte Absolute $0FAD,
   TXREG As Byte Absolute $0FAD,
   TXREG1 As Byte Absolute $0FAD,
   RC1REG As Byte Absolute $0FAE,
   RCREG As Byte Absolute $0FAE,
   RCREG1 As Byte Absolute $0FAE,
   SP1BRG As Byte Absolute $0FAF,
   SPBRG As Byte Absolute $0FAF,
   SPBRG1 As Byte Absolute $0FAF,
   SP1BRGH As Byte Absolute $0FB0,
   SPBRGH As Byte Absolute $0FB0,
   SPBRGH1 As Byte Absolute $0FB0,
   T3CON As Byte Absolute $0FB1,
   TMR3L As Byte Absolute $0FB2,
   TMR3H As Byte Absolute $0FB3,
   T3GCON As Byte Absolute $0FB4,
   ECCP1AS As Byte Absolute $0FB6,
   ECCPAS As Byte Absolute $0FB6,
   PWM1CON As Byte Absolute $0FB7,
   PWMCON As Byte Absolute $0FB7,
   BAUD1CON As Byte Absolute $0FB8,
   BAUDCON As Byte Absolute $0FB8,
   BAUDCON1 As Byte Absolute $0FB8,
   BAUDCTL As Byte Absolute $0FB8,
   PSTR1CON As Byte Absolute $0FB9,
   PSTRCON As Byte Absolute $0FB9,
   T2CON As Byte Absolute $0FBA,
   PR2 As Byte Absolute $0FBB,
   TMR2 As Byte Absolute $0FBC,
   CCP1CON As Byte Absolute $0FBD,
   CCPR1 As Byte Absolute $0FBE,
   CCPR1L As Byte Absolute $0FBE,
   CCPR1H As Byte Absolute $0FBF,
   ADCON2 As Byte Absolute $0FC0,
   ADCON1 As Byte Absolute $0FC1,
   ADCON0 As Byte Absolute $0FC2,
   ADRES As Byte Absolute $0FC3,
   ADRESL As Byte Absolute $0FC3,
   ADRESH As Byte Absolute $0FC4,
   SSP1CON2 As Byte Absolute $0FC5,
   SSPCON2 As Byte Absolute $0FC5,
   SSP1CON1 As Byte Absolute $0FC6,
   SSPCON1 As Byte Absolute $0FC6,
   SSP1STAT As Byte Absolute $0FC7,
   SSPSTAT As Byte Absolute $0FC7,
   SSP1ADD As Byte Absolute $0FC8,
   SSPADD As Byte Absolute $0FC8,
   SSP1BUF As Byte Absolute $0FC9,
   SSPBUF As Byte Absolute $0FC9,
   SSP1MSK As Byte Absolute $0FCA,
   SSPMSK As Byte Absolute $0FCA,
   SSP1CON3 As Byte Absolute $0FCB,
   SSPCON3 As Byte Absolute $0FCB,
   T1GCON As Byte Absolute $0FCC,
   T1CON As Byte Absolute $0FCD,
   TMR1L As Byte Absolute $0FCE,
   TMR1H As Byte Absolute $0FCF,
   RCON As Byte Absolute $0FD0,
   WDTCON As Byte Absolute $0FD1,
   OSCCON2 As Byte Absolute $0FD2,
   OSCCON As Byte Absolute $0FD3,
   T0CON As Byte Absolute $0FD5,
   TMR0L As Byte Absolute $0FD6,
   TMR0H As Byte Absolute $0FD7,
   STATUS As Byte Absolute $0FD8,
   FSR2L As Byte Absolute $0FD9,
   FSR2H As Byte Absolute $0FDA,
   PLUSW2 As Byte Absolute $0FDB,
   PREINC2 As Byte Absolute $0FDC,
   POSTDEC2 As Byte Absolute $0FDD,
   POSTINC2 As Byte Absolute $0FDE,
   INDF2 As Byte Absolute $0FDF,
   BSR As Byte Absolute $0FE0,
   FSR1L As Byte Absolute $0FE1,
   FSR1H As Byte Absolute $0FE2,
   PLUSW1 As Byte Absolute $0FE3,
   PREINC1 As Byte Absolute $0FE4,
   POSTDEC1 As Byte Absolute $0FE5,
   POSTINC1 As Byte Absolute $0FE6,
   INDF1 As Byte Absolute $0FE7,
   WREG As Byte Absolute $0FE8,
   FSR0L As Byte Absolute $0FE9,
   FSR0H As Byte Absolute $0FEA,
   PLUSW0 As Byte Absolute $0FEB,
   PREINC0 As Byte Absolute $0FEC,
   POSTDEC0 As Byte Absolute $0FED,
   POSTINC0 As Byte Absolute $0FEE,
   INDF0 As Byte Absolute $0FEF,
   INTCON3 As Byte Absolute $0FF0,
   INTCON2 As Byte Absolute $0FF1,
   INTCON As Byte Absolute $0FF2,
   PROD As Byte Absolute $0FF3,
   PRODL As Byte Absolute $0FF3,
   PRODH As Byte Absolute $0FF4,
   TABLAT As Byte Absolute $0FF5,
   TBLPTR As Byte Absolute $0FF6,
   TBLPTRL As Byte Absolute $0FF6,
   TBLPTRH As Byte Absolute $0FF7,
   TBLPTRU As Byte Absolute $0FF8,
   PC As Byte Absolute $0FF9,
   PCL As Byte Absolute $0FF9,
   PCLATH As Byte Absolute $0FFA,
   PCLATU As Byte Absolute $0FFB,
   STKPTR As Byte Absolute $0FFC,
   TOS As Byte Absolute $0FFD,
   TOSL As Byte Absolute $0FFD,
   TOSH As Byte Absolute $0FFE,
   TOSU As Byte Absolute $0FFF

// system ports...
Public System Port
   PORTE As Byte Absolute $0F84,
   PORTD As Byte Absolute $0F83,
   PORTC As Byte Absolute $0F82,
   PORTB As Byte Absolute $0F81,
   PORTA As Byte Absolute $0F80

// alias...
Public Dim
   FSR0 As FSR0L.AsWord,
   FSR1 As FSR1L.AsWord,
   FSR2 As FSR2L.AsWord,
   TABLEPTR As TBLPTRL.AsWord

// configuration fuses...
Public Config
   FOSC(FOSC) = [LP, XT, HSHP, HSMP, ECHP, ECHPIO6, RC, RCIO6, INTIO67, INTIO7, ECMP, ECMPIO6, ECLP, ECLPIO6],
   PLLCFG(PLLCFG) = [OFF, ON],
   PRICLKEN(PRICLKEN) = [OFF, ON],
   FCMEN(FCMEN) = [OFF, ON],
   IESO(IESO) = [OFF, ON],
   PWRTEN(PWRTEN) = [ON, OFF],
   BOREN(BOREN) = [OFF, ON, NOSLP, SBORDIS],
   BORV(BORV) = [285, 250, 220, 190],
   WDTEN(WDTEN) = [OFF, NOSLP, SWON, ON],
   WDTPS(WDTPS) = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768],
   CCP2MX(CCP2MX) = [PORTB3, PORTC1],
   PBADEN(PBADEN) = [OFF, ON],
   CCP3MX(CCP3MX) = [PORTE0, PORTB5],
   HFOFST(HFOFST) = [OFF, ON],
   T3CMX(T3CMX) = [PORTB5, PORTC0],
   P2BMX(P2BMX) = [PORTC0, PORTD2],
   MCLRE(MCLRE) = [INTMCLR, EXTMCLR],
   STVREN(STVREN) = [OFF, ON],
   LVP(LVP) = [OFF, ON],
   XINST(XINST) = [OFF, ON],
   DEBUG(DEBUG) = [ON, OFF],
   CP0(CP0) = [ON, OFF],
   CP1(CP1) = [ON, OFF],
   CP2(CP2) = [ON, OFF],
   CP3(CP3) = [ON, OFF],
   CPB(CPB) = [ON, OFF],
   CPD(CPD) = [ON, OFF],
   WRT0(WRT0) = [ON, OFF],
   WRT1(WRT1) = [ON, OFF],
   WRT2(WRT2) = [ON, OFF],
   WRT3(WRT3) = [ON, OFF],
   WRTC(WRTC) = [ON, OFF],
   WRTB(WRTB) = [ON, OFF],
   WRTD(WRTD) = [ON, OFF],
   EBTR0(EBTR0) = [ON, OFF],
   EBTR1(EBTR1) = [ON, OFF],
   EBTR2(EBTR2) = [ON, OFF],
   EBTR3(EBTR3) = [ON, OFF],
   EBTRB(EBTRB) = [ON, OFF]

// default fuses...
Config
   FCMEN = OFF,
   IESO = OFF,
   BOREN = ON,
   WDTEN = OFF,
   WDTPS = 128,
   PBADEN = OFF,
   STVREN = ON,
   LVP = OFF,
   XINST = OFF,
   DEBUG = OFF
