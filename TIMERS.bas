{
*****************************************************************************
*  Name    : TIMERS.BAS                                                     *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2010 Scanner Alarmas                             *
*          : All Rights Reserved                                            *
*  Date    : 29/01/2010                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

Module TIMERS

Include "IO.bas"
Include "BUFFERUSART.bas"
Include "usart.bas"
Include "GSM.bas"
Include "COMUNICACIONPC.bas"
Include "system.bas"
'Include "SPI.bas"
Include "DISPLAY.bas"
'Include "6P20.bas"
Include "H6P20.bas"
Include "wala.bas"
Include "CENTRAL.bas"
Include "PROGRAMACION.bas"
'Include "TECLADO.bas"
'Include "MEMORIA.bas"
Include "PROCESADOR.bas"
Include "WALA.bas"


//Declaracion de constantes locales
Const   
    TMR1StartVal = 45576,          // User defined TMR1 starting value
    ipLow = 1,
    ipHigh = 2
    
//Declaracion de variables locales
Dim
    GIEH As INTCON.7,               // Habilitacion de las interrupciones de alta prioridad
    GIEL As INTCON.6,               // Habilitacion de las interrupciones de baja prioridad
    TMR1IF As PIR1.0,               // TMR1 Interrupt Flag
    TMR1IP As IPR1.0,               // TMR1 Interrupt Flag
    TMR1ON As T1CON.0,              // TMR1 Count Enable
    Timer1 As TMR1L.AsWord,         // A quick way of creating a Word Alias
    Timer3 As TMR3L.AsWord,         // A quick way of creating a Word Alias
    TMR2ON As T2CON.2,              // Enables TMR2 to begin incrementing (DEPRECATED - using TMR4)
    TMR2IP As IPR1.1,               // TMR2 Interrupt Priority (DEPRECATED - using TMR4)
    TMR4ON As T4CON.2,              // Enables TMR4 to begin incrementing
    TMR4IP As IPR3.1,               // TMR4 Interrupt Priority
    TMR3ON As T3CON.0,              // Habilita el timer 3
    TMR3IE As PIE2.1,               // TMR3 Interrupt Enable
    TMR3IF As PIR2.1,               // TMR3 Interrupt Flag
    TMR3IP As IPR2.1,               // TMR3 Interrupt Priority
    CCP1IE As PIE1.2,               // CCP1 Interrupt Enable
    CCP1IF As PIR1.2,               // CCP1 Interrupt Flag
    CCP1IP As IPR1.2                // CCP1 Interrupt Priority
    

// event handler type...
'Type TEvent = Event()

// local and public variables...
Dim
'    FTimersEvent As TEvent,
    FByteRead As Byte,
    tonta As Byte


//Declaracion de constantes publicas
Public Dim
    TMR1IE As PIE1.0,               // TMR1 Interrupt Enable
    TMR2IF As PIR1.1,               // TMR2 overflow flag (DEPRECATED - using TMR4)
    TMR2IE As PIE1.1,               // TMR2 interrupt enable (DEPRECATED - using TMR4)
    TMR4IF As PIR3.1,               // TMR4 overflow flag
    TMR4IE As PIE3.1,               // TMR4 interrupt enable
    mS As Word,
    flag500ms As Bit,
    flag250ms As Bit,
    flag100ms As Bit,
    flag50ms As Bit

//Declaracion de variables publicas


'Interrupt tmr1_interrupt()
'    save(0)                       // back up system variables
'    if tmr1if = 1 then
'        tmr1on = 0                // disable tmr1
'        timer1 = tmr1reloadval    // reload a new start value (includes non-counted cycles while disabled)
'        tmr1on = 1                // enable tmr1
'        tmr1if = 0                // clear the tmr1 interrupt
'        inc(ms, 10)               // increment ms by 10
'    endif 
'    restore                       // restore system variables
'end interrupt   
 
Interrupt HighInterrupt(ipHigh)
    Save(0)                   // Back up system variables

    If TMR4IF = 1 And TMR4IE=1 Then         // Check if the interrupt was from TMR4
        SalidaWala=WALA.Salida
        If WALA.ContadorTiempo>0 Then
            Dec(WALA.ContadorTiempo)
        EndIf
        TMR4IF = 0                          // Clear the TMR4 interrupt flag
    EndIf

    If RCIE=1 And RCIF=1 Then
        ClrWDT()
        'BufferOverrun = FMaybeOverrun
        If Not USART.Overrun Then
          FByteRead = USART.RCRegister
          If Not flagDesbordamiento Then
            MiBuffer(ptrFinal) = FByteRead
            Inc(ptrFinal)
            If ptrFinal > Bound(MiBuffer) Then
               ptrFinal = 0
            EndIf
            flagDesbordamiento = (ptrFinal = ptrInicio) 
          Else
            BufferUSART.Initialize()
          EndIf
        Else
          BufferUSART.Initialize()
          'USART.Write("MUERTO!!!!")
        EndIf	
        RCIF=0     
    EndIf

    Restore                                 // Restore system variables
End Interrupt

Interrupt LowInterrupt(ipLow)
    Save(0)                   // Back up system variables

    If TMR1IF = 1 And TMR1IE=1 Then
        'Toggle(SalidaTest)
        TMR1ON = 0                // disable tmr1
        Timer1 = TMR1StartVal    // reload a new start value (includes non-counted cycles while disabled)
        TMR1ON = 1                // enable tmr1
        TMR1IF = 0                // clear the tmr1 interrupt
        Inc(mS, 10)               // increment ms by 10
'        FTimersEvent()



    //Para el vento del rollover a los 60000 ms
    If mS=60010 Then
        mS=10 
    EndIf

    //Replica del timer ms para modulo GSM
    GSM.mS=TIMERS.mS

    //Replica del timer ms para modulo COMUNICACIONPC
    COMUNICACIONPC.mS=TIMERS.mS

    //Timer para la comunicacion con la pc que se decrementa de a 10ms por vez
    If COMUNICACIONPC.TimerComunicacionPC>0 Then
        Dec(COMUNICACIONPC.TimerComunicacionPC, 10)
    EndIf

    //Timer del modulo de comunicaciones que se decrementa de a 10ms por vez
    //Cada cuenta representa 10ms
    If GSM.TimerModulo>0 Then
        Dec(GSM.TimerModulo)
    EndIf

    //Timer del modulo de GPRS que se decrementa de a 10ms por vez
    If GSM.TimerGPRS>0 Then
        Dec(GSM.TimerGPRS, 10)
    EndIf

    //Timer de solicitud de estado de GPRS que se decrementa de a 10ms por vez
    If GSM.TimerSolicitudEstadoGPRS>0 Then
        Dec(GSM.TimerSolicitudEstadoGPRS, 10)
    EndIf

    //Timer de de envio por GPRS que se decrementa de a 10ms por vez
    If GSM.TimerEnvioGPRS>0 Then
        Dec(GSM.TimerEnvioGPRS, 10)
    EndIf

    //Timer del procesador sintactico en caso de error y que quede atrapado leyendo un mensaje, se decrementa de a 10ms por vez
    If BufferUSART.TimerProcesador>0 Then
        Dec(BufferUSART.TimerProcesador, 10)
    EndIf

    //Timer de la salida 1 que se decrementa de a 10ms por vez
    If TimerSalida1>0 Then
        Dec(TimerSalida1, 10)
    EndIf

    //Timer de la salida 2 que se decrementa de a 10ms por vez
    If TimerSalida2>0 Then
        Dec(TimerSalida2, 10)
    EndIf
    
    //Timer de beeps de sirena
    If TimerBeepsSirenas>0 Then
        Dec(TimerBeepsSirenas, 10)
    EndIf

    //Timer de delays
    If TimerDelay>0 Then
        Dec(TimerDelay, 10)
    EndIf

    //Estado de activacion de wala
    If CENTRAL.flagEntradaWalaHabilitada Then
        If IO.ZonaPrealarma=0 Then
            If WALA.TimerDesactivada<65535 Then
                Inc(WALA.TimerDesactivada)
            EndIf
            If WALA.TimerActivada>250 Then
                If WALA.ContadorPulsosLed<255 Then
                    Inc(WALA.ContadorPulsosLed)
                EndIf
            EndIf
            WALA.TimerActivada=0
        Else
            If WALA.TimerActivada<65535 Then
                Inc(WALA.TimerActivada)
            EndIf
            If WALA.TimerDesactivada>250 Then
                If WALA.ContadorPulsosLed<255 Then
                    Inc(WALA.ContadorPulsosLed)
                EndIf
            EndIf
            WALA.TimerDesactivada=0
        EndIf
    EndIf
    
    If WALA.TimerActivada>500 Then
        WALA.EstadoActivacionWala=WALA.Activada
    EndIf

    If WALA.TimerActivada>500 Then
        WALA.ContadorPulsosLed=0
    EndIf

    If WALA.TimerDesactivada>500 Then
        WALA.EstadoActivacionWala=WALA.Desactivada
        WALA.ContadorPulsosLed=0
    EndIf
    
    If WALA.ContadorPulsosLed>3 Then
        WALA.EstadoActivacionWala=WALA.ActivadaParcial
    EndIf
    
    //Control de las entradas cableadas con debounce
    //Zona 1
    If IO.ZonaCableada1=1 Then
        IO.TimerZonaCableadaL(0)=0
        If (IO.TimerZonaCableadaH(0)<255) Then
            Inc (IO.TimerZonaCableadaH(0))
        EndIf
    Else
        IO.TimerZonaCableadaH(0)=0
        If (IO.TimerZonaCableadaL(0)<255) Then
            Inc (IO.TimerZonaCableadaL(0))
        EndIf
    EndIf

    //Zona 2
    If IO.ZonaCableada2=1 Then
        IO.TimerZonaCableadaL(1)=0
        If (IO.TimerZonaCableadaH(1)<255) Then
            Inc (IO.TimerZonaCableadaH(1))
        EndIf
    Else
        IO.TimerZonaCableadaH(1)=0
        If (IO.TimerZonaCableadaL(1)<255) Then
            Inc (IO.TimerZonaCableadaL(1))
        EndIf
    EndIf

    //Zona 3
    If IO.ZonaCableada3=1 Then
        IO.TimerZonaCableadaL(2)=0
        If (IO.TimerZonaCableadaH(2)<255) Then
            Inc (IO.TimerZonaCableadaH(2))
        EndIf
    Else
        IO.TimerZonaCableadaH(2)=0
        If (IO.TimerZonaCableadaL(2)<255) Then
            Inc (IO.TimerZonaCableadaL(2))
        EndIf
    EndIf

    //Zona 4
    If IO.ZonaCableada4=1 Then
        IO.TimerZonaCableadaL(3)=0
        If (IO.TimerZonaCableadaH(3)<255) Then
            Inc (IO.TimerZonaCableadaH(3))
        EndIf
    Else
        IO.TimerZonaCableadaH(3)=0
        If (IO.TimerZonaCableadaL(3)<255) Then
            Inc (IO.TimerZonaCableadaL(3))
        EndIf
    EndIf

    //Zona 5
    If IO.ZonaCableada5=1 Then
        IO.TimerZonaCableadaL(4)=0
        If (IO.TimerZonaCableadaH(4)<255) Then
            Inc (IO.TimerZonaCableadaH(4))
        EndIf
    Else
        IO.TimerZonaCableadaH(4)=0
        If (IO.TimerZonaCableadaL(4)<255) Then
            Inc (IO.TimerZonaCableadaL(4))
        EndIf
    EndIf

    //Zona 6
    If IO.ZonaCableada6=1 Then
        IO.TimerZonaCableadaL(5)=0
        If (IO.TimerZonaCableadaH(5)<255) Then
            Inc (IO.TimerZonaCableadaH(5))
        EndIf
    Else
        IO.TimerZonaCableadaH(5)=0
        If (IO.TimerZonaCableadaL(5)<255) Then
            Inc (IO.TimerZonaCableadaL(5))
        EndIf
    EndIf

    //Zona 7
    If IO.ZonaCableada7=1 Then
        IO.TimerZonaCableadaL(6)=0
        If (IO.TimerZonaCableadaH(6)<255) Then
            Inc (IO.TimerZonaCableadaH(6))
        EndIf
    Else
        IO.TimerZonaCableadaH(6)=0
        If (IO.TimerZonaCableadaL(6)<255) Then
            Inc (IO.TimerZonaCableadaL(6))
        EndIf
    EndIf

    //Zona 8
    If IO.ZonaCableada8=1 Then
        IO.TimerZonaCableadaL(7)=0
        If (IO.TimerZonaCableadaH(7)<255) Then
            Inc (IO.TimerZonaCableadaH(7))
        EndIf
    Else
        IO.TimerZonaCableadaH(7)=0
        If (IO.TimerZonaCableadaL(7)<255) Then
            Inc (IO.TimerZonaCableadaL(7))
        EndIf
    EndIf

    //Antidesarme
    If IO.ZonaAntidesarme=1 Then
        If (IO.TimerZonaAntidesarme<255) Then
            Inc (IO.TimerZonaAntidesarme)
        EndIf
    Else
        IO.TimerZonaAntidesarme=0
    EndIf


    //Timer de cada un segundo
    If (mS Mod 1000)=0 Then
        //Controlo todos los timers medidos en segundos decrecientes a cero
        If CENTRAL.TimerSirenas>0 Then
            Dec(CENTRAL.TimerSirenas)
        EndIf
        If CENTRAL.TimerPrealarma>0 Then
            Dec(CENTRAL.TimerPrealarma)
        EndIf
        If CENTRAL.TimerEntrada>0 Then
            Dec(CENTRAL.TimerEntrada)
        EndIf
        If CENTRAL.TimerSalida>0 Then
            Dec(CENTRAL.TimerSalida)
        EndIf
        If PROGRAMACION.TimerProgramacion>0 Then
            Dec(PROGRAMACION.TimerProgramacion)
        EndIf
        If CENTRAL.TimerDisparo>0 Then
            Dec(CENTRAL.TimerDisparo)
        EndIf
        If CENTRAL.TimerBateriaBaja>0 Then
            Dec(CENTRAL.TimerBateriaBaja)
        EndIf
        If CENTRAL.TimerBloqueoAsalto>0 Then
            Dec(CENTRAL.TimerBloqueoAsalto)
        EndIf
        If CENTRAL.TimerBloqueoEmergencia>0 Then
            Dec(CENTRAL.TimerBloqueoEmergencia)
        EndIf
        If CENTRAL.TimerBloqueoIncendio>0 Then
            Dec(CENTRAL.TimerBloqueoIncendio)
        EndIf
        If CENTRAL.TimerMostrarRSSI>0 Then
            Dec(CENTRAL.TimerMostrarRSSI)
        EndIf
        If CENTRAL.TimerTest>0 Then
            Dec(CENTRAL.TimerTest)
        EndIf
        If CENTRAL.TimerPanico>0 Then
            Dec(CENTRAL.TimerPanico)
        EndIf
        If TimerBloqueoBateriaBaja>0 Then
            Dec(TimerBloqueoBateriaBaja)
        EndIf
        
        
//        If TECLADO.tmrPulsandoTeclas>0 Then
//            Dec(TECLADO.tmrPulsandoTeclas)
//        EndIf
        //Controlo todos los timers medidos en segundos crecientes
        If HT6P20.RecibiendoCanal1 Then
            If HT6P20.TimerRecibiendoCanal1<255 Then
                Inc(HT6P20.TimerRecibiendoCanal1)
            EndIf
        Else
            HT6P20.TimerRecibiendoCanal1=0
        EndIf
        If HT6P20.RecibiendoCanal2 Then
            If HT6P20.TimerRecibiendoCanal2<255 Then
                Inc(HT6P20.TimerRecibiendoCanal2)
            EndIf
        Else
            HT6P20.TimerRecibiendoCanal2=0
        EndIf
        If HT6P20.RecibiendoAmbosCanales Then
            If HT6P20.TimerRecibiendoAmbosCanales<255 Then
                Inc(HT6P20.TimerRecibiendoAmbosCanales)
            EndIf
        Else
            HT6P20.TimerRecibiendoAmbosCanales=0
        EndIf

//        If TECLADO.TeclaPulsada And TECLADO.UltimaTeclaPulsada=TECLADO.ultimaTeclaNum Then
//            If PROGRAMACION.TimerBorrarInalambricos<255 Then
//                Inc(PROGRAMACION.TimerBorrarInalambricos)
//            EndIf
//        Else
//            PROGRAMACION.TimerBorrarInalambricos=0
//        EndIf

        If flagLlamadaEnCurso Then
            Inc(TimerLlamada)
        Else
            TimerLlamada=0
        EndIf
        
        If Not flagRegistrado Then
            If TimerNoRegistrado<255 Then
                Inc(TimerNoRegistrado)
            EndIf
        Else
            TimerNoRegistrado=0
        EndIf
        //Incremento el timer freerun del modulo gsm usado para medir diferencia en el tiempo por timestamp
        Inc(IO.Tick)
        
        //Incremento el timer de desconexion o el de conexion segun corresponda
        If IO.DeteccionAlimentacion = 1 Then
            If CENTRAL.TimerAlimentacionConectada<65535 Then
                Inc(CENTRAL.TimerAlimentacionConectada)
            EndIf
            CENTRAL.TimerAlimentacionDesconectada=0
        Else
            If CENTRAL.TimerAlimentacionDesconectada<65535 Then
                Inc(CENTRAL.TimerAlimentacionDesconectada)
            EndIf
            CENTRAL.TimerAlimentacionConectada=0
        EndIf
    EndIf

    //Timer de cada medio segundo
    If (mS Mod 500)=0 Then
        flag500ms=flag500ms Xor 1
    EndIf
    //Timer de cada cuarto de segundo
    If (mS Mod 250)=0 Then
        flag250ms=flag250ms Xor 1
    EndIf
    //Timer de cada 100ms
    If (mS Mod 100)=0 Then
        flag100ms=flag100ms Xor 1
    EndIf
    //Timer de cada 50ms
    If (mS Mod 50)=0 Then
        flag50ms=flag50ms Xor 1
    EndIf
    
    //Control del flag de parpadeo de 500ms del modulo programacion
    If flag250ms=1 Then
        PROGRAMACION.flag250ms=true
    Else
        PROGRAMACION.flag250ms=false
    EndIf
    
    //Control de parpadeo del led de estado
    If IO.flagParpadearLedEstado500ms Then
        If flag500ms=IO.SegmentoEncendido Then
            IO.LedEstado=IO.LedEstadoEncendido
        Else
            IO.LedEstado=IO.LedEstadoApagado
        EndIf
    EndIf
    If IO.flagParpadearLedEstado250ms Then
        If flag250ms=IO.SegmentoEncendido Then
            IO.LedEstado=IO.LedEstadoEncendido
        Else
            IO.LedEstado=IO.LedEstadoApagado
        EndIf
    EndIf

    //Control de parpadeo del led de GSM
    If IO.flagParpadearLedGSM500ms Then
        If flag500ms=IO.SegmentoEncendido Then
            IO.LedGSM=IO.LedGSMEncendido
        Else
            IO.LedGSM=IO.LedGSMApagado
        EndIf
    EndIf
    If IO.flagParpadearLedGSM250ms Then
        If flag250ms=IO.SegmentoEncendido Then
            IO.LedGSM=IO.LedGSMEncendido
        Else
            IO.LedGSM=IO.LedGSMApagado
        EndIf
    EndIf
    If IO.flagParpadearLedGSM100ms Then
        If flag100ms=IO.SegmentoEncendido Then
            IO.LedGSM=IO.LedGSMEncendido
        Else
            IO.LedGSM=IO.LedGSMApagado
        EndIf
    EndIf
    If IO.flagParpadearLedGSM50ms Then
        If flag50ms=IO.SegmentoEncendido Then
            IO.LedGSM=IO.LedGSMEncendido
        Else
            IO.LedGSM=IO.LedGSMApagado
        EndIf
    EndIf
    
    //Control de parpadeo del display
    If DISPLAY.flagParpadearDisplay Then
        If flag500ms=IO.SegmentoEncendido Then
            IO.SegmentoA = IO.flagSegmentoA
            IO.SegmentoB = IO.flagSegmentoB
            IO.SegmentoC = IO.flagSegmentoC
            IO.SegmentoD = IO.flagSegmentoD
            IO.SegmentoE = IO.flagSegmentoE
            IO.SegmentoF = IO.flagSegmentoF
            IO.SegmentoG = IO.flagSegmentoG
        Else
            IO.SegmentoA = IO.SegmentoApagado
            IO.SegmentoB = IO.SegmentoApagado
            IO.SegmentoC = IO.SegmentoApagado
            IO.SegmentoD = IO.SegmentoApagado
            IO.SegmentoE = IO.SegmentoApagado
            IO.SegmentoF = IO.SegmentoApagado
            IO.SegmentoG = IO.SegmentoApagado
        EndIf
    Else
        IO.SegmentoA = IO.flagSegmentoA
        IO.SegmentoB = IO.flagSegmentoB
        IO.SegmentoC = IO.flagSegmentoC
        IO.SegmentoD = IO.flagSegmentoD
        IO.SegmentoE = IO.flagSegmentoE
        IO.SegmentoF = IO.flagSegmentoF
        IO.SegmentoG = IO.flagSegmentoG
    EndIf
    
    If DISPLAY.flagParpadearPunto Then
        If flag500ms=IO.SegmentoEncendido Then
            IO.Punto=IO.SegmentoEncendido
        Else
            IO.Punto=IO.SegmentoApagado
        EndIf
    Else
        IO.Punto=IO.flagPunto 
    EndIf
    
    //Control de los botones
    If IO.btnProg=IO.BotonPulsado Then
        If tmrBtnProg<3000 Then
            Inc(tmrBtnProg)
        EndIf
        btnProgPulsado=true
    Else
        btnProgPulsado=false
        If tmrBtnProg>0 Then
            TiempoBtnProgPulsado=tmrBtnProg
            btnProgSoltado=true
        EndIf
        tmrBtnProg=0
    EndIf
    
    If IO.btnSel=IO.BotonPulsado Then
        If tmrBtnSel<3000 Then
            Inc(tmrBtnSel)
        EndIf
        btnSelPulsado=true
    Else
        btnSelPulsado=false
        If tmrBtnSel>0 Then
            TiempoBtnSelPulsado=tmrBtnSel
            btnSelSoltado=true
        EndIf
        tmrBtnSel=0
    EndIf



    EndIf 

    If TMR3IF = 1 And TMR3IE=1 Then         // Check if the interrupt was from TMR3
        If ContadorDesbordamiento<255 Then
            Inc(ContadorDesbordamiento)
        EndIf
        //Decremento del contador de buenos si el timer desbordo 2 veces seguidas
        If ContadorDesbordamiento>10 Then
            If ContBuenos>0 Then
                ContBuenos=ContBuenos-1
            Else
                Recibiendo6P20=false
                RecibiendoCanal1=false
                RecibiendoCanal2=false
                RecibiendoAmbosCanales=false
                MismoCodigo=false
                MismoDato=false
                AddressLow=0
                AddressMid=0
                AddressHigh=0
                DataAnti=0
            EndIf
        EndIf
        TMR3IF = 0                          // Clear the TMR3 interrupt flag
    EndIf

 {
    //Si llego por un evento en el modulo de captura
    If CCP1IF = 1 And CCP1IE=1 Then         // Check if the interrupt was from CCP1
        //Calculo la medicion actual
        Medicion=CapturaActual-CapturaAnterior
        //Guardo la medicion actual en el registro de anterior
        CapturaAnterior=CapturaActual
        
        //TEST
        //TEST
        //TEST
        If Estado6P20=Estado6P20_Espera Then
            SalidaTest=0
        Else
            Toggle(SalidaTest)
        EndIf
        //TEST
        //TEST
        //TEST

        
        //Verifico en que instancia del tren me encuentro
        Select Estado6P20
            //Si estoy esperando el comienzo del pulso piloto...
            Case Estado6P20_Espera
                //Reconfiguro el modulo para detectar el flanco de subida
                CCP1IE=0
                ccp1con=0
                ccp1con=%00000101
                CCP1IF=0
                CCP1IE=1
                //Voy al siguiente paso
                Inc(Estado6P20)
            //Si estoy esperando el comienzo del tercio...
            Case Estado6P20_Piloto
                //Llego el fin del piloto
                //Reconfiguro el modulo para detectar el flanco de bajada
                CCP1IE=0
                ccp1con=0
                ccp1con=%00000100
                CCP1IF=0
                CCP1IE=1
                //Si el piloto es de duracion valida
                If Medicion<MaxPiloto And Medicion>MinPiloto Then
                    //Voy al siguiente paso
                    Inc(Estado6P20)
                Else
                    //Retorno a la espera
                    Estado6P20=Estado6P20_Espera
                    ErrorGeneral=true
                EndIf
            //Si estoy esperando el final del tercio...
            Case Estado6P20_Tercio
                //Llego el fin del tercio
                //Si el tercio es de duracion valida
                If Medicion<MaxTercio And Medicion>MinTercio Then
                    //Reconfiguro el modulo para detectar el flanco de subida
                    CCP1IE=0
                    ccp1con=0
                    ccp1con=%00000101
                    CCP1IF=0
                    CCP1IE=1
                    //Arranco por el primer byte
                    ContadorBytes=0
                    //Arranco por el primer bit
                    ContadorBits=0
                    //Limpio el buffer
                    Buffer6P20=0
                    //Voy al siguiente paso
                    Inc(Estado6P20)
                Else
                    //Retorno a la espera
                    Estado6P20=Estado6P20_Espera
                    ErrorGeneral=true
                EndIf
            //Termino el bit low
            Case Estado6P20_BitLow
                //Reconfiguro el modulo para detectar el flanco de bajada
                CCP1IE=0
                ccp1con=0
                ccp1con=%00000100
                CCP1IF=0
                CCP1IE=1
                //guardo el valor de bitlow para analizarlo cuando llegue el bithigh
                BitLow=Medicion
                //Voy al siguiente paso
                Inc(Estado6P20)
            //Termino el bit high
            Case Estado6P20_BitHigh
                //Tomo el valor de medicion como bithigh
                BitHigh=Medicion
                
                //Chequeo de integridad de trecios de bit
                If (BitLow>MinTercio And BitLow<MaxTercio And BitHigh>MinDosTercios And BitHigh<MaxDosTercios) Or (BitHigh>MinTercio And BitHigh<MaxTercio And BitLow>MinDosTercios And BitLow<MaxDosTercios) Then
                    //y el total del bit como unidad integral
                    BitTot = BitLow + BitHigh
                    If BitTot<MaxTiempoBit Or BitTot>MinTiempoBit Then
                        //Llego un bit ok
                        
                        //Reconfiguro el modulo para detectar el flanco de subida
                        CCP1IE=0
                        ccp1con=0
                        ccp1con=%00000101
                        CCP1IF=0
                        CCP1IE=1
        
                            //Lo cargo en el buffer
                        Buffer6P20=Buffer6P20>>1
                        If BitLow>BitHigh Then 
                            Buffer6P20.7=1
                        Else
                            Buffer6P20.7=0
                        EndIf
                        
                        //Marco para ir al paso anterior (bitlow)
                        Dec(Estado6P20)
                        
                        //Anoto para saber en que posicion estoy del byte
                        Inc(ContadorBits)
                        
                        Select ContadorBytes
                            //Estoy recibiendo address low
                            Case 0
                                //Si termine con address low
                                If ContadorBits=8 Then
                                    //Paso a address mid
                                    Inc(ContadorBytes)
                                    //Comienzo por el primer bit
                                    ContadorBits=0
                                    
                                    AddressLow=Buffer6P20
                                EndIf
                            //Estoy recibiendo address mid
                            Case 1
                                //Si termine con address mid
                                If ContadorBits=8 Then
                                    //Paso a address hig
                                    Inc(ContadorBytes)
                                    //Comienzo por el primer bit
                                    ContadorBits=0
                                    
                                    AddressMid=Buffer6P20
                                EndIf
                            //Estoy recibiendo address high
                            Case 2
                                //Si termine con address high
                                If ContadorBits=4 Then
                                    //Paso a dataanti
                                    Inc(ContadorBytes)
                                    //Comienzo por el primer bit
                                    ContadorBits=0
                                    
                                    AddressHigh=Buffer6P20>>4
                                EndIf
                            //Estoy recibiendo dataanti
                            Case 3
                                //Si termine con dataanti
                                If ContadorBits=8 Then
                                    DataAnti=Buffer6P20
                                    
                                    //Chequeo si los datos son consistentes
                                    If (DataAnti And %11110000)=%10100000 Then
                                        
                                        ErrorGeneral=false
                                        
                                        ContadorDesbordamiento=0
                                        
                                        MismoCodigo=(AddressLow=_AddressLow) And (AddressMid=_AddressMid) And (AddressHigh=_AddressHigh)
                                        MismoDato=(DataAnti=_DataAnti)
    
                                        _AddressLow=AddressLow
                                        _AddressMid=AddressMid
                                        _AddressHigh=AddressHigh
                                        _DataAnti=DataAnti
                                    EndIf
                                                        
                                    //Paso al estado de espera de fin de piloto
                                    Estado6P20=Estado6P20_Piloto
                                    
                                    //Reconfiguro el modulo para detectar el flanco de subida
                                    CCP1IE=0
                                    ccp1con=0
                                    ccp1con=%00000101
                                    CCP1IF=0
                                    CCP1IE=1
                    
                                    EndIf
                        End Select
                    Else
                        //Retorno a la espera
                        Estado6P20=Estado6P20_Espera
                        ErrorGeneral=true
                    EndIf
                Else
                    //Retorno a la espera
                    Estado6P20=Estado6P20_Espera
                    ErrorGeneral=true
                EndIf
            End Select
            
            If ErrorGeneral Then
                MismoCodigo=false
                MismoDato=false
            Else
                //Control de las indicaciones para el programa principal
                If MismoCodigo And MismoDato Then
                    ContBuenos=40
                    Recibiendo6P20=true
                Else
                    If ContBuenos>0 Then
                        ContBuenos=ContBuenos-1
                    Else
                        Recibiendo6P20=false
                        RecibiendoCanal1=false
                        RecibiendoCanal2=false
                        RecibiendoAmbosCanales=false
                    EndIf
                EndIf
            EndIf
            
        Tonta=PORTC
        
        'CCP1IF = 0                          // Clear the TMR3 interrupt flag
    EndIf
}
    If CCP1IF = 1 And CCP1IE=1 Then         // Check if the interrupt was from CCP1
        HT6P20.flagNuevaMedicion=true
        tonta=CCP1CON
        CCP1CON=0
        If HT6P20.Estado6P20=Estado6P20_Espera Then
            CCP1CON=%00000101
            'SalidaTest=1
        Else                              
            CCP1CON=tonta Xor 1
            'Toggle(SalidaTest)
        EndIf
        tonta=PORTC
        CCP1IF=0
    EndIf
    
    Restore                   // Restore system variables
End Interrupt
 
'// event 
'Event TimersEvent()
'    Save(0)
'    
'    Restore
'End Event 

Sub TMR1_Initialize()
    TMR1ON = 0                 // Disable TMR1
    T1CON.1 = 0                // 1 = External clock from pin RC0/T1OSO/T1CKI (on the rising edge)
                               // 0 = Internal clock (FOSC/4)
    'TRISC.0 = 1               // If External clock, then set clock as an input
    'T1CON.2 = 1               // 1 = Do not synchronize external clock input
                               // 0 = Synchronize external clock input
                               // When T1CON.1 = 0;
                               //   this bit is ignored.
    T1CON.4 = 1                // 11 = 1:8 prescale value
    T1CON.5 = 1                // 10 = 1:4 prescale value
                               // 01 = 1:2 prescale value...
                               // 00 = 1:1 prescale value
    Timer1 = TMR1StartVal      // Fill the Timer register with a starting value
 
    TMR1IE = 1                 // Enable TMR1 Interrupts
    TMR1IP = 0
    TMR1ON = 1                 // Enable TMR1 to Increment
//    Enable(LowInterrupt)     // Enable TMR1 Interrupt Handle
End Sub    
 
Private Sub TMR4_Initialize()
    TMR4ON = 0                // Disable TMR4
    TMR4IE = 0                // Turn off TMR4 interrupts

    PR4=MinBit                // TMR4 Period register PR4
    T4CON=%01001010           // T4CON 0:1 = Prescale
                              //        00 = Prescaler is 1:1
                              //        01 = Prescaler is 1:4
                              //        1x = Prescaler is 1:16
                              //      3:6 = Postscale
                              //     0000 = 1:1 postscale
                              //     0001 = 1:2 postscale
                              //     0010 = 1:3 postscale...
                              //     1111 = 1:16 postscale

    'ccp1con=%00001100
    TMR4IP = 1                // Set TMR4 to high priority
    TMR4 = 0                  // Reset TMR4 Value
    TMR4IE = 0                // Initially disable TMR4 interrupts
    TMR4ON = 1                // Enable TMR4 to increment
End Sub

// DEPRECATED - Keeping TMR2_Initialize for reference
'Private Sub TMR2_Initialize()
'    TMR2ON = 0                // Disable TMR2
'    TMR2IE = 0                // Turn off TMR2 interrupts
'
'    PR2=MinBit                // TMR2 Period register PR2
'    T2CON=%01001010           // T2CON 0:1 = Prescale
'                              //        00 = Prescaler is 1:1
'                              //        01 = Prescaler is 1:4
'                              //        1x = Prescaler is 1:16
'                              //      3:6 = Postscale
'                              //     0000 = 1:1 postscale
'                              //     0001 = 1:2 postscale
'                              //     0010 = 1:3 postscale...
'                              //     1111 = 1:16 postscale
'
'    'ccp1con=%00001100
'    TMR2IP = 1
'    TMR2 = 0                  // Reset TMR2 Value
'    TMR2IE = 0                // Enable TMR2 interrupts
'    TMR2ON = 1                // Enable TMR2 to increment
'End Sub

Private Sub TMR3_Initialize()
    TMR3ON = 0                // Disable TMR3
    TMR3IE = 0                // Turn off TMR3 interrupts   
    CCP1IE = 0

    T3CON=%00100010
 
    //CCP1CON=%00000100         //Seteo el modulo CCP para captura por flanco de bajada
    CCPTMRS0=%00000001

    TMR3IF = 0
    CCP1IF = 0
     
//    Enable(HighInterrupt)
    TMR3IP = 0
    CCP1IP = 0
    
    TMR3IE = 1                // Enable TMR3 interrupts
    CCP1IE = 1
    TMR3ON = 1                // Enable TMR3 to increment
End Sub

Public Sub HabilitarInterrupciones()
    Enable(LowInterrupt)     // Enable TMR1 Interrupt Handle
    Enable(HighInterrupt)
End Sub
//Inicializacion del modulo
TMR1_Initialize                 // Setup and enable TMR1
TMR4_Initialize                 // Setup and enable TMR4 (replaces TMR2)
TMR3_Initialize                 // Setup and enable TMR3

'FTimersEvent=TimersEvent


