{
*****************************************************************************
*  Name    : MemoryAccess.BAS                                               *
*  Author  : Tim Box                                                        *
*  Notice  : Copyright (c) 2007 TJB Systems Ltd                             *
*          : All Rights Reserved                                            *
*  Date    : 03/08/2007                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}


Module MemoryAccessOriginal

//Este contiene codigo especifico para cada micro

// module option - just put some values in here for testing...
    #if _device in (18f4520, 18f4550) Then
        #option ROM_BLOCK_SIZE = 32
    #elseif _device=18f452
        #option ROM_BLOCK_SIZE = 8
    #elseif _device in (18f4620, 18f46k20, 18f46k22)
        #option ROM_BLOCK_SIZE = 64
    #else
        #error "Micro Equivocado!!!"
    #endif
    #if Not (ROM_BLOCK_SIZE in (8, 16, 32, 64))
      #error ROM_BLOCK_SIZE, "Invalid option. ROM_BLOCK_SIZE must be 8, 16 or whatever..."
    #endif

// bring option into the program...
    Const 
        WriteBlockSize = ROM_BLOCK_SIZE
        
// some local aliases...  
    Dim
      EEPGD As EECON1.7,
      WR As EECON1.1,
      WREN As EECON1.2 
      
//Variables publicas
    Public Dim
        FlagGIEH As Bit,
        FlagGIEL As Bit

// inline delay
    Inline Sub Delay()
      ASM
      GoTo $ + 2
      End ASM
    End Sub

// table read
    Inline Sub TableRead()
      ASM
      TBLRD*+
      End ASM
    End Sub

// table write
    Inline Sub TableWrite()
      ASM
      TBLWT*+
      End ASM
    End Sub

// Functions =======================

// read a byte from ROM  
    Public Function ReadByte (pAddress As TABLEPTR) As TABLAT
      EECON1 = 0
      EEPGD = 1
      TableRead
    End Function

// read a Word from ROM  
    Public Function ReadWord (pAddress As TABLEPTR) As Word
      EECON1 = 0
      EEPGD = 1
      TableRead
      result.Byte0 = TABLAT
      TableRead
      result.Byte1 = TABLAT
    End Function

// read a LongWord from ROM  
    Public Function ReadLongWord (pAddress As TABLEPTR) As LongWord
      EECON1 = 0
      EEPGD = 1
      TableRead
      result.Byte0 = TABLAT
      TableRead
      result.Byte1 = TABLAT
      TableRead
      result.Byte2 = TABLAT
      TableRead
      result.Byte3 = TABLAT
    End Function

// Subroutines ======================= 

// erase ROM block...
    Sub EraseBlock (pAddress As TABLEPTR)
      intcon.7=0
      EECON1 = $94
      EECON2 = $55
      EECON2 = $AA
      WR = 1
      intcon.7=1
      Delay
      WREN = 0
    End Sub

// close a write
    Sub CloseWrite()
      Dec(tableptr)
      TBLptru=0
      intcon.7=0
      EECON1 = $84
      EECON2 = $55
      EECON2 = $AA
      WR = 1
      intcon.7=1
      Delay
      WREN = 0
      Inc(tableptr)
    End Sub

{
****************************************************************************
* Name    : WriteItem                                                      *
* Purpose : Write a byte to program memory                                 *
****************************************************************************
}
    Sub WriteItem (pValue As TABLAT)
      TableWrite       
      'Inc(TABLEPTR)
    End Sub


    Public Sub GrabarStringMemoria(direccion As Word, telefono As String)
        Dim index As Byte

        FlagGIEH=INTCON.7
        INTCON.7=0
        FlagGIEL=INTCON.6
        INTCON.6=0

        EraseBlock(direccion)
        tableptr=direccion

        For INDEX = 0 To 63
            WriteItem(telefono(index))
            If ((index+1) Mod WriteBlockSize)=0 Then
                CloseWrite()
            EndIf
        Next        
'        For index = 0 To 31
'            WriteItem(telefono(index))
'        Next 
'        CloseWrite()       
'        For index = 32 To 63
'            WriteItem(telefono(index))
'        Next 
'        CloseWrite()       
         

'        For index = 32 To 63
'            MemoryAccess.WriteAt(direccion+index,telefono(index)) 
'        Next 
'        MemoryAccess.CloseWrite()        
        INTCON.7=FlagGIEH
        INTCON.6=FlagGIEL
    End Sub

