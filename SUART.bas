{
****************************************************************
*  Name    : SUART.BAS                                         *
*  Author  : <PERSON>                                 *
*  Notice  : Copyright (c) 2006 Mecanique                      *
*          : All Rights Reserved                               *
*  Date    : 28/05/2006                                        *
*  Version : 1.3 Changed "W" to "WREG" in ReadByte             *
*  Notes   : 1.2 Replaced 'rcall' with 'call'                  *
*          : 1.1 Release version                               *
****************************************************************
}
Module UART

Include "system.bas"
               
Const
   CycleCost = 20,                          // TX and RX cycle cost
   FOSC = _clock * 1000000,                 // clock (MHz)
   tcy300 = (2 * FOSC) / (4 * 300) / 2,     // 300 baud Tcy
   tcy600 = (2 * FOSC) / (4 * 600) / 2,     // 600 baud Tcy
   tcy1200 = (2 * FOSC) / (4 * 1200) / 2,   // 1200 baud Tcy
   tcy2400 = (2 * FOSC) / (4 * 2400) / 2,   // 2400 baud Tcy
   tcy4800 = (2 * FOSC) / (4 * 4800) / 2,   // 4800 baud Tcy
   tcy9600 = (2 * FOSC) / (4 * 9600) / 2,   // 9600 baud Tcy
   tcy19200 = (2 * FOSC) / (4 * 19200) / 2, // 19200 baud Tcy
   tcy38400 = (2 * FOSC) / (4 * 38400) / 2, // 38400 baud Tcy - pushing the limits, high xtals only!
   tcy57600 = (2 * FOSC) / (4 * 57600) / 2  // 57600 baud Tcy - pushing the limits, high xtals only!


// calculate baud rate constants - these figures are one
// BitTime (BT) in microseconds (us). For example, 1 / 9600 baud
// gives 104.16 us. Taking into account the TX and RX cycle costs
// yields a figure of 100 us
Public Const
   sbr300 As Word = (tcy300 - CycleCost) / (_clock / 4),
   sbr600 As Word = (tcy600 - CycleCost) / (_clock / 4),
   sbr1200 As Word = (tcy1200 - CycleCost) / (_clock / 4),
   sbr2400 As Word = (tcy2400 - CycleCost) / (_clock / 4),
   sbr4800 As Word = (tcy4800 - CycleCost) / (_clock / 4),
   sbr9600 As Word = (tcy9600 - CycleCost) / (_clock / 4),
   sbr19200 As Word = (tcy19200 - CycleCost) / (_clock / 4),
   sbr38400 As Word = (tcy38400 - CycleCost) / (_clock / 4),
   sbr57600 As Word = (tcy57600 - CycleCost) / (_clock / 4)  

// public UART modes...
Public Const
   umTrue = 1,
   umInverted = 2,
   umOpen = 4,
   umOpenTrue = umOpen Or umTrue,
   umOpenInverted = umOpen Or umInverted 

// a pin structure...
Structure TPin
   AddrPort As Word
   Pin, PinMask As Byte
End Structure 

// private variables and aliases... 
Dim 
   FTX As TPin,                       // TX pin
   FRX As TPin,                       // RX pin
   FBaudrate As Word,                 // Baudrate (1 BT)
   FHalfBaudrate As Word,             // Half Baudrate (1/2 BT)
   FMode As Byte,                     // Mode (true, inverted)
   FPacing As Word,                   // write byte pacing (us)
   FTerminator As Char,               // read string terminator character
   FIsInverted As FMode.Booleans(1)   // inverted mode flag

// public variables...
Public Dim
   FTimeOut As Boolean,
   ReadTerminator As FTerminator,     // read string terminator
   Pacing As FPacing                  // write pacing (us) 
{
****************************************************************************
* Name    : SetTX                                                          *
* Purpose : Set TX port and pin number                                     *
****************************************************************************
}
Public Sub SetTX(ByRef pPin As Bit)
   FTX.AddrPort = AddressOf(pPin) + 9 // get pin (latch address)
   FTX.Pin = BitOf(pPin)              // get pin number
   FTX.PinMask = Not FTX.Pin          // create pin number mask
End Sub
{
****************************************************************************
* Name    : SetRX                                                          *
* Purpose : Set RX port and pin number                                     *
****************************************************************************
}
Public Sub SetRX(ByRef pPin As Bit)
   FRX.AddrPort = AddressOf(pPin)     // get pin (port address)
   FRX.Pin = BitOf(pPin)              // get pin number
   FRX.PinMask = Not FRX.Pin          // create pin number mask
   FSR0  = FRX.AddrPort + 18          // point FSR0 to TRIS
   INDF0 = INDF0 Or FRX.Pin           // set to input
End Sub
{
****************************************************************************
* Name    : SetBaudrate                                                    *
* Purpose : Sets the software UART baudrate                                *
*         : Pass a 'sbr' constant, as defined above. For example, sbr9600  *
****************************************************************************
}
Public Sub SetBaudrate(pBaudrate As Word)
   FBaudrate = pBaudrate
   FHalfBaudrate = FBaudrate / 2
End Sub
{
****************************************************************************
* Name    : SetMode                                                        *
* Purpose : Set the UART mode, either TRUE or INVERTED. Use a 'um'         *
*         : constant, as defined above. For example umTrue. Module default *
*         : is umInverted                                                  *
****************************************************************************
}
Public Sub SetMode(pMode As Byte)
   FMode = pMode
End Sub
{
****************************************************************************
* Name    : OneBT (PRIVATE)                                                *
* Purpose : One bit time                                                   *
****************************************************************************
}
NoInline Sub OneBT()
   DelayUS(FBaudrate)
End Sub  
{
****************************************************************************
* Name    : HalfBT (PRIVATE)                                               *
* Purpose : One half bit time                                              *
****************************************************************************
}
NoInline Sub HalfBT()
   DelayUS(FHalfBaudrate)
End Sub 
{
****************************************************************************
* Name    : ReadByte                                                       *
* Purpose : Read a byte from the software UART                             *
****************************************************************************
}
Public Function Access ReadByte() As Byte
   Dim Index, Pin, Mode, timeout As Byte
   
   // get port, pin and mode...
   Pin = FRX.Pin
   FSR0 = FRX.AddrPort
   Mode = FMode
   Index = 8
   timeout=0
   result=0
  
ASM-
   ; look For start Bit...
   infsnz  timeout
   return  0
   movf    INDF0, W         ; <Read Bit>
   btfsc   Mode, 1                 
   comf    WREG             ; <- changed from "W"
   andwf   Pin, W                      
   btfss   STATUS, Z               
   bra     $ - 14                     
End ASM
   HalfBT                   
ASM-
   movf    INDF0, W         ; <Read Bit>
   btfsc   Mode, 1          
   comf    WREG             ; <- changed from "W"
   andwf   Pin, W           
   btfss   STATUS, Z        
   bra     $ - 28 

   ; Read data bits...
   call    OneBT            ; 2 + BT + 2  | 20
   movf    INDF0, W         ; 1           | ** <Read Pin>
   bcf     STATUS, C        ; 1           | 1
   andwf   Pin, W           ; 1           | 2
   btfss   STATUS, Z        ; 1 (2)       | 3 (4)
   bsf     STATUS, C        ; 1           | 4
   rrcf    result, F        ; 1           | 5
   bra     $ + 2            ; 2           | 7
   bra     $ + 2            ; 2           | 9
   bra     $ + 2            ; 2           | 11
   bra     $ + 2            ; 2           | 13
   decfsz  Index, F         ; 1 (2)       | 14 (15)
   bra     $ - 26           ; 2           | 16
End ASM
   OneBT
   If FIsInverted Then
      result = Not result
   EndIf  
   FTimeOut=false
   ClrWDT   
End Function

Public Function ReadByte(pTimeout As Byte) As Byte
    FTimeOut=true
    Repeat
        Dec(pTimeout)
        result=ReadByte()
    Until Not FTimeOut Or pTimeout=0
End Function

{
****************************************************************************
* Name    : WriteByte                                                      *
* Purpose : Write a byte to the software UART                              *
****************************************************************************
}
Public Sub Access WriteByte(pValue As Byte)  
   Dim Index, Mode, ModeOpen As Byte
   Dim Pin, PinMask As Byte
   Dim MakeHigh As Pin, MakeLow As PinMask
   Dim MakeInput As Pin, MakeOutput As PinMask, SaveTRIS As ModeOpen
   Dim DATAPort As INDF0, TRISPort As INDF1
   
   // set mode...
   Mode = FMode
   ModeOpen = 0
   If FIsInverted Then
      ModeOpen = FTX.Pin
      pValue = Not pValue
   EndIf  
   Pin = FTX.Pin              // pin mask
   PinMask = FTX.PinMask      // inverse mask
   FSR0 = FTX.AddrPort        // port address (latch)
   FSR1 = FTX.AddrPort + 9    // TRIS address
   Index = 9                  // 1 start, 8 data bits
ASM-
   ; start Bit...
   bsf    STATUS, C       ; start Bit is High
   btfsc  Mode, 0         ; is it true mode
   bcf    STATUS, C       ; yes, start Bit is Low
   bra    $ + 4           ; GoTo Write Bit

   ; main loop...
   rrcf   pValue, F       ; 1           | 8

   ; set direction...  
   movf   TRISPort, W     ; 1           | 9
   andwf  MakeOutput, W   ; 1           | 10   
   btfsc  STATUS, C       ; 1 (2)       | 11 (12)
   iorwf  MakeInput, W    ; 1           | 12 
   xorwf  ModeOpen, W     ; 1           | 13
   btfss  Mode, 2         ; 1 (2)       | 14 (15)
   andwf  MakeOutput, W   ; 1           | 15
   movwf  TRISPort        ; 1           | 16 <set tris>

   ; set Bit value...
   movf   DataPort, W     ; 1           | 17
   andwf  MakeLow, W      ; 1           | 18 
   btfsc  STATUS, C       ; 1 (2)       | 19 (20)
   iorwf  MakeHigh, W     ; 1           | 20 
   movwf  DataPort        ; 1           | ** <Write Pin>
   call   OneBT           ; 2 + BT + 2  | 4
   decfsz Index, F        ; 1 (2)       | 5 (6)
   bra    $ - 34          ; 2           | 7

   ; stop Bit, set direction...
   movf   TRISPort, W     ; 1           | 8
   iorwf  MakeInput, W    ; 1           | 9
   btfss  Mode, 2         ; 1 (2)       | 10 (11)
   andwf  MakeOutput, W   ; 1           | 11
   movwf  SaveTRIS        ; 1           | 12 

   ; stop Bit, data...
   movf   INDF0, W        ; 1           | 13
   andwf  MakeLow, W      ; 1           | 14
   btfsc  Mode, 0         ; 1 (2)       | 15 (16)
   iorwf  MakeHigh, W     ; 1           | 16
   bra    $ + 2           ; 2           | 18
   movff  SaveTRIS, INDF1 ; 2           | 20 <set tris>
   movwf  INDF0           ; 1           | ** <Write Pin>
End ASM

   // return from ASM and make direct call - also ensures
   // that subroutine is linked into program to allow above
   // call from within the ASM block...
   OneBT
   If FPacing > 0 Then
      DelayUS(FPacing)
   EndIf 
   ClrWDT  
End Sub
{
****************************************************************************
* Name    : WriteBoolean                                                   *
* Purpose : Write a boolean value to the software UART                     *
****************************************************************************
}
Public Sub WriteBoolean(pValue As Boolean)
   WriteByte(Byte(pValue))
End Sub
{
****************************************************************************
* Name    : WriteWord                                                      *
* Purpose : Write a word value to the software UART                        *
****************************************************************************
}
Public Sub WriteWord(pValue As Word)
   WriteByte(pValue.Bytes(0))
   WriteByte(pValue.Bytes(1))
End Sub
{
****************************************************************************
* Name    : WriteLongWord                                                  *
* Purpose : Write a word value to the software UART                        *
****************************************************************************
}
Public Sub WriteLongWord(pValue As LongWord) 
   WriteByte(pValue.Bytes(0))
   WriteByte(pValue.Bytes(1))
   WriteByte(pValue.Bytes(2))
   WriteByte(pValue.Bytes(3))
End Sub
{
****************************************************************************
* Name    : WriteFloat                                                     *
* Purpose : Write a floating point number to the software UART             *
****************************************************************************
}
Public Sub WriteFloat(pValue As Float) 
   WriteByte(pValue.Bytes(0))
   WriteByte(pValue.Bytes(1))
   WriteByte(pValue.Bytes(2))
   WriteByte(pValue.Bytes(3))
End Sub
{
****************************************************************************
* Name    : ReadItem (OVERLOAD)                                            *
* Purpose : Read a byte from the software UART                             *
****************************************************************************
}
'Sub ReadItem(ByRef pValue As Byte)
'   pValue = ReadByte
'End Sub  
{
****************************************************************************
* Name    : ReadItem (OVERLOAD)                                            *
* Purpose : Read a shortint from the software UART                         *
****************************************************************************
}
'Sub ReadItem(ByRef pValue As ShortInt)
'   pValue = ReadByte
'End Sub  
{
****************************************************************************
* Name    : ReadItem (OVERLOAD)                                            *
* Purpose : Read a word from the software UART                             *
****************************************************************************
}
'Sub ReadItem(ByRef pValue As Word)
'   pValue = ReadWord
'End Sub
{
****************************************************************************
* Name    : ReadItem (OVERLOAD)                                            *
* Purpose : Read an integer from the software UART                         *
****************************************************************************
}
'Sub ReadItem(ByRef pValue As Integer)
'   pValue = ReadWord
'End Sub
{
****************************************************************************
* Name    : ReadItem (OVERLOAD)                                            *
* Purpose : Read a long word from the software UART                        *
****************************************************************************
}
'Sub ReadItem(ByRef pValue As LongWord)
'   pValue = ReadLongWord
'End Sub
{
****************************************************************************
* Name    : ReadItem (OVERLOAD)                                            *
* Purpose : Read a long integer from the software UART                     *
****************************************************************************
}
'Sub ReadItem(ByRef pValue As LongInt)
'   pValue = ReadLongWord
'End Sub
{
****************************************************************************
* Name    : ReadItem (OVERLOAD)                                            *
* Purpose : Read a floating point number from the software UART            *
****************************************************************************
}
'Sub ReadItem(ByRef pValue As Float) 
'   pValue = ReadFloat
'End Sub
{
****************************************************************************
* Name    : ReadItem (OVERLOAD)                                            *
* Purpose : Read a string from the software UART. Use ReadTerminator       *
*         : to specify the input string terminator character.              *
****************************************************************************
}
Sub ReadItem(ByRef pText As String)
   Dim Data, Index As Byte
   
   Index = 0
   Data=ReadByte(100)
   If Not FTimeOut Then
       While Data <> Byte(FTerminator) And Not FTimeOut
          pText(Index) = Data
          Data=ReadByte(100)
          Inc(Index)
       Wend
   EndIf
   pText(Index) = null
End Sub
{
****************************************************************************
* Name    : Read (COMPOUND)                                                *
* Purpose : Read an item from the software UART                            *
****************************************************************************
}
Public Compound Sub Read(ReadItem)
{
****************************************************************************
* Name    : WriteItem (OVERLOAD)                                           *
* Purpose : Write a boolean value to the software UART                     *
****************************************************************************
}
Sub WriteItem(pValue As Boolean)
   WriteByte(Byte(pValue))
End Sub
{
****************************************************************************
* Name    : WriteItem (OVERLOAD)                                           *
* Purpose : Write a byte value to the software UART                        *
****************************************************************************
}
Sub WriteItem(pValue As WREG)
   WriteByte(pValue)
End Sub
{
****************************************************************************
* Name    : WriteItem (OVERLOAD)                                           *
* Purpose : Write a short int value to the software UART                   *
****************************************************************************
}
Sub WriteItem(pValue As ShortInt)
   WriteByte(pValue)
End Sub
{
****************************************************************************
* Name    : WriteItem (OVERLOAD)                                           *
* Purpose : Write a word value to the software UART                        *
****************************************************************************
}
Sub WriteItem(pValue As Word) 
   WriteWord(pValue)
End Sub
{
****************************************************************************
* Name    : WriteItem (OVERLOAD)                                           *
* Purpose : Write an integer value to the software UART                    *
****************************************************************************
}
Sub WriteItem(pValue As Integer) 
   WriteWord(pValue)
End Sub
{
****************************************************************************
* Name    : WriteItem (OVERLOAD)                                           *
* Purpose : Write a long word to the software UART                         *
****************************************************************************
}
Sub WriteItem(pValue As LongWord) 
   WriteLongWord(pValue)
End Sub
{
****************************************************************************
* Name    : WriteItem (OVERLOAD)                                           *
* Purpose : Write a long integer to the software UART                      *
****************************************************************************
}
Sub WriteItem(pValue As LongInt) 
   WriteLongWord(pValue)
End Sub
{
****************************************************************************
* Name    : WriteItem (OVERLOAD)                                           *
* Purpose : Write a floating point number to the software UART             *
****************************************************************************
}
Sub WriteItem(pValue As Float) 
   WriteByte(pValue.Bytes(0))
   WriteByte(pValue.Bytes(1))
   WriteByte(pValue.Bytes(2))
   WriteByte(pValue.Bytes(3))
End Sub
{
****************************************************************************
* Name    : WriteItem (OVERLOAD)                                           *
* Purpose : Write a string value to the software UART                      *
****************************************************************************
}
Sub WriteItem(pText As String)
   Dim Index, Data As Byte  
   Index = 0
   Data = pText(0)
   While Data <> 0
      WriteByte(Data)
      Inc(Index)
      Data = pText(index)
   Wend
End Sub
{
****************************************************************************
* Name    : Write (COMPOUND)                                               *
* Purpose : Write an item to the software UART                             *
****************************************************************************
}
Public Compound Sub Write(WriteItem)

// initialise module...
FTerminator = #13
FPacing = 0
FMode = umInverted


