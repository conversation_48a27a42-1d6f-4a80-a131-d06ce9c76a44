{
****************************************************************************
* Name    : SetDigitalIO                                                   *
* Purpose : Routines to set device IO pins to analog/digital mode          *
* Version : 2.4                                                            *
* Notes   : For the latest version, visit                                  *
* http://www.sfcompiler.co.uk/wiki/pmwiki.php?n=SwordfishUser.SetDigitalIO *
*         : 2.4 - add 18FxxQ10                                             *
*         : 2.3 - add missing 18LF4xK22 to analog pin definitions          *
*               - add 18LFxxK50                                            *
*         : 2.2 - add 18LF support                                         *
*         : 2.1 - add 18FxxK40                                             *
*         : 2.0 - add SetAnalogPort() macro and ANxx pin definitions       *
*         :       add SetAllAnalog()                                       *
*         : 1.8 - add #option DigitalIO_SLEWRATE to change default SLRCON  *
*         : 1.7 - add 18F2xK50/4xK50                                       *
*         :       add J94/J99 series                                       *
*         :       add check for _IsCompilerSE define                       *
*         :       move ADSHR definition since not all devices have WDTCON  *
*         : 1.6 - change 18FxxK22 (rickado)                                *
*         :       correct 18F1230/1330 comparator setting                  *
*         : 1.5 - add 18F4xK22 (rickado)                                   *
*         : 1.4 - add write_sfr()/read_sfr() macros and add'tl devices     *
*         :       add #option SWORDFISH_SE                                 *
*         : 1.3 - add fix for ADSHR shared SFR registers (certain J series)*
*         : 1.2 - add 18FxxK20, K22, K50, K80, and K90 series              *
*         : 1.1 - add 18F46J50                                             *
****************************************************************************
}
Module SetDigitalIO

// set this option true if building with the SE compiler
#if defined(_IsCompilerSE) And (_IsCompilerSE = true)
  #option SWORDFISH_SE = true
#endif
#option SWORDFISH_SE = false

// added V1.8 for K series
// devices that have IO slew rate control (SLRCON) default to slow rate,
// which is about 1/10 the normal speed
// this changes the default to normal speed (but can be overridden)
#option DigitalIO_SLEWRATE = 0      // 0=normal, 1=slow (SLRCON default)
#if (DigitalIO_SLEWRATE = 0)
  Const _SLR = 0
#else
  Const _SLR = $ff
#endif

// added in V2.0 to enable/disable SetAnalogPort macro code
// (not supported by SE version... would require nested macros)
#if (SWORDFISH_SE = true)
  #option DigitalIO_AnalogPort = false      // disable SetAnalogPort
#endif
#option DigitalIO_AnalogPort = true         // enable SetAnalogPort

//
//--------------------------------------------------------------------------
// these macros are used to read and write SFR's that are located in the upper
// bank (bank 15), but are not part of the access bank
//
// since the SE compiler does not contain code to set the bank select register, 
// setting '#option SWORDFISH_SE = true' will enable the write_sfr()/read_sfr()
// macros to generate a MOVFF instruction so that setting the BSR is not required
//--------------------------------------------------------------------------
//
'Public Macro write_sfr(sfr, val)
'  #if (SWORDFISH_SE)
'    WREG = val
'    ASM
'        movff wreg, sfr
'    End ASM
'  #else
'    sfr = val
'  #endif
'End Macro

'Public Macro read_sfr(sfr, bvar)
'  #if (SWORDFISH_SE)
'    ASM
'        movff sfr, bvar
'    End ASM
'  #else
'    bvar = sfr
'  #endif
'End Macro

//
//--------------------------------------------------------------------------
// set all analog IO pins to digital function
//--------------------------------------------------------------------------
//
Public Sub SetAllDigital()
  // devices are grouped by datasheet where appropriate

  // 7 channels, bit field...
  #if _device in (18F1220, 18F1320, 18LF1220, 18LF1320)
    ADCON1 = $7F

  // 4 channels
  #elseif _device in (18F1230, 18F1330, 18LF1230, 18LF1330)
    ADCON1 = $00
    CMCON = $00         // changed V1.6

  // 5 channels, ANSEL...
  #elseif _device in (18F2331, 18F2431, 18LF2331, 18LF2431)
    ANSEL0 = $00

  // 9 channels, ANSEL...
  #elseif _device in (18F4331, 18F4431, 18LF4331, 18LF4431)
    ANSEL0 = $00
    ANSEL1.bits(0) = 0

  // J10/J15 family... (no LF versions)
  #elseif _device in (18F65J10, 18F65J15, 18F66J10, 18F66J15, 18F67J10, 18F85J10, 18F85J15, 18F86J10, 18F86J15, 18F87J10)
    ADCON1 = $0F
    CMCON = 0

  // J11 family... (no LF versions)
  #elseif _device in (18F63J11, 18F64J11, 18F65J11, 18F83J11, 18F84J11, 18F85J11)
    ADCON1 = $0F
    CMCON = 0

  // J11 family... (shared SFR addresses)  (no LF versions)
  #elseif _device in (18F66J11, 18F67J11, 18F86J11, 18F87J11)
    Dim ADSHR As WDTCON.4         // alternate register address bit present on certain devices
    // registers $0F5A - $0F5F are not part of the access bank
    ADSHR = 1
    ANCON0 = $FF
    ANCON1 = $FF
    ADSHR = 0
    CM1CON = 0
    CM2CON = 0

  // J16 family... (shared SFR addresses)
  #elseif _device in (18F66J16, 18F86J16)
    Dim ADSHR As WDTCON.4         // alternate register address bit present on certain devices
    // registers $0F5A - $0F5F are not part of the access bank
    ADSHR = 1
    ANCON0 = $FF
    ANCON1 = $FF
    ADSHR = 0
    CM1CON = 0
    CM2CON = 0

  // J50 family... (shared SFR addresses) (no LF versions)
  #elseif _device in (18F65J50, 18F66J50, 18F67J50, 18F85J50, 18F86J50, 18F87J50)
    Dim ADSHR As WDTCON.4         // alternate register address bit present on certain devices
    // registers $0F40 - $0F5F are not part of the access bank
    ADSHR = 1
    ANCON0 = $FF
    ANCON1 = $FF
    ADSHR = 0
    CM1CON = 0
    CM2CON = 0

  // J55 family... (shared SFR addresses)  (no LF versions)
  #elseif _device in (18F66J55, 18F86J55)
    Dim ADSHR As WDTCON.4         // alternate register address bit present on certain devices
    // registers $0F40 - $0F5F are not part of the access bank
    ADSHR = 1
    ANCON0 = $FF
    ANCON1 = $FF
    ADSHR = 0
    CM1CON = 0
    CM2CON = 0

  // J50 family...
  #elseif _device in (18F24J50, 18F25J50, 18F26J50, 18F44J50, 18F45J50, 18F46J50) Or _
          _device in (18LF24J50, 18LF25J50, 18LF26J50, 18LF44J50, 18LF45J50, 18LF46J50)
    // registers $0EC0 - $0F5F are not part of the access bank
    write_sfr(ANCON0, $FF)
    write_sfr(ANCON1, $1F)
    CM1CON = 0
    CM2CON = 0

  // J53 family...
  #elseif _device in (18F26J53, 18F27J53, 18F46J53, 18F47J53, 18LF26J53, 18LF27J53, 18LF46J53, 18LF47J53)
    // registers $0EB0 - $0F5F are not part of the access bank
    write_sfr(ANCON0, $FF)
    write_sfr(ANCON1, $1F)
    CM1CON = 0
    CM2CON = 0

  // J94 family...       // added V1.7 (no LF versions)
  #elseif _device in (18F65J94, 18F66J94, 18F67J94, 18F85J94, 18F86J94, 18F87J94, 18F95J94, 18F96J94, 18F97J94)
    // registers $0DFA - $0F5F are not part of the access bank
    write_sfr(ANCON1, $00)
    write_sfr(ANCON2, $00)
    write_sfr(ANCON3, $00)
    write_sfr(CM1CON, 0)
    write_sfr(CM2CON, 0)
    write_sfr(CM3CON, 0)

  // J99 family...       // added V1.7 (no LF versions)
  #elseif _device in (18F66J99, 18F86J99, 18F96J99)
    // registers $0DFA - $0F5F are not part of the access bank
    write_sfr(ANCON1, $00)
    write_sfr(ANCON2, $00)
    write_sfr(ANCON3, $00)
    write_sfr(CM1CON, 0)
    write_sfr(CM2CON, 0)
    write_sfr(CM3CON, 0)

  // 18FxxK20 family (no LF versions)
  #elseif _device in (18F23K20, 18F24K20, 18F25K20, 18F26K20, 18F43K20, 18F44K20, 18F45K20, 18F46K20)
    ANSEL = $00
    ANSELH = $00
    CM1CON0 = 0
    CM2CON0 = 0
    SLRCON = _SLR               // added V1.8
    
  // 18FxxK22 family
  // 18F1xK22
  #elseif _device in (18F13K22, 18F14K22, 18LF13K22, 18LF14K22)
    // registers $0F53 - $0F5F are not part of the access bank
    ANSEL = $00
    ANSELH = $00
    CM1CON0 = 0
    CM2CON0 = 0
    VREFCON1 = $00              // added V1.6
    SLRCON = _SLR               // added V1.8

  // 18F2xK22/18F4xK22
  #elseif _device in (18F23K22, 18F24K22, 18F25K22, 18F26K22, 18LF23K22, 18LF24K22, 18LF25K22, 18LF26K22)
    // registers $0F38 - $0F5F are not part of the access bank
    write_sfr(ANSELA, $00)
    write_sfr(ANSELB, $00)
    write_sfr(ANSELC, $00)
    CM1CON0 = %********         // changed V1.6
    CM2CON0 = %********
    VREFCON1 = $00              // added V1.6
    SLRCON = _SLR               // added V1.8

  #elseif _device in (18F43K22, 18F44K22, 18F45K22, 18F46K22, 18LF43K22, 18LF44K22, 18LF45K22, 18LF46K22) // added V1.5
    // registers $0F38 - $0F5F are not part of the access bank
    write_sfr(ANSELA, $00)
    write_sfr(ANSELB, $00)
    write_sfr(ANSELC, $00)
    write_sfr(ANSELD, $00)
    write_sfr(ANSELE, $00)
    CM1CON0 = %********         // changed V1.6
    CM2CON0 = %********
    VREFCON1 = $00              // added V1.6
    SLRCON = _SLR               // added V1.8

  // 18F87K22 family (no LF versions)
  #elseif _device in (18F65K22, 18F66K22, 18F67K22, 18F85K22, 18F86K22, 18F87K22)
    // registers $0F16 - $0F5F are not part of the access bank
    write_sfr(ANCON0, $00)
    write_sfr(ANCON1, $00)
    write_sfr(ANCON2, $00)
    CM1CON = 0
    CM2CON = 0
    CM3CON = 0
    CVRCON = $00                // added V1.6

  // 18FxxK40 family added V2.1
  // 18F2xK40
  #elseif _device in (18F24K40, 18F25K40, 18F26K40, 18F27K40, 18LF24K40, 18LF25K40, 18LF26K40, 18LF27K40)
    write_sfr(ANSELA, $00)
    write_sfr(ANSELB, $00)
    write_sfr(ANSELC, $00)
    write_sfr(CM1CON0, %********)
    write_sfr(CM2CON0, %********)
    write_sfr(ADREF, $00)
    write_sfr(SLRCONA, _SLR)
    write_sfr(SLRCONB, _SLR)
    write_sfr(SLRCONC, _SLR)

  // 18F4xK40
  #elseif _device in (18F45K40, 18F46K40, 18F47K40, 18LF45K40, 18LF46K40, 18LF47K40)
    write_sfr(ANSELA, $00)
    write_sfr(ANSELB, $00)
    write_sfr(ANSELC, $00)
    write_sfr(ANSELD, $00)
    write_sfr(ANSELE, $00)
    write_sfr(CM1CON0, %********)
    write_sfr(CM2CON0, %********)
    write_sfr(ADREF, $00)
    write_sfr(SLRCONA, _SLR)
    write_sfr(SLRCONB, _SLR)
    write_sfr(SLRCONC, _SLR)
    write_sfr(SLRCOND, _SLR)
    write_sfr(SLRCONE, _SLR)

  // 18F6xK40
  #elseif _device in (18F65K40, 18F66K40, 18F67K40, 18LF65K40, 18LF66K40, 18LF67K40)
    write_sfr(ANSELA, $00)
    write_sfr(ANSELB, $00)
    write_sfr(ANSELD, $00)
    write_sfr(ANSELE, $00)
    write_sfr(ANSELF, $00)
    write_sfr(ANSELG, $00)
    write_sfr(CM1CON0, %********)
    write_sfr(CM2CON0, %********)
    write_sfr(ADREF, $00)
    write_sfr(SLRCONA, _SLR)
    write_sfr(SLRCONB, _SLR)
    write_sfr(SLRCONC, _SLR)
    write_sfr(SLRCOND, _SLR)
    write_sfr(SLRCONE, _SLR)
    write_sfr(SLRCONF, _SLR)
    write_sfr(SLRCONH, _SLR)

  // 18FxxK50 family
  #elseif _device in (18F13K50, 18F14K50, 18LF13K50, 18LF14K50)
    // registers $0F53 - $0F5F are not part of the access bank
    write_sfr(ANSEL, $00)
    write_sfr(ANSELH, $00)
    write_sfr(CM1CON0, 0)
    write_sfr(CM2CON0, 0)
    write_sfr(SLRCON, _SLR)               // added V1.8

  #elseif _device in (18F24K50, 18F25K50, 18LF24K50, 18LF25K50)     // added V1.7, LF V2.3
    // registers $0F53 - $0F5F are not part of the access bank
    write_sfr(ANSELA, $00)
    write_sfr(ANSELB, $00)
    write_sfr(ANSELC, $00)
    write_sfr(CM1CON0, 0)
    write_sfr(CM2CON0, 0)
    write_sfr(SLRCON, _SLR)               // added V1.8

  #elseif _device in (18F45K50, 18LF45K50)                          // added V1.7, LF V2.3
    // registers $0F53 - $0F5F are not part of the access bank
    write_sfr(ANSELA, $00)
    write_sfr(ANSELB, $00)
    write_sfr(ANSELC, $00)
    write_sfr(ANSELD, $00)
    write_sfr(ANSELE, $00)
    write_sfr(CM1CON0, 0)
    write_sfr(CM2CON0, 0)
    write_sfr(SLRCON, _SLR)               // added V1.8

  // 18FxxK80 family
  #elseif _device in (18F25K80, 18F26K80, 18F45K80, 18F46K80, 18F65K80, 18F66K80) Or _
          _device in (18LF25K80, 18LF26K80, 18LF45K80, 18LF46K80, 18LF65K80, 18LF66K80)
    // registers $0E41 - $0F5F are not part of the access bank
    write_sfr(ANCON0, $00)
    write_sfr(ANCON1, $00)
    write_sfr(CM1CON, $00)
    write_sfr(CM2CON, $00)
    write_sfr(SLRCON, _SLR)               // added V1.8

  // 18FxxK90 family
  #elseif _device in (18F65K90, 18F66K90, 18F67K90, 18F85K90, 18F86K90, 18F87K90)
    // registers $0EF4 - $0F5F are not part of the access bank
    write_sfr(ANCON0, $00)
    write_sfr(ANCON1, $00)
    write_sfr(ANCON2, $00)
    write_sfr(CM1CON, $00)
    write_sfr(CM2CON, $00)
    write_sfr(CM3CON, $00)

  // 18FxxQ10 family added V2.4
  // 18F2xQ10
  #elseif _device in (18F24Q10, 18F25Q10, 18F26Q10, 18F27Q10)
    write_sfr(ANSELA, $00)
    write_sfr(ANSELB, $00)
    write_sfr(ANSELC, $00)
    write_sfr(CM1CON0, $00)
    write_sfr(CM2CON0, $00)
    write_sfr(ADREF, $00)
    write_sfr(SLRCONA, _SLR)
    write_sfr(SLRCONB, _SLR)
    write_sfr(SLRCONC, _SLR)

  // 18F4xQ10
  #elseif _device in (18F45Q10, 18F46Q10, 18F47Q10)
    write_sfr(ANSELA, $00)
    write_sfr(ANSELB, $00)
    write_sfr(ANSELC, $00)
    write_sfr(ANSELD, $00)
    write_sfr(ANSELE, $00)
    write_sfr(CM1CON0, $00)
    write_sfr(CM2CON0, $00)
    write_sfr(ADREF, $00)
    write_sfr(SLRCONA, _SLR)
    write_sfr(SLRCONB, _SLR)
    write_sfr(SLRCONC, _SLR)
    write_sfr(SLRCOND, _SLR)
    write_sfr(SLRCONE, _SLR)

  // 8 - 13 channels - 0 comp
  #elseif _comparator = 0
    ADCON1 = $0F

  // assume default is ok...
  #else
    ADCON1 = $0F
    CMCON = $07
  #endif
End Sub

//
//--------------------------------------------------------------------------
// set all analog IO pins to analog function
//--------------------------------------------------------------------------
//
Public Sub SetAllAnalog()
  // devices are grouped by datasheet where appropriate

  // 7 channels, ADCON1...
  #if _device in (18F1220, 18F1320, 18LF1220, 18LF1320)
    ADCON1 = $00

  // 4 channels
  #elseif _device in (18F1230, 18F1330, 18LF1230, 18LF1330)
    ADCON1 = $0F

  // 5 channels, ANSEL...
  #elseif _device in (18F2331, 18F2431, 18LF2331, 18LF2431)
    ANSEL0 = $1F

  // 9 channels, ANSEL...
  #elseif _device in (18F4331, 18F4431, 18LF4331, 18LF4431)
    ANSEL0 = $FF
    ANSEL1.bits(0) = 1

  // J10/J15 family...
  #elseif _device in (18F65J10, 18F65J15, 18F66J10, 18F66J15, 18F67J10, 18F85J10, 18F85J15, 18F86J10, 18F86J15, 18F87J10)
    ADCON1 = $00

  // J11 family...
  #elseif _device in (18F63J11, 18F64J11, 18F65J11, 18F83J11, 18F84J11, 18F85J11)
    ADCON1 = $00

  // J11 family... (shared SFR addresses)
  #elseif _device in (18F66J11, 18F67J11, 18F86J11, 18F87J11)
    Dim ADSHR As WDTCON.4         // alternate register address bit present on certain devices
    // registers $0F5A - $0F5F are not part of the access bank
    ADSHR = 1
    ANCON0 = $00
    ANCON1 = $00
    ADSHR = 0

  // J16 family... (shared SFR addresses)
  #elseif _device in (18F66J16, 18F86J16)
    Dim ADSHR As WDTCON.4         // alternate register address bit present on certain devices
    // registers $0F5A - $0F5F are not part of the access bank
    ADSHR = 1
    ANCON0 = $00
    ANCON1 = $00
    ADSHR = 0

  // J50 family... (shared SFR addresses)
  #elseif _device in (18F65J50, 18F66J50, 18F67J50, 18F85J50, 18F86J50, 18F87J50)
    Dim ADSHR As WDTCON.4         // alternate register address bit present on certain devices
    // registers $0F40 - $0F5F are not part of the access bank
    ADSHR = 1
    ANCON0 = $00
    ANCON1 = $00
    ADSHR = 0

  // J55 family... (shared SFR addresses)
  #elseif _device in (18F66J55, 18F86J55)
    Dim ADSHR As WDTCON.4         // alternate register address bit present on certain devices
    // registers $0F40 - $0F5F are not part of the access bank
    ADSHR = 1
    ANCON0 = $00
    ANCON1 = $00
    ADSHR = 0

  // J50 family...
  #elseif _device in (18F24J50, 18F25J50, 18F26J50, 18F44J50, 18F45J50, 18F46J50) Or _
          _device in (18LF24J50, 18LF25J50, 18LF26J50, 18LF44J50, 18LF45J50, 18LF46J50)
    // registers $0EC0 - $0F5F are not part of the access bank
    write_sfr(ANCON0, $00)
    write_sfr(ANCON1, $00)

  // J53 family...
  #elseif _device in (18F26J53, 18F27J53, 18F46J53, 18F47J53, 18LF26J53, 18LF27J53, 18LF46J53, 18LF47J53)
    // registers $0EB0 - $0F5F are not part of the access bank
    write_sfr(ANCON0, $00)
    write_sfr(ANCON1, $00)

  // J94 family...       // added V1.7
  #elseif _device in (18F65J94, 18F66J94, 18F67J94, 18F85J94, 18F86J94, 18F87J94, 18F95J94, 18F96J94, 18F97J94)
    // registers $0DFA - $0F5F are not part of the access bank
    write_sfr(ANCON1, $FF)
    write_sfr(ANCON2, $FF)
    write_sfr(ANCON3, $FF)

  // J99 family...       // added V1.7
  #elseif _device in (18F66J99, 18F86J99, 18F96J99)
    // registers $0DFA - $0F5F are not part of the access bank
    write_sfr(ANCON1, $FF)
    write_sfr(ANCON2, $FF)
    write_sfr(ANCON3, $FF)

  // 18FxxK20 family
  #elseif _device in (18F23K20, 18F24K20, 18F25K20, 18F26K20, 18F43K20, 18F44K20, 18F45K20, 18F46K20)
    ANSEL = $FF
    ANSELH = $1F
    
  // 18FxxK22 family
  // 18F1xK22
  #elseif _device in (18F13K22, 18F14K22, 18LF13K22, 18LF14K22)
    // registers $0F53 - $0F5F are not part of the access bank
    ANSEL = $FF
    ANSELH = $0F

  // 18F2xK22/18F4xK22
  #elseif _device in (18F23K22, 18F24K22, 18F25K22, 18F26K22, 18LF23K22, 18LF24K22, 18LF25K22, 18LF26K22)
    // registers $0F38 - $0F5F are not part of the access bank
    write_sfr(ANSELA, $2F)
    write_sfr(ANSELB, $3F)
    write_sfr(ANSELC, $FC)

  #elseif _device in (18F43K22, 18F44K22, 18F45K22, 18F46K22, 18LF43K22, 18LF44K22, 18LF45K22, 18LF46K22)
    // registers $0F38 - $0F5F are not part of the access bank
    write_sfr(ANSELA, $2F)
    write_sfr(ANSELB, $3F)
    write_sfr(ANSELC, $FC)
    write_sfr(ANSELD, $FF)
    write_sfr(ANSELE, $07)

  // 18F87K22 family
  #elseif _device in (18F65K22, 18F66K22, 18F67K22, 18F85K22, 18F86K22, 18F87K22)
    // registers $0F16 - $0F5F are not part of the access bank
    write_sfr(ANCON0, $FF)
    write_sfr(ANCON1, $FF)
    write_sfr(ANCON2, $FF)

  // 18FxxK40 family added V2.1
  // 18F2xK40
  #elseif _device in (18F24K40, 18F25K40, 18F26K40, 18F27K40, 18LF24K40, 18LF25K40, 18LF26K40, 18LF27K40)
    write_sfr(ANSELA, $FF)
    write_sfr(ANSELB, $FF)
    write_sfr(ANSELC, $FF)

  // 18F4xK40
  #elseif _device in (18F45K40, 18F46K40, 18F47K40, 18LF45K40, 18LF46K40, 18LF47K40)
    write_sfr(ANSELA, $FF)
    write_sfr(ANSELB, $FF)
    write_sfr(ANSELC, $FF)
    write_sfr(ANSELD, $FF)
    write_sfr(ANSELE, $FF)

  // 18F6xK40
  #elseif _device in (18F65K40, 18F66K40, 18F67K40, 18LF65K40, 18LF66K40, 18LF67K40)
    write_sfr(ANSELA, $FF)
    write_sfr(ANSELB, $FF)
    write_sfr(ANSELD, $FF)
    write_sfr(ANSELE, $FF)
    write_sfr(ANSELF, $FF)
    write_sfr(ANSELG, $FF)

  // 18FxxK50 family
  #elseif _device in (18F13K50, 18F14K50, 18LF13K50, 18LF14K50)
    // registers $0F53 - $0F5F are not part of the access bank
    ANSEL = $F8
    ANSELH = $0F

  #elseif _device in (18F24K50, 18F25K50, 18LF24K50, 18LF25K50)     // added V1.7, LF V2.3
    // registers $0F53 - $0F5F are not part of the access bank
    write_sfr(ANSELA, $2F)
    write_sfr(ANSELB, $3F)
    write_sfr(ANSELC, $C4)

  #elseif _device in (18F45K50, 18LF45K50)                          // added V1.7, LF V2.3
    // registers $0F53 - $0F5F are not part of the access bank
    write_sfr(ANSELA, $2F)
    write_sfr(ANSELB, $3F)
    write_sfr(ANSELC, $C4)
    write_sfr(ANSELD, $FF)
    write_sfr(ANSELE, $07)

  // 18FxxK80 family
  #elseif _device in (18F25K80, 18F26K80, 18F45K80, 18F46K80, 18F65K80, 18F66K80) Or _
          _device in (18LF25K80, 18LF26K80, 18LF45K80, 18LF46K80, 18LF65K80, 18LF66K80)
    // registers $0E41 - $0F5F are not part of the access bank
    write_sfr(ANCON0, $FF)
    write_sfr(ANCON1, $FF)

  // 18FxxK90 family
  #elseif _device in (18F65K90, 18F66K90, 18F67K90, 18F85K90, 18F86K90, 18F87K90)
    // registers $0EF4 - $0F5F are not part of the access bank
    write_sfr(ANCON0, $FF)
    write_sfr(ANCON1, $FF)
    write_sfr(ANCON2, $FF)

  // 18FxxQ10 family added V2.4
  // 18F2xQ10
  #elseif _device in (18F24Q10, 18F25Q10, 18F26Q10, 18F27Q10)
    // registers $0F0C - $0F1C are not part of the access bank
    write_sfr(ANSELA, $FF)
    write_sfr(ANSELB, $FF)
    write_sfr(ANSELC, $FF)

  // 18F4xQ10
  #elseif _device in (18F45Q10, 18F46Q10, 18F47Q10)
    // registers $0F0C - $0F29 are not part of the access bank
    write_sfr(ANSELA, $FF)
    write_sfr(ANSELB, $FF)
    write_sfr(ANSELC, $FF)
    write_sfr(ANSELD, $FF)
    write_sfr(ANSELE, $FF)

  // assume default is ok...
  #else
    ADCON1 = $00
  #endif
End Sub

#if (DigitalIO_AnalogPort = true)
//
//--------------------------------------------------------------------------
// SetAnalogPort(an, mode)
// set analog port configuration register bit to analog/digital mode
// parameters:
//      an      constant, 0-34 (or one of the 'ANxx' constants defined below)
//      mode    ANA/DIG (or 0/1)
//
// example macro usage:
//  SetAnalogPort(AN0, ANA)
//  SetAnalogPort(AN23, DIG)
//
// This routine allows you to treat the analog port configuration register(s)
// as one big set of 8-bit registers and it will determine the proper 
// ADCONx/ANCONx/ANSEL/ANSELH/ANSELn register and bit setting based on the 
// 'an' and 'mode' parameters.
//
// The 'an' parameter varies from device to device depending on how the device
// maps the analog port configuration bits to the actual pins. The easiest
// way to use the macro is to use one of the predefined 'ANxx' constants below
// for your device, which you can get from the datasheet (usually there is
// a table of pins vs functions that will show you the ANx number for the desired
// pin). 
// Using the 18F26K22 as an example, port pin RA5 is mapped to analog function
// AN4, so you would use 'SetAnalogPort(AN4, ANA)' to set the pin mode to analog.
// Likewise, port pin RC2 is analog AN14, so you would use 'SetAnalogPort(AN14, ANA)'
// You can also use a bit number (from 0-34) for 'an', but you'll have figure out 
// which bit maps to what pin, and it's not always a simple 1:1 map (check out 
// the 18F26K22 or the 18F24K50, for example).
//
// The macro can only be used with devices that use individual analog/digital
// mode select bits... some devices (like the 18F4520) use an encoded setting 
// where only certain combinations are valid and you don't have individual 
// control.
//
// Since the routine is done as a macro and uses constants to determine what
// operation to perform, in most cases it will optimize out to a single 
// bitset/bitclear instruction
//--------------------------------------------------------------------------
//

// values used by CheckParam()
Public Const etError = 0
Public Const cpConst = $01

// 'mode' parameter constants
Public Const 
    ANA = 0,       // Enable pin in analog mode 
    DIG = 1        // Enable pin in digital mode 

// 'an' parameter constants (analog pin ANxx map)
#if _device in (18F1220, 18F1320, 18LF1220, 18LF1320)
Public Const
    // ADCON1
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
    AN5 = 5,
    AN6 = 6
#elseif _device in (18F1230, 18F1330, 18LF1230, 18LF1330)
Public Const
    // ADCON1
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3
#elseif _device in (18F2331, 18F2431, 18LF2331, 18LF2431)
Public Const
    // ANSEL0
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4
#elseif _device in (18F4331, 18F4431, 18LF4331, 18LF4431)
Public Const
    // ANSEL0
    AN0 = 0,    
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
    AN5 = 5,
    AN6 = 6,
    AN7 = 7,
    // ANSEL1
    AN8 = 8    
#elseif _device in (18F66J11, 18F67J11, 18F86J11, 18F87J11, 18F66J16, 18F86J16)
Public Const
    //ANCON0
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
    AN6 = 6,
    AN7 = 7, 
    // ANCON1
    AN8 = 8,
    AN9 = 9,
    AN10 = 10,
    AN11 = 11
  #if _device in (18F86J11, 18F87J11, 18F86J16)
    ,               // continue the list
    AN12 = 12,
    AN13 = 13,
    AN14 = 14,
    AN15 = 15
  #endif
#elseif _device in (18F65J50, 18F66J50, 18F67J50, 18F85J50, 18F86J50, 18F87J50, 18F66J55, 18F86J55)
Public Const
    //ANCON0
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
    AN7 = 7, 
    // ANCON1
    AN10 = 10,
    AN11 = 11
  #if _device in (18F85J50, 18F86J50, 18F87J50, 18F86J55)
    ,               // continue the list
    AN12 = 12,
    AN13 = 13,
    AN14 = 14,
    AN15 = 15
  #endif
#elseif _device in (18F24J50, 18F25J50, 18F26J50, 18F26J53, 18F27J53) Or _
        _device in (18LF24J50, 18LF25J50, 18LF26J50, 18LF26J53, 18LF27J53)
Public Const
    //ANCON0
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
    //ANCON1
    AN8 = 8,
    AN9 = 9,
    AN10 = 10,
    AN11 = 11,
    AN12 = 12
#elseif _device in (18F44J50, 18F45J50, 18F46J50, 18F46J53, 18F47J53) Or _
        _device in (18LF44J50, 18LF45J50, 18LF46J50, 18LF46J53, 18LF47J53)
Public Const
    //ANCON0
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
    AN5 = 5,
    AN6 = 6,
    AN7 = 7,
    //ANCON1
    AN8 = 8,
    AN9 = 9,
    AN10 = 10,
    AN11 = 11,
    AN12 = 12
#elseif _device in (18F65J94, 18F66J94, 18F67J94, 18F66J99) Or _
        _device in (18F85J94, 18F86J94, 18F87J94, 18F95J94, 18F96J94, 18F97J94) Or _
        _device in (18F86J99, 18F96J99)
Public Const
    //ANCON1 (ANSEL0-ANSEL7)
    AN0 = 0,    // RA0
    AN1 = 1,    // RA1
    AN2 = 2,    // RA2
    AN3 = 3,    // RA3
    AN6 = 4,    // RA4
    AN4 = 5,    // RA5
    AN7 = 6,    // RF2
    AN8 = 7,    // RG0
    //ANCON2 (ANSEL8-ANSEL15)
    AN9  = 8,   // RC2
    AN10 = 9,   // RF5
    AN11 = 10,  // RF6
    AN5  = 11,  // RF7
    AN19 = 12,  // RG1
    AN18 = 13,  // RG2
    AN17 = 14,  // RG3
    AN16 = 15   // RG4
  #if _device in (18F85J94, 18F86J94, 18F87J94, 18F95J94, 18F96J94, 18F97J94, 18F86J99, 18F96J99)
    ,               // continue the list
    //ANCON3 (ANSEL16-ANSEL23)
    AN23 = 16,  // RH0
    AN22 = 17,  // RH1
    AN21 = 18,  // RH2
    AN20 = 19,  // RH3
    AN12 = 20,  // RH4
    AN13 = 21,  // RH5
    AN14 = 22,  // RH6
    AN15 = 23   // RH7
  #endif
#elseif _device in (18F23K20, 18F24K20, 18F25K20, 18F26K20)
Public Const
    //ANSEL
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
    //ANSELH
    AN8 = 8,
    AN9 = 9,
    AN10 = 10,
    AN11 = 11,
    AN12 = 12
#elseif _device in (18F43K20, 18F44K20, 18F45K20, 18F46K20)
Public Const
    //ANSEL
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
    AN5 = 5,
    AN6 = 6,
    AN7 = 7,
    //ANSELH
    AN8 = 8,
    AN9 = 9,
    AN10 = 10,
    AN11 = 11,
    AN12 = 12
#elseif _device in (18F13K22, 18F14K22, 18LF13K22, 18LF14K22)
Public Const
    //ANSEL
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
    AN5 = 5,
    AN6 = 6,
    AN7 = 7,
    //ANSELH
    AN8 = 8,
    AN9 = 9,
    AN10 = 10,
    AN11 = 11
#elseif _device in (18F23K22, 18F24K22, 18F25K22, 18F26K22, 18LF23K22, 18LF24K22, 18LF25K22, 18LF26K22) Or _
        _device in (18F43K22, 18F44K22, 18F45K22, 18F46K22, 18LF43K22, 18LF44K22, 18LF45K22, 18LF46K22) // added V2.3
Public Const
    //ANSELA
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 5,
    //ANSELB
    AN12 = 8,
    AN10 = 9,
    AN8  = 10,
    AN9  = 11,
    AN11 = 12,
    AN13 = 13,
    //ANSELC
    AN14 = 18,
    AN15 = 19,
    AN16 = 20,
    AN17 = 21,
    AN18 = 22,
    AN19 = 23
  #if _device in (18F43K22, 18F44K22, 18F45K22, 18F46K22, 18LF43K22, 18LF44K22, 18LF45K22, 18LF46K22)     // added V2.3
    ,               // continue the list
    //ANSELD
    AN20 = 24,
    AN21 = 25,
    AN22 = 26,
    AN23 = 27,
    AN24 = 28,
    AN25 = 29,
    AN26 = 30,
    AN27 = 31,
    //ANSELE
    AN5  = 32,
    AN6  = 33,
    AN7  = 34
  #endif
#elseif _device in (18F65K22, 18F66K22, 18F67K22, 18F85K22, 18F86K22, 18F87K22)
Public Const
    //ANCON0
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
    AN5 = 5,
    AN6 = 6,
    AN7 = 7, 
    // ANCON1
    AN8 = 8,
    AN9 = 9,
    AN10 = 10,
    AN11 = 11,
  #if _device in (18F85K22, 18F86K22, 18F87K22)
    AN12 = 12,
    AN13 = 13,
    AN14 = 14,
    AN15 = 15,
  #endif
    // ANCON2
    AN16 = 16,
    AN17 = 17,
    AN18 = 18,
    AN19 = 19
  #if _device in (18F85K22, 18F86K22, 18F87K22)
    ,               // continue the list
    AN20 = 20,
    AN21 = 21,
    AN22 = 22,
    AN23 = 23
  #endif
#elseif _device in (18F24K40, 18F25K40, 18F26K40, 18F27K40, 18F45K40, 18F46K40, 18F47K40) Or _
        _device in (18LF24K40, 18LF25K40, 18LF26K40, 18LF27K40, 18LF45K40, 18LF46K40, 18LF47K40)
// K40 family combines the port name with 'AN'
Public Const
    //ANSELA
    ANA0 = 0,
    ANA1 = 1,
    ANA2 = 2,
    ANA3 = 3,
    ANA4 = 4,
    ANA5 = 5,
    ANA6 = 6,
    ANA7 = 7,
    //ANSELB
    ANB0 = 8,
    ANB1 = 9,
    ANB2 = 10,
    ANB3 = 11,
    ANB4 = 12,
    ANB5 = 13,
    ANB6 = 14,
    ANB7 = 15,
    //ANSELC
    ANC0 = 16,
    ANC1 = 17,
    ANC2 = 18,
    ANC3 = 19,
    ANC4 = 20,
    ANC5 = 21,
    ANC6 = 22,
    ANC7 = 23
  #if _device in (18F45K40, 18F46K40, 18F47K40, 18LF45K40, 18LF46K40, 18LF47K40) 
    ,               // continue the list
    //ANSELD
    AND0 = 24,
    AND1 = 25,
    AND2 = 26,
    AND3 = 27,
    AND4 = 28,
    AND5 = 29,
    AND6 = 30,
    AND7 = 31,
    //ANSELE
    ANE0 = 32,
    ANE1 = 33,
    ANE2 = 34
  #endif
#elseif _device in (18F65K40, 18F66K40, 18F67K40, 18LF65K40, 18LF66K40, 18LF67K40)
Public Const
    //ANSELA
    ANA0 = 0,
    ANA1 = 1,
    ANA2 = 2,
    ANA3 = 3,
    ANA4 = 4,
    ANA5 = 5,
    ANA6 = 6,
    ANA7 = 7,
    //ANSELB
    ANB0 = 8,
    ANB1 = 9,
    ANB2 = 10,
    ANB3 = 11,
    ANB4 = 12,
    ANB5 = 13,
    ANB6 = 14,
    ANB7 = 15,
    //ANSELD
    AND0 = 24,
    AND1 = 25,
    AND2 = 26,
    AND3 = 27,
    AND4 = 28,
    AND5 = 29,
    AND6 = 30,
    AND7 = 31,
    //ANSELE
    ANE0 = 32,
    ANE1 = 33,
    ANE2 = 34,
    ANE3 = 35,
    ANE4 = 36,
    ANE5 = 37,
    ANE6 = 38,
    ANE7 = 39,
    //ANSELF
    ANF0 = 40,
    ANF1 = 41,
    ANF2 = 42,
    ANF3 = 43,
    ANF4 = 44,
    ANF5 = 45,
    ANF6 = 46,
    ANF7 = 47,
    //ANSELG
    ANG0 = 48,
    ANG1 = 49,
    ANG2 = 50,
    ANG3 = 51,
    ANG4 = 52,
    ANG5 = 53,
    ANG6 = 54,
    ANG7 = 55 
#elseif _device in (18F13K50, 18F14K50, 18LF13K50, 18LF14K50)
Public Const
    //ANSEL
    AN3 = 3,
    AN4 = 4,
    AN5 = 5,
    AN6 = 6,
    AN7 = 7,
    //ANSELH
    AN8 = 8,
    AN9 = 9,
    AN10 = 10,
    AN11 = 11
#elseif _device in (18F24K50, 18F25K50, 18F45K50, 18LF24K50, 18LF25K50, 18LF45K50)  // added LF V2.3
Public Const
    //ANSELA
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 5,
    //ANSELB
    AN12 = 8,
    AN10 = 9,
    AN8  = 10,
    AN9  = 11,
    AN11 = 12,
    AN13 = 13,
    //ANSELC
    AN14 = 18,
    AN18 = 22,
    AN19 = 23
  #if _device in (18F45K50, 18LF45K50)      // added LF V2.3
    ,               // continue the list
    //ANSELD
    AN20 = 24,
    AN21 = 25,
    AN22 = 26,
    AN23 = 27,
    AN24 = 28,
    AN25 = 29,
    AN26 = 30,
    AN27 = 31,
    //ANSELE
    AN5  = 32,
    AN6  = 33,
    AN7  = 34
  #endif
#elseif _device in (18F25K80, 18F26K80, 18F45K80, 18F46K80, 18F65K80, 18F66K80) Or _
        _device in (18LF25K80, 18LF26K80, 18LF45K80, 18LF46K80, 18LF65K80, 18LF66K80)
Public Const
    //ANCON0
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
  #if _device in (18F45K80, 18F46K80, 18F65K80, 18F66K80, 18LF45K80, 18LF46K80, 18LF65K80, 18LF66K80)
    AN5 = 5,
    AN6 = 6,
    AN7 = 7,
  #endif
    // ANCON1
    AN8 = 8,
    AN9 = 9,
    AN10 = 10
  #if _device in (18F45K80, 18F46K80, 18F65K80, 18F66K80, 18LF45K80, 18LF46K80, 18LF65K80, 18LF66K80)
    ,               // continue the list
    AN11 = 11,
    AN12 = 12,
    AN13 = 13,
    AN14 = 14
  #endif
#elseif _device in (18F65K90, 18F66K90, 18F67K90, 18F85K90, 18F86K90, 18F87K90)
Public Const
    //ANCON0
    AN0 = 0,
    AN1 = 1,
    AN2 = 2,
    AN3 = 3,
    AN4 = 4,
    AN5 = 5,
    AN6 = 6,
    AN7 = 7,
    //ANCON1
    AN8 = 8,
    AN9 = 9,
    AN10 = 10,
    AN11 = 11,
  #if _device in (18F85K90, 18F86K90, 18F87K90)
    AN12 = 12,
    AN13 = 13,
    AN14 = 14,
    AN15 = 15,
  #endif
    //ANCON2
    AN16 = 16,
    AN17 = 17,
    AN18 = 18,
    AN19 = 19
  #if _device in (18F85K90, 18F86K90, 18F87K90)
    ,               // continue the list
    AN20 = 20,
    AN21 = 21,
    AN22 = 22,
    AN23 = 23
  #endif

#elseif _device in (18F24Q10, 18F25Q10, 18F26Q10, 18F27Q10, 18F45Q10, 18F46Q10, 18F47Q10)
// Q10 family combines the port name with 'AN'
Public Const
    //ANSELA
    ANA0 = 0,
    ANA1 = 1,
    ANA2 = 2,
    ANA3 = 3,
    ANA4 = 4,
    ANA5 = 5,
    ANA6 = 6,
    ANA7 = 7,
    //ANSELB
    ANB0 = 8,
    ANB1 = 9,
    ANB2 = 10,
    ANB3 = 11,
    ANB4 = 12,
    ANB5 = 13,
    ANB6 = 14,
    ANB7 = 15,
    //ANSELC
    ANC0 = 16,
    ANC1 = 17,
    ANC2 = 18,
    ANC3 = 19,
    ANC4 = 20,
    ANC5 = 21,
    ANC6 = 22,
    ANC7 = 23
  #if _device in (18F45Q10, 18F46Q10, 18F47Q10) 
    ,               // continue the list
    //ANSELD
    AND0 = 24,
    AND1 = 25,
    AND2 = 26,
    AND3 = 27,
    AND4 = 28,
    AND5 = 29,
    AND6 = 30,
    AND7 = 31,
    //ANSELE
    ANE0 = 32,
    ANE1 = 33,
    ANE2 = 34
  #endif

#else
  #warning "unknown analog select map"
#endif

//
// SetAnalogPort
//
Public macro SetAnalogPort(an, mode)
    If Not checkparam(an, cpConst) Or Not checkparam(mode, cpConst) Then
        checkparam(etError, "SetAnalogPort requires const parameters")
    EndIf    

  // 18F1x20 family, ADCON1
  // 0=analog, 1=digital
  #if _device in (18F1220, 18F1320, 18LF1220, 18LF1320)
    If (an <= 6) Then
        If (mode = ANA) Then
            ADCON1.bits(an) = 0
        Else
            ADCON1.bits(an) = 1
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F1x30 family, ADCON1
  // 0=analog, 1=digital
  #elseif _device in (18F1230, 18F1330, 18LF1230, 18LF1330)
    If (an <= 3) Then
        If (mode = ANA) Then
            ADCON1.bits(an) = 0
        Else
            ADCON1.bits(an) = 1
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F2x31 family, ANSEL0
  // 0=digital, 1=analog
  #elseif _device in (18F2331, 18F2431, 18LF2331, 18LF2431)
    If (an <= 4) Then
        If (mode = ANA) Then
            ANSEL0.bits(an) = 1
        Else
            ANSEL0.bits(an) = 0
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F4x31 family, ANSEL0/1
  // 0=digital, 1=analog
  #elseif _device in (18F4331, 18F4431, 18LF4331, 18LF4431)
    If (an <= 7) Then
        If (mode = ANA) Then
            ANSEL0 = ANSEL0 Or Byte(1 << an)     // can't use 'reg.bits()' past 7... get 'const expression violates bounds'
        Else
            ANSEL0 = ANSEL0 And Not Byte(1 << an)    // 'byte' cast reqd for SF 2225
        EndIf
    ElseIf (an = 8) Then
        If (mode = ANA) Then
            ANSEL1 = ANSEL1 Or Byte(1 << (an-8))
        Else
            ANSEL1 = ANSEL1 And Not Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F6xJ11/J16 family, ANCON0/1 (shared SFR addresses)
  // 0=analog, 1=digital
  #elseif _device in (18F66J11, 18F67J11, 18F66J16)
    WDTCON.bits(4) = 1        // ADSHR alternate register address bit
    If (an <= 7) And (an <> 5) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 And Not Byte(1 << an)
        Else
            ANCON0 = ANCON0 Or Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 11) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    
    WDTCON.bits(4) = 0
  
  // 18F8xJ11/J16 family, ANCON0/1 (shared SFR addresses)
  // 0=analog, 1=digital
  #elseif _device in (18F86J11, 18F87J11, 18F86J16)
    WDTCON.bits(4) = 1        // ADSHR alternate register address bit
    If (an <= 7) And (an <> 5) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 And Not Byte(1 << an)
        Else
            ANCON0 = ANCON0 Or Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 15) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    
    WDTCON.bits(4) = 0

  // 18F6xJ50/J55 family, ANCON0/1 (shared SFR addresses)
  // 0=analog, 1=digital
  #elseif _device in (18F65J50, 18F66J50, 18F67J50, 18F66J55)
    WDTCON.bits(4) = 1        // ADSHR alternate register address bit
    If (an <= 4) Or (an = 7) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 And Not Byte(1 << an)
        Else
            ANCON0 = ANCON0 Or Byte(1 << an)
        EndIf
    ElseIf (an >= 10) And (an <= 11) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    
    WDTCON.bits(4) = 0

  // 18F8xJ50/J55 family, ANCON0/1 (shared SFR addresses)
  // 0=analog, 1=digital
  #elseif _device in (18F85J50, 18F86J50, 18F87J50, 18F86J55)
    WDTCON.bits(4) = 1        // ADSHR alternate register address bit
    If (an <= 4) Or (an = 7) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 And Not Byte(1 << an)
        Else
            ANCON0 = ANCON0 Or Byte(1 << an)
        EndIf
    ElseIf (an >= 10) And (an <= 15) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    
    WDTCON.bits(4) = 0

  // 18F2xJ50/J53 family, ANCON0/1
  // 0=analog, 1=digital
  #elseif _device in (18F24J50, 18F25J50, 18F26J50, 18F26J53, 18F27J53) Or _
          _device in (18LF24J50, 18LF25J50, 18LF26J50, 18LF26J53, 18LF27J53)
    If (an <= 4) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 And Not Byte(1 << an)
        Else
            ANCON0 = ANCON0 Or Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 12) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    
  // 18F4xJ50/J53 family, ANCON0/1
  #elseif _device in (18F44J50, 18F45J50, 18F46J50, 18F46J53, 18F47J53) Or _
          _device in (18LF44J50, 18LF45J50, 18LF46J50, 18LF46J53, 18LF47J53)
    If (an <= 7) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 And Not Byte(1 << an)
        Else
            ANCON0 = ANCON0 Or Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 12) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18FxxJ94/J99 family... ANCON1/2/3
  // 0=digital, 1=analog
  #elseif _device in (18F65J94, 18F66J94, 18F67J94, 18F66J99) Or _
          _device in (18F85J94, 18F86J94, 18F87J94, 18F95J94, 18F96J94, 18F97J94) Or _
          _device in (18F86J99, 18F96J99)
    If (an <= 7) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 Or Byte(1 << an)
        Else
            ANCON1 = ANCON1 And Not Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 15) Then
        If (mode = ANA) Then
            ANCON2 = ANCON2 Or Byte(1 << (an-8))
        Else
            ANCON2 = ANCON2 And Not Byte(1 << (an-8))
        EndIf
    #if _device in (18F85J94, 18F86J94, 18F87J94, 18F95J94, 18F96J94, 18F97J94, 18F86J99, 18F96J99)
    ElseIf (an >= 16) And (an <= 23) Then
        If (mode = ANA) Then
            ANCON3 = ANCON3 Or Byte(1 << (an-16))
        Else
            ANCON3 = ANCON3 And Not Byte(1 << (an-16))
        EndIf
    #endif
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F2xK20 family, ANSEL/ANSELH
  // 0=digital, 1=analog
  #elseif _device in (18F23K20, 18F24K20, 18F25K20, 18F26K20)
    If (an <= 4) Then
        If (mode = ANA) Then
            ANSEL = ANSEL Or Byte(1 << an)
        Else
            ANSEL = ANSEL And Not Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 12) Then
        If (mode = ANA) Then
            ANSELH = ANSELH Or Byte(1 << (an-8))
        Else
            ANSELH = ANSELH And Not Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F4xK20 family, ANSEL/ANSELH
  // 0=digital, 1=analog
  #elseif _device in (18F43K20, 18F44K20, 18F45K20, 18F46K20)
    If (an <= 7) Then
        If (mode = ANA) Then
            ANSEL = ANSEL Or Byte(1 << an)
        Else
            ANSEL = ANSEL And Not Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 12) Then
        If (mode = ANA) Then
            ANSELH = ANSELH Or Byte(1 << (an-8))
        Else
            ANSELH = ANSELH And Not Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F1xK22, ANSEL/ANSELH
  // 0=digital, 1=analog
  #elseif _device in (18F13K22, 18F14K22, 18LF13K22, 18LF14K22)
    If (an <= 7) Then
        If (mode = ANA) Then
            ANSEL = ANSEL Or Byte(1 << an)
        Else
            ANSEL = ANSEL And Not Byte(1 << an)
        EndIf
    ElseIf (an <= 11) Then
        If (mode = ANA) Then
            ANSELH = ANSELH Or Byte(1 << (an-8))
        Else
            ANSELH = ANSELH And Not Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F2xK22, ANSELA/B/C
  // 0=digital, 1=analog
  #elseif _device in (18F23K22, 18F24K22, 18F25K22, 18F26K22, 18LF23K22, 18LF24K22, 18LF25K22, 18LF26K22)
    If (an <= 3) Or (an = 5) Then
        If (mode = ANA) Then
            ANSELA = ANSELA Or Byte(1 << (an Mod 32))    // macro req's mod operator for const param > 32
        Else
            ANSELA = ANSELA And Not Byte(1 << (an Mod 32))
        EndIf
    ElseIf (an >= 8) And (an <= 13) Then
        If (mode = ANA) Then
            ANSELB = ANSELB Or Byte(1 << (an-8))
        Else
            ANSELB = ANSELB And Not Byte(1 << (an-8))
        EndIf
    ElseIf (an >= 18) And (an <= 23) Then
        If (mode = ANA) Then
            ANSELC = ANSELC Or Byte(1 << (an-16))
        Else
            ANSELC = ANSELC And Not Byte(1 << (an-16))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F4xK22, ANSELA/B/C/D/E
  // 0=digital, 1=analog
  #elseif _device in (18F43K22, 18F44K22, 18F45K22, 18F46K22, 18LF43K22, 18LF44K22, 18LF45K22, 18LF46K22)
    If (an <= 3) Or (an = 5) Then
        If (mode = ANA) Then
            ANSELA = ANSELA Or Byte(1 << (an Mod 32))    // macro req's mod operator for const param > 32
        Else
            ANSELA = ANSELA And Not Byte(1 << (an Mod 32))
        EndIf
    ElseIf (an >= 8) And (an <= 13) Then
        If (mode = ANA) Then
            ANSELB = ANSELB Or Byte(1 << (an-8))
        Else
            ANSELB = ANSELB And Not Byte(1 << (an-8))
        EndIf
    ElseIf (an >= 18) And (an <= 23) Then
        If (mode = ANA) Then
            ANSELC = ANSELC Or Byte(1 << (an-16))
        Else
            ANSELC = ANSELC And Not Byte(1 << (an-16))
        EndIf
    ElseIf (an >= 24) And (an <= 31) Then
        If (mode = ANA) Then
            ANSELD = ANSELD Or Byte(1 << (an-24))
        Else
            ANSELD = ANSELD And Not Byte(1 << (an-24))
        EndIf
    ElseIf (an >= 32) And (an <= 34) Then
        If (mode = ANA) Then
            ANSELE = ANSELE Or Byte(1 << (an-32))
        Else
            ANSELE = ANSELE And Not Byte(1 << (an-32))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F6xK22 family, ANCON0/1/2
  // 0=digital, 1=analog
  #elseif _device in (18F65K22, 18F66K22, 18F67K22)
    If (an <= 7) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 Or Byte(1 << an)
        Else
            ANCON0 = ANCON0 And Not Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 11) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        EndIf
    ElseIf (an >= 16) And (an <= 19) Then
        If (mode = ANA) Then
            ANCON2 = ANCON2 Or Byte(1 << (an-16))
        Else
            ANCON2 = ANCON2 And Not Byte(1 << (an-16))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F8xK22 family, ANCON0/1/2
  // 0=digital, 1=analog
  #elseif _device in (18F85K22, 18F86K22, 18F87K22)
    If (an <= 7) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 Or Byte(1 << an)
        Else
            ANCON0 = ANCON0 And Not Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 15) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        EndIf
    ElseIf (an >= 16) And (an <= 23) Then
        If (mode = ANA) Then
            ANCON2 = ANCON2 Or Byte(1 << (an-16))
        Else
            ANCON2 = ANCON2 And Not Byte(1 << (an-16))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F2xK40, ANSELA/B/C
  // 0=digital, 1=analog
  #elseif _device in (18F24K40, 18F25K40, 18F26K40, 18F27K40)
    If (an >= 0) And (an <= 7) Then
        If (mode = ANA) Then
            ANSELA = ANSELA Or Byte(1 << (an Mod 32))    // macro req's mod operator for const param > 32
        Else
            ANSELA = ANSELA And Not Byte(1 << (an Mod 32))
        EndIf
    ElseIf (an >= 8) And (an <= 15) Then
        If (mode = ANA) Then
            ANSELB = ANSELB Or Byte(1 << (an-8))
        Else
            ANSELB = ANSELB And Not Byte(1 << (an-8))
        EndIf
    ElseIf (an >= 16) And (an <= 23) Then
        If (mode = ANA) Then
            ANSELC = ANSELC Or Byte(1 << (an-16))
        Else
            ANSELC = ANSELC And Not Byte(1 << (an-16))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F4xK40, ANSELA/B/C/D/E
  // 0=digital, 1=analog
  #elseif _device in (18F45K40, 18F46K40, 18F47K40)
    If (an >= 0) And (an <= 7) Then
        If (mode = ANA) Then
            ANSELA = ANSELA Or Byte(1 << (an Mod 32))    // macro req's mod operator for const param > 32
        Else
            ANSELA = ANSELA And Not Byte(1 << (an Mod 32))
        EndIf
    ElseIf (an >= 8) And (an <= 15) Then
        If (mode = ANA) Then
            ANSELB = ANSELB Or Byte(1 << (an-8))
        Else
            ANSELB = ANSELB And Not Byte(1 << (an-8))
        EndIf
    ElseIf (an >= 16) And (an <= 23) Then
        If (mode = ANA) Then
            ANSELC = ANSELC Or Byte(1 << (an-16))
        Else
            ANSELC = ANSELC And Not Byte(1 << (an-16))
        EndIf
    ElseIf (an >= 24) And (an <= 31) Then
        If (mode = ANA) Then
            ANSELD = ANSELD Or Byte(1 << (an-24))
        Else
            ANSELD = ANSELD And Not Byte(1 << (an-24))
        EndIf
    ElseIf (an >= 32) And (an <= 34) Then
        If (mode = ANA) Then
            ANSELE = ANSELE Or Byte(1 << (an-32))
        Else
            ANSELE = ANSELE And Not Byte(1 << (an-32))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F6xK40, ANSELA/B/D/E/F/G
  // 0=digital, 1=analog
  #elseif _device in (18F65K40, 18F66K40, 18F67K40)
    If (an >= 0) And (an <= 7) Then
        If (mode = ANA) Then
            ANSELA = ANSELA Or Byte(1 << (an Mod 32))    // macro req's mod operator for const param > 32
        Else
            ANSELA = ANSELA And Not Byte(1 << (an Mod 32))
        EndIf
    ElseIf (an >= 8) And (an <= 15) Then
        If (mode = ANA) Then
            ANSELB = ANSELB Or Byte(1 << ((an-8) Mod 32))
        Else
            ANSELB = ANSELB And Not Byte(1 << ((an-8) Mod 32))
        EndIf
    ElseIf (an >= 24) And (an <= 31) Then
        If (mode = ANA) Then
            ANSELD = ANSELD Or Byte(1 << ((an-24) Mod 32))
        Else
            ANSELD = ANSELD And Not Byte(1 << ((an-24) Mod 32))
        EndIf
    ElseIf (an >= 32) And (an <= 39) Then
        If (mode = ANA) Then
            ANSELE = ANSELE Or Byte(1 << ((an-32) Mod 32))
        Else
            ANSELE = ANSELE And Not Byte(1 << ((an-32) Mod 32))
        EndIf
    ElseIf (an >= 40) And (an <= 47) Then
        If (mode = ANA) Then
            ANSELF = ANSELF Or Byte(1 << ((an-40) Mod 32))
        Else
            ANSELF = ANSELF And Not Byte(1 << ((an-40) Mod 32))
        EndIf
    ElseIf (an >= 48) And (an <= 55) Then
        If (mode = ANA) Then
            ANSELG = ANSELG Or Byte(1 << ((an-48) Mod 32))
        Else
            ANSELG = ANSELG And Not Byte(1 << ((an-48) Mod 32))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F1xK50 family, ANSEL/ANSELH
  // 0=digital, 1=analog
  #elseif _device in (18F13K50, 18F14K50, 18LF13K50, 18LF14K50)
    // registers $0F53 - $0F5F are not part of the access bank
    If (an >= 3) And (an <= 7) Then
        If (mode = ANA) Then
            ANSEL = ANSEL Or Byte(1 << an)
        Else
            ANSEL = ANSEL And Not Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 11) Then
        If (mode = ANA) Then
            ANSELH = ANSELH Or Byte(1 << (an-8))
        Else
            ANSELH = ANSELH And Not Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F2xK50 family, ANSELA/B/C, 18F4xK50 family, ANSELA/B/C/D/E
  // 0=digital, 1=analog
  #elseif _device in (18F24K50, 18F25K50, 18F45K50, 18LF24K50, 18LF25K50, 18LF45K50)     // added LF V2.3
    If (an <= 3) Or (an = 5) Then
        If (mode = ANA) Then
            ANSELA = ANSELA Or Byte(1 << (an Mod 32))    // macro req's mod operator for const param > 32
        Else
            ANSELA = ANSELA And Not Byte(1 << (an Mod 32))
        EndIf
    ElseIf (an >= 8) And (an <= 13) Then
        If (mode = ANA) Then
            ANSELB = ANSELB Or Byte(1 << (an-8))
        Else
            ANSELB = ANSELB And Not Byte(1 << (an-8))
        EndIf
    ElseIf (an = 18) Or (an = 22) Or (an = 23) Then
        If (mode = ANA) Then
            ANSELC = ANSELC Or Byte(1 << (an-16))
        Else
            ANSELC = ANSELC And Not Byte(1 << (an-16))
        EndIf
    #if _device in (18F45K50, 18LF45K50)     // added LF V2.3
    ElseIf (an >= 24) And (an <= 31) Then
        If (mode = ANA) Then
            ANSELD = ANSELD Or Byte(1 << (an-24))
        Else
            ANSELD = ANSELD And Not Byte(1 << (an-24))
        EndIf
    ElseIf (an >= 32) And (an <= 34) Then
        If (mode = ANA) Then
            ANSELE = ANSELE Or Byte(1 << (an-32))
        Else
            ANSELE = ANSELE And Not Byte(1 << (an-32))
        EndIf
    #endif
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18FxxK80 family, ANCON0/1
  // 0=digital, 1=analog
  #elseif _device in (18F25K80, 18F26K80, 18LF25K80, 18LF26K80)
    If (an <= 4) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 Or Byte(1 << an)
        Else
            ANCON0 = ANCON0 And Not Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 10) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    
  #elseif _device in (18F45K80, 18F46K80, 18F65K80, 18F66K80, 18LF45K80, 18LF46K80, 18LF65K80, 18LF66K80)
    If (an <= 7) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 Or Byte(1 << an)
        Else
            ANCON0 = ANCON0 And Not Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 14) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18FxxK90 family, ANCON0/1
  // 0=digital, 1=analog
  #elseif _device in (18F65K90, 18F66K90, 18F67K90)
    If (an <= 7) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 Or Byte(1 << an)
        Else
            ANCON0 = ANCON0 And Not Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 11) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        EndIf
    ElseIf (an >= 16) And (an <= 19) Then
        If (mode = ANA) Then
            ANCON2 = ANCON2 Or Byte(1 << (an-16))
        Else
            ANCON2 = ANCON2 And Not Byte(1 << (an-16))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    
  #elseif _device in (18F85K90, 18F86K90, 18F87K90)
    If (an <= 7) Then
        If (mode = ANA) Then
            ANCON0 = ANCON0 Or Byte(1 << an)
        Else
            ANCON0 = ANCON0 And Not Byte(1 << an)
        EndIf
    ElseIf (an >= 8) And (an <= 15) Then
        If (mode = ANA) Then
            ANCON1 = ANCON1 Or Byte(1 << (an-8))
        Else
            ANCON1 = ANCON1 And Not Byte(1 << (an-8))
        EndIf
    ElseIf (an >= 16) And (an <= 23) Then
        If (mode = ANA) Then
            ANCON2 = ANCON2 Or Byte(1 << (an-16))
        Else
            ANCON2 = ANCON2 And Not Byte(1 << (an-16))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F2xQ10, ANSELA/B/C
  // 0=digital, 1=analog
  #elseif _device in (18F24Q10, 18F25Q10, 18F26Q10, 18F27Q10)
    If (an >= 0) And (an <= 7) Then
        If (mode = ANA) Then
            ANSELA = ANSELA Or Byte(1 << (an Mod 32))    // macro req's mod operator for const param > 32
        Else
            ANSELA = ANSELA And Not Byte(1 << (an Mod 32))
        EndIf
    ElseIf (an >= 8) And (an <= 15) Then
        If (mode = ANA) Then
            ANSELB = ANSELB Or Byte(1 << (an-8))
        Else
            ANSELB = ANSELB And Not Byte(1 << (an-8))
        EndIf
    ElseIf (an >= 16) And (an <= 23) Then
        If (mode = ANA) Then
            ANSELC = ANSELC Or Byte(1 << (an-16))
        Else
            ANSELC = ANSELC And Not Byte(1 << (an-16))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // 18F4xQ10, ANSELA/B/C/D/E
  // 0=digital, 1=analog
  #elseif _device in (18F45Q10, 18F46Q10, 18F47Q10)
    If (an >= 0) And (an <= 7) Then
        If (mode = ANA) Then
            ANSELA = ANSELA Or Byte(1 << (an Mod 32))    // macro req's mod operator for const param > 32
        Else
            ANSELA = ANSELA And Not Byte(1 << (an Mod 32))
        EndIf
    ElseIf (an >= 8) And (an <= 15) Then
        If (mode = ANA) Then
            ANSELB = ANSELB Or Byte(1 << (an-8))
        Else
            ANSELB = ANSELB And Not Byte(1 << (an-8))
        EndIf
    ElseIf (an >= 16) And (an <= 23) Then
        If (mode = ANA) Then
            ANSELC = ANSELC Or Byte(1 << (an-16))
        Else
            ANSELC = ANSELC And Not Byte(1 << (an-16))
        EndIf
    ElseIf (an >= 24) And (an <= 31) Then
        If (mode = ANA) Then
            ANSELD = ANSELD Or Byte(1 << (an-24))
        Else
            ANSELD = ANSELD And Not Byte(1 << (an-24))
        EndIf
    ElseIf (an >= 32) And (an <= 34) Then
        If (mode = ANA) Then
            ANSELE = ANSELE Or Byte(1 << (an-32))
        Else
            ANSELE = ANSELE And Not Byte(1 << (an-32))
        EndIf
    Else
        checkparam(etError, "unsupported ANxx setting")
    EndIf    

  // any others... mode is probably encoded in ADCON1 so we can't use this macro
  #else
    checkparam(etError, "SetAnalogPort error... analog selection is encoded or device is not supported")
  #endif
End macro
#endif      // DigitalIO_AnalogPort

End Module
