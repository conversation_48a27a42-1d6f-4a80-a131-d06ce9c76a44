{
****************************************************************
* Name    : SPECTRUM GSM 2015 18F4620.BAS                      *
* Author  : <PERSON>                                       *
* Notice  : Copyright (c) 2010 Scanne<PERSON>                 *
*         : All Rights Reserved                                *
* Date    : 22/01/2015                                         *
* Version : 2.0                                                *
* Notes   :                                                    *
*         :                                                    *
****************************************************************
}
Program GSM2

    Device = 18f46k22
    Clock = 64 'remember to enable HSPLL in configuration
    Config
       FOSC = INTIO67, //turn 4x PLL ON
       LVP = OFF,
       MCLRE = INTMCLR,
       DEBUG = OFF,
       PWRTEN = ON,
       BOREN = ON,
       WDTEN = off,
       PBADEN = OFF, 'for 18F4620 - makes port B digital
       XINST=OFF,
       CP0 = On, 		' Code Protection Block 0 Disabled
       CP1 = On, 		' Code Protection Block 1 Disabled
       CP2 = On, 		' Code Protection Block 2 Disabled
       CP3 = On, 		' Code Protection Block 3 Disabled
       CPB = On, 		' Boot Block Code Protection Disabled
       CPD = On, 		' Data EEPROM Code Protection Disabled
       WRT0	= OFF, 		' Write Protection Block 0 Disabled
       WRT1	= OFF, 		' Write Protection Block 1Disabled
       WRT2	= OFF, 		' Write Protection Block 2 Disabled
       WRT3	= OFF, 		' Write Protection Block 3 Disabled
       WRTB	= OFF, 		' Boot Block Write Protection Disabled
       WRTC	= OFF, 		' Configuration Register Write Protection Disabled
       WRTD	= OFF, 		' Data EEPROM Write Protection Disabled
       EBTR0 = OFF,		' Table Read Protection Block 0 Disabled
       EBTR1 = OFF,		' Table Read Protection Block 1 Disabled
       EBTR2 = OFF,		' Table Read Protection Block 2 Disabled
       EBTR3 = OFF,		' Table Read Protection Block 3 Disabled
       EBTRB = OFF		' Boot Block Table Read Protection Disabled
       
    #option ISR_SHADOW=true
        
    //Declaro que la compilacion va a ser para un modulo quectel o simcom
    #define Quectel
    //#define Simcom

    Include "system.bas"
    'Include "setdigitalio.bas"
    Include "ROM.bas"   
    Include "GSM.bas"
    Include "COMUNICACIONPC.bas"
    Include "IO.bas"
    Include "TIMERS.bas"
    'Include "DTMF.bas"
    Include "DISPLAY.bas"
    Include "BUZZER.bas"
    'Include "TECLADO.bas"
    'Include "PWM.bas"
    'Include "6P20.bas"
    Include "H6P20.bas"
    Include "WALA.bas"
    Include "CENTRAL.bas"
    Include "PROGRAMACION.bas"
    Include "RUTINAS.bas"
    Include "DISPAROS.bas"
    'Include "MEMORIA.bas"
    'Include "SALTEC.bas"
    'Include "TESTS.bas"

    //Inicializacion del modulo
    ANSELA=0
    ANSELB=0
    ANSELC=0
    ANSELD=0
    ANSELE=0
    
    //CM1CON0=0
    //CM2CON0=0
    OSCCON = %01110000     // Sets up the internal oscillator
        
    DelayMS(125)
    OSCTUNE.6 = 1          // Enables PLL


    TIMERS.mS=0
    
    TIMERS.HabilitarInterrupciones()

        
    {
        //Verifico si hay que borrar el codigo de programacion
        TECLADO.LeerTeclado()
        If TECLADO.TeclaAst=1 Then
            BUZZER.BeepLargo()
            BUZZER.BeepLargo()
            BUZZER.BeepLargo()
            ROM.CodigoProgramacionAFabrica()
        EndIf
        
        //Verifico si hay que resetear la central
        If TECLADO.Tecla9=1 Then
            BUZZER.BeepLargo()
            BUZZER.BeepLargo()
            BUZZER.BeepLargo()
            BUZZER.BeepLargo()
            BUZZER.BeepLargo()
            BUZZER.BeepLargo()
            ROM.CentralAFabrica()
            TECLADO.EsperarSoltarTeclas()
        EndIf
    }
    
    //Me aseguro que todos los valores en ram esten actualizados
    LeerSeteos()
    
    If IO.btnSel=IO.BotonPulsado Then
        If IO.btnProg=IO.BotonPulsado Then
            WALA.TransmitirCodigos()
        Else
            flagEntradaWalaHabilitada=false
            ROM.GuardarSeteos()
        EndIf
    ElseIf IO.btnProg=IO.BotonPulsado Then
            flagEntradaWalaHabilitada=true
            ROM.GuardarSeteos()
    EndIf

    DISPLAY.SecuenciaInicio

    CCP1CON=%00000100         //Seteo el modulo CCP para captura por flanco de bajada

    CENTRAL.ControlarEstadoInicio()
    
    //Bloque principal de programa
    Repeat
        //Acciones comunes a todos los eventos
        CENTRAL.ControlarInalambricos()
        CENTRAL.ControlarZonasCableadas()
        CENTRAL.ControlarAlimentacion()
        CENTRAL.ProcesarSalidas()
        DISPAROS.ProcesarDisparos()
        Repeat
            GSM.ProcesarGSM()                                               
        Until Not (CENTRAL.CentralBloqueadaInstalador Or CENTRAL.CentralBloqueadaFabrica)
        COMUNICACIONPC.AtenderPC()
        WALA.ProcesarWala()
    
        //Acciones en funcion al estado actual de la central
        Select CENTRAL.Estado
            //Central activada
            Case EstadoCentralActivada
                //Si recien entro en este estado
                If UltimoEstadoCentralEEPROM<>Estado Then
                    UltimoEstadoCentralEEPROM=EstadoCentralActivada
                    GuardarSeteos()
                EndIf
                
                //Controlo si hay que incorporar la cancelacion en funcion a la wala
                If WALA.EstadoActivacionWala=WALA.Activada Then
                    CENTRAL.CancelacionActivada=false
                EndIf
                If WALA.EstadoActivacionWala=WALA.ActivadaParcial Then
                    CENTRAL.CancelacionActivada=true
                EndIf
                
                
                //Controlo si se esta pulsando la tecla seleccion, si es asi desactivo la central
                If IO.tmrBtnSel>50 Or WALA.EstadoActivacionWala=WALA.Desactivada Then
                    CENTRAL.DesactivarCentral(0)
                    'CENTRAL.CancelarDisparo()
                    //Pongo en cero el contador para forzar la espera de 500ms para la siguiente act/des
                    IO.tmrBtnSel=0
                EndIf

'                If TIMERS.flag500ms=1 Then
'                    If CENTRAL.FlagEnviarEstadoAlTeclado Then
'                        'SALTEC.CentralActivada()
'                        CENTRAL.FlagEnviarEstadoAlTeclado=false
'                    EndIf
'                Else
'                    CENTRAL.FlagEnviarEstadoAlTeclado=true
'                EndIf
            //Central desactivada
            Case EstadoCentralDesactivada
                //Si recien entro en este estado
                If UltimoEstadoCentralEEPROM<>Estado Then
                    UltimoEstadoCentralEEPROM=EstadoCentralDesactivada
                    GuardarSeteos()
                EndIf
                
                //Controlo si se esta pulsando la tecla seleccion, si es asi activo la central
                
                If IO.tmrBtnSel>50 Or WALA.EstadoActivacionWala=WALA.Activada Or WALA.EstadoActivacionWala=WALA.ActivadaParcial Then
                    DISPLAY.ApagarDisplay()
                    CENTRAL.ActivarCentral(0)
                    'CENTRAL.CancelarDisparo()
                    //Pongo en cero el contador para forzar la espera de 500ms para la siguiente act/des
                    IO.tmrBtnSel=0
                EndIf
                
                //Controlo si se esta pulsando la tecla programacion por mas de 5 segundos, si es asi entro en programacion
                
                If IO.tmrBtnProg>500 Then
                    PROGRAMACION.EntrarEnProgramacion()
                    //Pongo en cero el contador
                    IO.tmrBtnProg=0
                    //Me aseguro no se lea como una pulsacion corta
                    IO.btnProgSoltado=false
                EndIf

                //Controlo si se solto la tecla de programacion y si se pulso por mas de 500 ms muestro rssi
                If IO.btnProgSoltado Then
                    If IO.TiempoBtnProgPulsado>50 Then
                        //Valido con un beep para que el usuario lo sepa
                        BeepCorto()
                        //Muestro por 1 minuto el rssi en display
                        TimerMostrarRSSI=60
                        //Pongo en cero el contador
                        IO.tmrBtnProg=0
                    EndIf
                    //Me aseguro no se lea como una pulsacion corta
                    IO.btnProgSoltado=false
                EndIf
                
'                If TIMERS.flag500ms=1 Then
'                    If CENTRAL.FlagEnviarEstadoAlTeclado Then
'                        'SALTEC.CentralDesactivada()
'                        CENTRAL.FlagEnviarEstadoAlTeclado=false
'                    EndIf
'                Else
'                    CENTRAL.FlagEnviarEstadoAlTeclado=true
'                EndIf

                //Si no se esta corriendo ningun disparo en estado desactivado (24 horas, emergencia, robo)
                If TimerSirenas=0 Then
                    //Llamada al panico
                    If HT6P20.RecibiendoAmbosCanales Then
                        Select TimerRecibiendoAmbosCanales
                            Case = 1
                                FlagHabilitarODeshabilitarPrealarma=true
                            Case > 3
                                CENTRAL.EntrarEnPanico()
                                FlagHabilitarODeshabilitarPrealarma=false
                        End Select
                    ElseIf Not Recibiendo6P20 Then
                        If FlagHabilitarODeshabilitarPrealarma Then
                            HabilitarODeshabilitarPrealarma()
                            FlagHabilitarODeshabilitarPrealarma=false
                        EndIf
                    EndIf

                    //Si hay que mostrar rssi entonces....
                    If CENTRAL.TimerMostrarRSSI>0 Then
                        RUTINAS.MostrarRSSI(GSM.RSSI)
                    Else
                        //si no hay registro de zonas disparadas en el ciclo anterior de activacion
                        If CENTRAL.ContadorRegistroDisparos>0 Then
                            RUTINAS.MostrarRegistroDisparo()
                        Else
                            //Sino... si hay zonas que mostrar las muestro
                            RUTINAS.MostrarZonasActuadas()
                        EndIf
                    EndIf
                        
                EndIf
            //Central en timpo de demora
            Case EstadoCentralDemora
                If TimerEntrada=0 Then
                    DisparoCableado=ZonaConDemoraEsCableada
                    UltimaZonaDisparo=ZonaConDemora
                    CENTRAL.Estado=CENTRAL.EstadoCentralActivada
                    CENTRAL.GenerarDisparoRobo()
                Else
                    If (CENTRAL.TimerEntrada Mod 2)=0 Then
                        If CENTRAL.TimerEntrada>5 Then
                            IO.SalidaBuzzer=TIMERS.flag500ms
                        Else
                            IO.SalidaBuzzer=TIMERS.flag250ms
                        EndIf
                    Else
                        IO.SalidaBuzzer=IO.BuzzerApagado
                    EndIf

                    //Controlo si se esta pulsando la tecla seleccion, si es asi desactivo la central
                    If IO.tmrBtnSel>50 Or WALA.EstadoActivacionWala=WALA.Desactivada Then
                        CENTRAL.DesactivarCentral(0)
                        'CENTRAL.CancelarDisparo()
                        //Pongo en cero el contador para forzar la espera de 500ms para la siguiente act/des
                        IO.tmrBtnSel=0
                    EndIf
                EndIf

            //Central en tiempo de salida
            Case EstadoCentralActivando
                //Si hay zonas que mostrar las muestro
                If RUTINAS.MostrarZonasActuadas() Then
                    IO.SalidaBuzzer=Not TIMERS.flag500ms
                    If TIMERS.flag500ms=1 Then
                        If CENTRAL.FlagMostrarAutoexcluidas Then
                            'SALTEC.ZonasAutoExcluidas(ZonasCableadasActuadas)
                            'SALTEC.CentralActivando()
                            CENTRAL.FlagMostrarAutoexcluidas=false
                        EndIf
                    Else
                        CENTRAL.FlagMostrarAutoexcluidas=true
                    EndIf
                Else
                    If TIMERS.flag500ms=1 Then
                        If CENTRAL.FlagMostrarAutoexcluidas Then
                            'SALTEC.ZonasAutoExcluidas(0)
                            'SALTEC.CentralActivando()
                            CENTRAL.FlagMostrarAutoexcluidas=false
                        EndIf
                    Else
                        CENTRAL.FlagMostrarAutoexcluidas=true
                    EndIf
                    IO.SalidaBuzzer=IO.BuzzerApagado
                EndIf

                //Si el contador de pulsos de led de wala, llego a 3 tengo que cancelar zonas
                If WALA.EstadoActivacionWala=WALA.ActivadaParcial Then
                    CENTRAL.CancelacionActivada=true
                EndIf
                If WALA.EstadoActivacionWala=WALA.Activada Then
                    CENTRAL.CancelacionActivada=false
                EndIf
                
                //Si el timer de salida llego a cero es momento de dejar la central activada
                If CENTRAL.TimerSalida=0 Then
                    CENTRAL.DejarActivada()
                EndIf

                If IO.tmrBtnSel>50 Or WALA.EstadoActivacionWala=Desactivada Then
                    CENTRAL.DesactivarCentral(0)
                    'CENTRAL.CancelarDisparo()
                    //Pongo en cero el contador para forzar la espera de 500ms para la siguiente act/des
                    IO.tmrBtnSel=0
                EndIf

            //Central en panico
            Case EstadoCentralPanico
                //Retardo para que no se vacie rapido el buffer de inalambricos (contbuenos)
                //Delay(2)
                If Not HT6P20.RecibiendoAmbosCanales Then
                    If TimerPanico=0 Then
                        CENTRAL.SalirDePanico()
                    EndIf
                EndIf
        End Select
        
        //Si el boton no esta pulsado, me encargo de vaciar el timer de conteo antes de empezar a chequear otra vez
        If Not IO.btnProgPulsado Then
            IO.tmrBtnProg=0
        EndIf
        
        //Si el boton no esta pulsado, me encargo de vaciar el timer de conteo antes de empezar a chequear otra vez
        If Not IO.btnSelPulsado Then
            IO.tmrBtnSel=0  
        EndIf

    Until false
    
        ASM
    ORG dirCuerpoMensajeRoboZ1C
        DB 82, 111, 98, 111, 32, 90, 49, 67, 0
    ORG dirCuerpoMensajeRoboZ2C
        DB 82, 111, 98, 111, 32, 90, 50, 67, 0 
    ORG dirCuerpoMensajeRoboZ3C
        DB 82, 111, 98, 111, 32, 90, 51, 67, 0 
    ORG dirCuerpoMensajeRoboZ4C
        DB 82, 111, 98, 111, 32, 90, 52, 67, 0 
    ORG dirCuerpoMensajeRoboZ5C
        DB 82, 111, 98, 111, 32, 90, 53, 67, 0 
    ORG dirCuerpoMensajeRoboZ6C
        DB 82, 111, 98, 111, 32, 90, 54, 67, 0 
    ORG dirCuerpoMensajeRoboZ7C
        DB 82, 111, 98, 111, 32, 90, 55, 67, 0 
    ORG dirCuerpoMensajeRoboZ8C
        DB 82, 111, 98, 111, 32, 90, 56, 67, 0
    ORG dirCuerpoMensajeRoboZ1I
        DB 82, 111, 98, 111, 32, 90, 49, 73, 0
    ORG dirCuerpoMensajeRoboZ2I
        DB 82, 111, 98, 111, 32, 90, 50, 73, 0
    ORG dirCuerpoMensajeRoboZ3I
        DB 82, 111, 98, 111, 32, 90, 51, 73, 0
    ORG dirCuerpoMensajeRoboZ4I
        DB 82, 111, 98, 111, 32, 90, 52, 73, 0
    ORG dirCuerpoMensajeRoboZ5I
        DB 82, 111, 98, 111, 32, 90, 53, 73, 0
    ORG dirCuerpoMensajeRoboZ6I
        DB 82, 111, 98, 111, 32, 90, 54, 73, 0
    ORG dirCuerpoMensajeRoboZ7I
        DB 82, 111, 98, 111, 32, 90, 55, 73, 0
    ORG dirCuerpoMensajeRoboZ8I
        DB 82, 111, 98, 111, 32, 90, 56, 73, 0
    ORG dirCuerpoMensajeAntidesarme
        DB 84, 97, 109, 112, 101, 114, 0
    ORG dirCuerpoMensajeAsaltoC
        DB 65, 115, 97, 108, 116, 111, 32, 67, 0
    ORG dirCuerpoMensajeAsaltoI
        DB 65, 115, 97, 108, 116, 111, 32, 73, 0
    ORG dirCuerpoMensajeEmergenciaC
        DB 69, 109, 101, 114, 103, 101, 110, 99, 105, 97, 32, 67, 0
    ORG dirCuerpoMensajeEmergenciaI
        DB 69, 109, 101, 114, 103, 101, 110, 99, 105, 97, 32, 73, 0
    ORG dirCuerpoMensajeIncendioC
        DB 73, 110, 99, 101, 110, 100, 105, 111, 32, 67, 0
    ORG dirCuerpoMensajeIncendioI
        DB 73, 110, 99, 101, 110, 100, 105, 111, 32, 73, 0
    ORG dirCuerpoMensajeActivado
        DB 65, 99, 116, 105, 118, 97, 100, 97, 0
    ORG dirCuerpoMensajeDesactivado
        DB 68, 101, 115, 97, 99, 116, 105, 118, 97, 100, 97, 0
    ORG dirCuerpoMensajeTextoUsuario
        DB 0
    ORG dirCuerpoMensajeGrupoRemoto1
        DB 82, 101, 109, 111, 116, 111, 32, 49, 0 
    ORG dirCuerpoMensajeGrupoRemoto2
        DB 82, 101, 109, 111, 116, 111, 32, 50, 0 
    ORG dirCuerpoMensajeGrupoRemoto3
        DB 82, 101, 109, 111, 116, 111, 32, 51, 0 
    ORG dirCuerpoMensajeGrupoRemoto4
        DB 82, 101, 109, 111, 116, 111, 32, 52, 0 
    ORG dirCuerpoMensajeGrupoRemoto5
        DB 82, 101, 109, 111, 116, 111, 32, 53, 0 
    ORG dirCuerpoMensajeGrupoRemoto6
        DB 82, 101, 109, 111, 116, 111, 32, 54, 0 
    ORG dirCuerpoMensajeGrupoRemoto7
        DB 82, 101, 109, 111, 116, 111, 32, 55, 0 
    ORG dirCuerpoMensajeGrupoRemoto8
        DB 82, 101, 109, 111, 116, 111, 32, 56, 0 
    ORG dirAPN
        DB '"', 103, 112, 114, 115, 46, 99, 108, 97, 114, 111, 46, 99, 111, 109, 46, 97, 114, '"', ',', '"', 104, 115, 100, 112, 97, '"', ',', '"', 104, 115, 100, 112, 97, '"', 0
    ORG dirServidor
        DB '"', 119, 119, 119, 46, 115, 101, 114, 118, 105, 100, 111, 114, 46, 99, 111, 109, 46, 97, 114, '"', ',', '5', '5', '5', '5', 0
    End ASM
