<?xml version='1.0' encoding='UTF-8' standalone='yes'?>
<WORKSPACE>
 <FRAME activewindow="0">
  <PLACEMENT>2c0000000200000003000000ffffffffffffffffffffffffffffffff42000000000000004006000098030000</PLACEMENT>
  <WINDOW type="default" module="ISIS">
   <editor metric="0" gridmajor="25400000" mode="00000000" zoom="0" scale="59" flipped="0" gridminor="508000" gridmode="32" snapidx="2" snap="254000" xcursor="0" orgmode="0" snaprange="215254">
    <world y1="-8890000" x1="-12700000" y2="8890000" x2="12700000"/>
    <centre x="3556000" y="-2286000"/>
    <origin x="0" y="0"/>
   </editor>
  </WINDOW>
 </FRAME>
 <MODULE name="ARES">
  <editor metric="0" gridmajor="0" mode="00000000" zoom="10" scale="10" flipped="0" gridminor="0" gridmode="32" snapidx="3" snap="0" xcursor="0" orgmode="0" snaprange="0">
   <world y1="-12700000" x1="-15240000" y2="12700000" x2="15240000"/>
   <centre x="0" y="0"/>
   <origin x="0" y="0"/>
  </editor>
 </MODULE>
 <MODULE name="ISIS">
  <editor metric="0" gridmajor="25400000" mode="00000000" zoom="0" scale="59" flipped="0" gridminor="508000" gridmode="32" snapidx="2" snap="254000" xcursor="0" orgmode="0" snaprange="215254">
   <world y1="-8890000" x1="-12700000" y2="8890000" x2="12700000"/>
   <centre x="3556000" y="-2286000"/>
   <origin x="0" y="0"/>
  </editor>
 </MODULE>
 <MODULE name="VSMDEBUG">
  <PWI>
   <POPUP w="800" x="38" flags="00000002" y="129" h="200" pid="0" iid="-1">
    <PROPERTIES>
     <ITEM name="Message Column Width">542</ITEM>
     <ITEM name="ShowGrid">No</ITEM>
     <ITEM name="Source Column Width">100</ITEM>
     <ITEM name="Time Column Width">120</ITEM>
     <ITEM name="Version">100</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="350" x="58" flags="00000033" y="149" h="200" pid="1" iid="-1">
    <PROPERTIES>
     <ITEM name="Address Column Width">83</ITEM>
     <ITEM name="AutoResize">No</ITEM>
     <ITEM name="Gridlines">Yes</ITEM>
     <ITEM name="Name Column Width">83</ITEM>
     <ITEM name="ShowAddresses">Yes</ITEM>
     <ITEM name="ShowPreviousValues">No</ITEM>
     <ITEM name="ShowTypes">No</ITEM>
     <ITEM name="ShowWatchPoint">Yes</ITEM>
     <ITEM name="TriggerMode">0</ITEM>
     <ITEM name="Value Column Width">83</ITEM>
     <ITEM name="Version">100</ITEM>
     <ITEM name="Watch Expression Column Width">83</ITEM>
     <ITEM name="nItems">0</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="695" x="837" flags="00008001" y="262" h="453" pid="0" iid="67">
    <PROPERTIES/>
   </POPUP>
   <POPUP w="581" x="98" flags="00000020" y="189" h="339" pid="0" iid="1">
    <PROPERTIES/>
   </POPUP>
   <POPUP w="581" x="118" flags="00000020" y="209" h="339" pid="0" iid="55">
    <PROPERTIES/>
   </POPUP>
   <POPUP w="450" x="138" flags="00000022" y="229" h="250" pid="0" iid="49">
    <PROPERTIES>
     <ITEM name="AutoEcho">0</ITEM>
     <ITEM name="HexMode">0</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="450" x="158" flags="00000022" y="249" h="250" pid="0" iid="51">
    <PROPERTIES>
     <ITEM name="AutoEcho">0</ITEM>
     <ITEM name="HexMode">0</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="450" x="178" flags="00000022" y="269" h="250" pid="0" iid="50">
    <PROPERTIES>
     <ITEM name="AutoEcho">0</ITEM>
     <ITEM name="HexMode">0</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="450" x="198" flags="00000022" y="289" h="250" pid="0" iid="48">
    <PROPERTIES>
     <ITEM name="AutoEcho">0</ITEM>
     <ITEM name="HexMode">0</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="690" x="268" flags="0000000b" y="373" h="400" pid="1" iid="69">
    <PROPERTIES/>
   </POPUP>
   <POPUP w="300" x="994" flags="0000000b" y="311" h="300" pid="2" iid="69">
    <PROPERTIES>
     <ITEM name="Address Column Width">94</ITEM>
     <ITEM name="AutoResize">No</ITEM>
     <ITEM name="Gridlines">Yes</ITEM>
     <ITEM name="Name Column Width">94</ITEM>
     <ITEM name="ShowAddresses">Yes</ITEM>
     <ITEM name="ShowGlobals">Yes</ITEM>
     <ITEM name="ShowPreviousValues">No</ITEM>
     <ITEM name="ShowTypes">No</ITEM>
     <ITEM name="ShowWatchPoint">No</ITEM>
     <ITEM name="TriggerMode">0</ITEM>
     <ITEM name="Value Column Width">94</ITEM>
     <ITEM name="Version">100</ITEM>
     <ITEM name="nItems">0</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="52" x="258" flags="00000008" y="349" h="12" pid="3" iid="69">
    <PROPERTIES/>
   </POPUP>
   <POPUP w="16" x="278" flags="0000000a" y="369" h="32" pid="7" iid="69">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00000000</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="8" x="298" flags="0000000a" y="389" h="32" pid="4" iid="69">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00000000</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="8" x="318" flags="80000002" y="409" h="16" pid="12" iid="69">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00000000</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="8" x="338" flags="0000000a" y="429" h="32" pid="6" iid="69">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00000000</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="8" x="358" flags="0000000a" y="449" h="1" pid="8" iid="69">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00200000</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="8" x="378" flags="0000000a" y="469" h="2" pid="9" iid="69">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00300000</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="18" x="398" flags="00000008" y="489" h="32" pid="10" iid="69">
    <PROPERTIES/>
   </POPUP>
   <POPUP w="8" x="418" flags="80000002" y="509" h="8" pid="13" iid="69">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00000000</ITEM>
    </PROPERTIES>
   </POPUP>
  </PWI>
  <BREAKPOINT LINE="129" FILE="../Spectrum GSM 2015 18f46k22 quectel.bas" ADDRESS="4294967295" PROJECT="69"/>
  <BREAKPOINT LINE="171" FILE="../wala.bas" ADDRESS="4294967295" PROJECT="69"/>
 </MODULE>
</WORKSPACE>
