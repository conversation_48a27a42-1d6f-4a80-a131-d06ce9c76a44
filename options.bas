{
*****************************************************************************
*  Name    : OPTIONS.BAS                                                   *
*  Author  : [select VIEW...EDITOR OPTIONS]                                 *
*  Notice  : Copyright (c) 2010 [select VIEW...EDITOR OPTIONS]              *
*          : All Rights Reserved                                            *
*  Date    : 28/05/2010                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*   The following directive block looks to see if a user has specified a    *
*   USART_BAUDRATE #option. If they have, it first checks to see if a valid *
*   value has been given. If not, an error is reported. If the #option is   *
*   valid, the SPBRG value is computed with BRGH as high. If the SPBRG value*
*   is greater than 255, then the SPBRG value is re-calculated with BRGH as *
*   low.                                                                    *
*****************************************************************************
}

Module Options

Include "usart.bas"
#option USART_BAUDRATE = 19200 // default is 19200
#if IsOption(USART_BAUDRATE)
   #if Not (USART_BAUDRATE in (300, 600, 1200, 2400, 4800, 9600, 19200, 38400, 57600, 115200))
      #error USART_BAUDRATE, "Invalid baudrate!"
   #else
      #define _FOSC = _clock * 1000000
      #define _FMULT = 16 
      #define _BRGH = $24
      #define _SPBRG = _FOSC / (_FMULT * (USART_BAUDRATE + 1)) - 1 + 0.5

      // high speed option generates a SPBRG value that is too big, try
      // a low speed option instead...
      #if _SPBRG > 255
         #undefine _FMULT
         #undefine _SPBRG
         #undefine _BRGH 
         #define _FMULT = 64
         #define _BRGH = $20
         #define _SPBRG = _FOSC / (_FMULT * (USART_BAUDRATE + 1)) - 1 + 0.5
      #endif  

      // cannot calculate from given clock and baudrate...
      #if _SPBRG > 255
         #warning USART_BAUDRATE, "Unable to calculate valid SPBRG value from given clock and baudrate"
      #endif  

      // bring defines into module code...
      Const SPBRGConst As Byte = _SPBRG          
      Const BRGHConst As Byte = _BRGH
   #endif   
#endif

// if a user has give a valid USART_BAUDRATE option, generate
// configuration code...
#ifdef _SPBRG
SPBRGRegister = SPBRGConst // Baudrate
TXStatus = BRGHConst       // high or low speed
RCStatus = $90             // serial port enable, continuous receive
RCInput = true             // receive pin is input
TXInput = false            // transmit pin is output
#endif 
