{
*****************************************************************************
*  Name    : COMUNICACIONPC.BAS                                            *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2010 Scanner Alarmas                             *
*          : All Rights Reserved                                            *
*  Date    : 16/04/2010                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

Module COMUNICACIONPC

    '#option DEPURANDO = true
    

    //Modulos
'    Include "MEMORYACCESSORIGINAL.bas"
    Include "ROM.bas"
    Include "eeprom.bas"
    Include "string.bas"
    Include "convert.bas"
    Include "IO.bas"
'    Include "options.bas"
'    Include "usart.bas"
'    Include "TIMERS.bas"
    Include "system.bas"
    Include "GSM.bas"
    Include "PROCESADOR.bas"
    'Include "DISCADOR.bas"
    Include "suart.bas"
'    Include "system.bas"
    Include "CENTRAL.bas"

    //Constantes publicas
    Public Const
        Telefono1 = 0,
        Telefono2 = 1,
        Telefono3 = 2,
        Telefono4 = 3,
        Telefono5 = 4,
        Telefono6 = 5,
        Telefono7 = 6,
        Telefono8 = 7
    

'    // RX report...
'    Public Structure TRXReport
'       CodigoParametro As Byte
'       Valor As Byte
'       Dato As String(61)
'    End Structure    
    
    //Declaracion de varibles
    Public Dim
        TXReport As Report,
        mS As Word,
        TimerComunicacionPC As Word

    Dim
        flagEnviarPing As Boolean,
        flagEnviarDump2 As Boolean,
        flagEnviarDump3 As Boolean,
        flagEnviarDump4 As Boolean
        
    Private Sub SetearUsart()
        UART.SetTX(TxPC)
        UART.SetBaudrate(sbr4800)
        UART.SetMode(umInverted)
    End Sub

    Private Sub WriteReport()
        INTCON.7=0
        UART.Write(13, TXReport.CodigoParametro)
        UART.Write(TXReport.Valor)
        UART.Write(TXReport.Dato, 13)
        INTCON.7=1
        ClrWDT()
    End Sub

    Private Sub EnviarRSSI()
        //Envio los mensajes desatendidos
        TXReport.CodigoParametro=Codigo_RSSI
        TXReport.Valor=0
        TXReport.Dato=DecToStr(GSM.RSSI)
        WriteReport()
    End Sub

    //Se encarga de mandar los textos de los mensajes que genera la central
    //Tambien se encarga de enviar los textos de comando que la central reconoce
    Private Sub EnviarDump()
        Dim
            codigo As Byte
            
        
        For codigo = Codigo_Robo_Z1C To Codigo_Grupo_Remotos_8
            TXReport.Valor=codigo

            //Envio los cuerpos de mensaje
            TXReport.CodigoParametro=Codigo_CuerpoMensaje
            TXReport.Dato=LeerMensaje(codigo)
            WriteReport()
            
            //Envio las habilitaciones de mensajes de texto
            TXReport.CodigoParametro=Codigo_Texto
            TXReport.Dato=DecToStr(ROM.LeerAlarmas(codigo))
            WriteReport()
        Next

        For codigo = Codigo_Transistor1_Activar To Codigo_Escucha
            TXReport.Valor=codigo

            //Envio los cuerpos de mensaje
            TXReport.CodigoParametro=Codigo_CuerpoMensaje
            TXReport.Dato=LeerMensaje(codigo)
            WriteReport()
            
            //Envio las habilitaciones de mensajes de texto
            TXReport.CodigoParametro=Codigo_Texto
            TXReport.Dato=DecToStr(ROM.LeerAlarmas(codigo))
            WriteReport()
        Next

        //Envio las habilitaciones de mensajes de texto de bateria baja
        TXReport.Valor=Codigo_Bateria_Baja
        TXReport.Dato=DecToStr(ROM.LeerAlarmas(Codigo_Bateria_Baja))
        WriteReport()

        flagEnviarDump2=true
    End Sub

    //Se encarga de enviar los numeros de telefono
    Private Sub EnviarDump2()
        Dim
            codigo As Byte

        //Envio los numeros de telefono
        TXReport.CodigoParametro=Codigo_Telefono
        
        For codigo = 0 To 7
            TXReport.Valor=codigo
            TXReport.Dato=LeerTelefono(codigo)
            WriteReport()
        Next

        flagEnviarDump2=false
        flagEnviarDump3=true
    End Sub
    
    Private Sub EnviarDump3()

        Dim contador As Byte
    
        //Envio las configuraciones de la central
        TXReport.CodigoParametro=Codigo_Seteos
        
        //Envio la configuracion de zonas normal abiertas cableadas
        TXReport.Valor=Codigo_Zonas_Cableadas_Normal_Abiertas
        TXReport.Dato=DecToStr(CENTRAL.ZonasNormalAbiertas)
        WriteReport()
        //Envio la configuracion de zonas cancelables cableadas
        TXReport.Valor=Codigo_Zonas_Cableadas_Cancelables
        TXReport.Dato=DecToStr(CENTRAL.ZonasCableadasCancelables)
        WriteReport()
        //Envio la configuracion de zonas demoradas cableadas
        TXReport.Valor=Codigo_Zonas_Cableadas_Demoradas
        TXReport.Dato=DecToStr(CENTRAL.ZonasCableadasDemoradas)
        WriteReport()
        //Envio la configuracion de zonas 24hs cableadas
        TXReport.Valor=Codigo_Zonas_Cableadas_24Hs
        TXReport.Dato=DecToStr(CENTRAL.ZonasCableadas24Horas)
        WriteReport()

        //Envio la configuracion de zonas cancelables inalambricas
        TXReport.Valor=Codigo_Zonas_Inalambricas_Cancelables
        TXReport.Dato=DecToStr(CENTRAL.ZonasInalambricasCancelables)
        WriteReport()
        //Envio la configuracion de zonas demoradas inalambricas
        TXReport.Valor=Codigo_Zonas_Inalambricas_Demoradas
        TXReport.Dato=DecToStr(CENTRAL.ZonasInalambricasDemoradas)
        WriteReport()
        //Envio la configuracion de zonas 24hs inalambricas
        TXReport.Valor=Codigo_Zonas_Inalambricas_24Hs
        TXReport.Dato=DecToStr(CENTRAL.ZonasInalambricas24Horas)
        WriteReport()

        //Envio el tiempo de sirenas
        TXReport.Valor=Codigo_Tiempo_Sirenas
        TXReport.Dato=DecToStr(CENTRAL.TiempoSirenas/60)
        WriteReport()

        //Envio el tiempo de entrada
        TXReport.Valor=Codigo_Tiempo_Entrada
        TXReport.Dato=DecToStr(CENTRAL.TiempoEntrada)
        WriteReport()

        //Envio el tiempo de salida
        TXReport.Valor=Codigo_Tiempo_Salida
        TXReport.Dato=DecToStr(CENTRAL.TiempoSalida)
        WriteReport()

        //Envio el tiempo de temporizado para el reporte de desconexion de alimentacion
        TXReport.Valor=Codigo_Tiempo_Temporizado_Desconexion
        TXReport.Dato=DecToStr(CENTRAL.TiempoTemporizadoDesconexion/3600)
        WriteReport()
        
        //Envio la configuracion general de la central
        TXReport.Valor=Codigo_Flags_Central
        TXReport.Dato=DecToStr(EE.ReadByte(dirFlagsCentral1))
        WriteReport()
        
        //Envio el valor de los maximos errores de ronda de envio de sms
        TXReport.Valor=Codigo_Maximo_Errores_Ronda
        TXReport.Dato=DecToStr(CENTRAL.MaximoErroresRonda)
        WriteReport()

        //Envio el registro de disparos
        TXReport.Valor=Codigo_Registro_Disparos
        TXReport.Dato=""
        For contador=0 To 7
            TXReport.Dato=TXReport.Dato+DecToStr(EE.ReadByte(dirDisparosGuardados+contador))+","
        Next
        WriteReport()
        
        //Envio los bits de configuracion de la salidas
        TXReport.Valor=Codigo_Flags_Salidas
        TXReport.Dato=DecToStr(EE.ReadByte(dirFlagsSalidas))
        WriteReport()
        
        //Envio el tiempo de la salida1
        TXReport.Valor=Codigo_Tiempo_Salida1
        TXReport.Dato=DecToStr(CENTRAL.TiempoPulsoSalida1)
        WriteReport()
        
        //Envio el tiempo de la salida2
        TXReport.Valor=Codigo_Tiempo_Salida2
        TXReport.Dato=DecToStr(CENTRAL.TiempoPulsoSalida2)
        WriteReport()
        
        //Envio las unidades del tiempo de la salida1
        TXReport.Valor=Codigo_Unidades_Salida1
        TXReport.Dato=DecToStr(CENTRAL.UnidadesTiempoPulsoSalida1)
        WriteReport()
        
        //Envio las unidades del tiempo de la salida2
        TXReport.Valor=Codigo_Unidades_Salida2
        TXReport.Dato=DecToStr(CENTRAL.UnidadesTiempoPulsoSalida2)
        WriteReport()
        
        //Envio el tipoo de inicio de la central
        TXReport.Valor=Codigo_Tipo_Inicio
        TXReport.Dato=DecToStr(CENTRAL.TipoInicioCentral)
        WriteReport()
        
        flagEnviarDump3=false
        flagEnviarDump4=true
    End Sub
    
    Private Sub EnviarDump4()
        Dim bloques As Byte,
            caracteres As Byte,
            caracter As Byte,
            lista As String(33),
            direccion As Word
    
        //Envio las configuraciones del GPRS
        TXReport.CodigoParametro=Codigo_GPRS
        
        //Envio la configuracion del APN
        TXReport.Valor=Codigo_APN
        TXReport.Dato=CargarDesdeRom(dirAPN)
        WriteReport()
            
        //Envio la configuracion del servidor
        TXReport.Valor=Codigo_Servidor
        TXReport.Dato=CargarDesdeRom(dirServidor)
        WriteReport()
            
        //Envio el tiempo del reporte periodico
        TXReport.Valor=Codigo_Reporte_Periodico
        TXReport.Dato=DecToStr(CENTRAL.TiempoTest/60)
        WriteReport()
            
        //Envio el numero de cuenta
        TXReport.Valor=Codigo_Cuenta
        TXReport.Dato=DecToStr(Account(0)) + DecToStr(Account(1)) + DecToStr(Account(2)) + DecToStr(Account(3))
        WriteReport()
            
        //Envio los flags de seteos de GPRS
        TXReport.Valor=Codigo_Flags_GPRS
        TXReport.Dato=DecToStr(EE.ReadByte(dirFlagsGPRS))
        WriteReport()
            
        //Envio los mensajes desatendidos
        EnviarRSSI()
        
        TXReport.CodigoParametro=Codigo_IMEI
        TXReport.Valor=0
        TXReport.Dato=LeerIMEI()
        WriteReport()
        
        TXReport.CodigoParametro=Codigo_Version
        TXReport.Valor=0
        TXReport.Dato=Version
        WriteReport()

        TXReport.CodigoParametro=Codigo_Memoria_Inalambricos
        For bloques = 0 To 49
            lista=""
            For caracteres = 0 To 15
                direccion=bloques<<4
                direccion=direccion+caracteres
                caracter=EE.ReadByte(direccion)
                lista=lista+HexToStr(caracter, 2)
            Next
            TXReport.Valor=bloques
            TXReport.Dato=lista
            WriteReport()
        Next
       flagEnviarDump4=false
    End Sub
    
    Public Sub AtenderPC()
        Dim ByteLeido As Byte 

        If TimerComunicacionPC=0 Then
            ConectadoPC=false
            'PIE1.2=1
            'PIE2.1=1
        EndIf

        SetearUsart()

        //Si hay actividad en el pin del puerto de la pc
        If RxPC=1 Then
            'Delay(100)
            //Deshabilito el timer1 para no generar interrupciones
            INTCON.6=0
            PIE1.1=0
            //Leo un byte
            ByteLeido=UART.ReadByte(255)
            If Not UART.FTimeOut Then
                'Toggle(SalidaTest)
                Select ByteLeido
                    Case CR
                        TimerComunicacionPC=5000
                        ConectadoPC=true
                        'PIE1.2=0
                        'PIE2.1=0
                        'INTCON.7=0
                        UART.WriteByte(CR)
                        RXReport.CodigoParametro=UART.ReadByte(255) 
                        If Not UART.FTimeOut Then
                            RXReport.Valor=UART.ReadByte(255) 
                            If Not UART.FTimeOut Then
                                UART.Read(RXReport.Dato)
                                If RXReport.CodigoParametro=Codigo_Dump Then
                                    EnviarDump()
                                Else
                                    ProcesarReporte()
                                EndIf
                            EndIf
                        EndIf
                        'INTCON.7=1
                    Case HT
                        TimerComunicacionPC=5000
                        ConectadoPC=true
                        'PIE1.2=0
                        'PIE2.1=0
                        UART.WriteByte(HT)
                        //Si hay una nueva medicion de rssi
                        If flagNuevoRSSI Then
                            //Lo envio
                            EnviarRSSI()
                            //Y lo marco como enviado
                            flagNuevoRSSI=false
                        EndIf
                End Select
            EndIf
        //Sino la pc no esta diciendo nada...
'        Else
'            //Si la pc inicio una comunicacion
'            If TimerComunicacionPC>0 Then
'                //Cada un segundo
'                If (mS Mod 500)=0 Then
'                    If flagEnviarPing Then
'                        //Envio un ping
'                        UART.WriteByte(LF)
'                        flagEnviarPing=false
'                    EndIf
'                Else
'                    //Preparo el siguiente envio
'                    flagEnviarPing=true
'                EndIf
'                //Si hay una nueva medicion de rssi
'                If flagNuevoRSSI Then
'                    //Lo envio
'                    EnviarRSSI()
'                EndIf
'            EndIf
            //Habilito el timer1
            INTCON.6=1
            PIE1.1=1
        EndIf
        
        If flagEnviarDump2 Then
            EnviarDump2()
        ElseIf flagEnviarDump3 Then
            EnviarDump3()
        ElseIf flagEnviarDump4 Then
            EnviarDump4()
        EndIf
    End Sub
    
    //Inicializacion
    flagEnviarPing=false
    flagEnviarDump2=false
    flagEnviarDump3=false
    flagEnviarDump4=false
    UART.SetRX(RxPC)
    SetearUsart()
    ConectadoPC=false
    TimerComunicacionPC=0
