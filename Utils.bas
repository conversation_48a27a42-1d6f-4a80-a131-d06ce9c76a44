{
*****************************************************************************
*  Name    : Utils Library                                                  *
*  Author  : <PERSON>                                              *
*  Notice  : Copyright (c) 2003 Mecanique                                   *
*          : All Rights Reserved                                            *
*  Date    : 25/06/05                                                       *
*  Version : 1.1                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}
Module Utils          
{
****************************************************************************
* Name    : Reverse (OVERLOAD)                                             *
* Purpose : Reverse the bits of pValue by pAmount                          *
****************************************************************************
}
Public Function Reverse(pValue As Byte, pAmount As Byte) As Byte
   Result = 0
   While pAmount > 0
      Result = Result << 1
      Result.0 = pValue.0
      pValue = pValue >> 1
      Dec(pAmount)
   Wend
End Function
{
****************************************************************************
* Name    : Reverse (OVERLOAD)                                             *
* Purpose : Reverse the bits of pValue by pAmount                          *
****************************************************************************
}
Public Function Reverse(pValue As Word, pAmount As Byte) As Word
   Result = 0
   While pAmount > 0
      Result = Result << 1
      Result.0 = pValue.0
      pValue = pValue >> 1
      Dec(pAmount)
   Wend
End Function
{
****************************************************************************
* Name    : Reverse (OVERLOAD)                                             *
* Purpose : Reverse the bits of pValue by pAmount                          *
****************************************************************************
}
Public Function Reverse(pValue As LongWord, pAmount As Byte) As LongWord
   Result = 0
   While pAmount > 0
      Result = Result << 1
      Result.0 = pValue.0
      pValue = pValue >> 1
      Dec(pAmount)
   Wend
End Function
{
****************************************************************************
* Name    : Digit (OVERLOAD)                                               *
* Purpose : Return the value of a decimal digit. For example, Digit(123,3) *
*         : will return the number 1                                       *
****************************************************************************
}
Public Function Digit(pValue As Byte, pIndex As Byte) As Byte
   Result = 0
   While pIndex > 0
      Result = pValue Mod 10
      pValue = pValue / 10
      Dec(pIndex)
   Wend
End Function
{
****************************************************************************
* Name    : Digit (OVERLOAD)                                               *
* Purpose : Return the value of a decimal digit. For example, Digit(123,3) *
*         : will return the number 1                                       *
****************************************************************************
}
Public Function Digit(pValue As Word, pIndex As Byte) As Byte
   Result = 0
   While pIndex > 0
      Result = pValue Mod 10
      pValue = pValue / 10
      Dec(pIndex)
   Wend
End Function
{
****************************************************************************
* Name    : Digit (OVERLOAD)                                               *
* Purpose : Return the value of a decimal digit. For example, Digit(123,3) *
*         : will return the number 1                                       *
****************************************************************************
}
Public Function Digit(pValue As LongWord, pIndex As Byte) As Byte
   Result = 0
   While pIndex > 0
      Result = pValue Mod 10
      pValue = pValue / 10
      Dec(pIndex)
   Wend
End Function
{
****************************************************************************
* Name    : Min (OVERLOAD)                                                 *
* Purpose : Return the minimum of two values                               *
****************************************************************************
}
Public Function Min(pValueA, pValueB As Byte) As Byte
   If pValueA < pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Min (OVERLOAD)                                                 *
* Purpose : Return the minimum of two values                               *
****************************************************************************
}
Public Function Min(pValueA, pValueB As ShortInt) As ShortInt
   If pValueA < pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Min (OVERLOAD)                                                 *
* Purpose : Return the minimum of two values                               *
****************************************************************************
}
Public Function Min(pValueA, pValueB As Word) As Word
   If pValueA < pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Min (OVERLOAD)                                                 *
* Purpose : Return the minimum of two values                               *
****************************************************************************
}
Public Function Min(pValueA, pValueB As Integer) As Integer
   If pValueA < pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Min (OVERLOAD)                                                 *
* Purpose : Return the minimum of two values                               *
****************************************************************************
}
Public Function Min(pValueA, pValueB As LongWord) As LongWord
   If pValueA < pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Min (OVERLOAD)                                                 *
* Purpose : Return the minimum of two values                               *
****************************************************************************
}
Public Function Min(pValueA, pValueB As LongInt) As LongInt
   If pValueA < pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Min (OVERLOAD)                                                 *
* Purpose : Return the minimum of two values                               *
****************************************************************************
}
Public Function Min(pValueA, pValueB As Float) As Float
   If pValueA < pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Max (OVERLOAD)                                                 *
* Purpose : Return the maximum of two values                               *
****************************************************************************
}
Public Function Max(pValueA, pValueB As Byte) As Byte
   If pValueA > pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Max (OVERLOAD)                                                 *
* Purpose : Return the maximum of two values                               *
****************************************************************************
}
Public Function Max(pValueA, pValueB As ShortInt) As ShortInt
   If pValueA > pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Max (OVERLOAD)                                                 *
* Purpose : Return the maximum of two values                               *
****************************************************************************
}
Public Function Max(pValueA, pValueB As Word) As Word
   If pValueA > pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Max (OVERLOAD)                                                 *
* Purpose : Return the maximum of two values                               *
****************************************************************************
}
Public Function Max(pValueA, pValueB As Integer) As Integer
   If pValueA > pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Max (OVERLOAD)                                                 *
* Purpose : Return the maximum of two values                               *
****************************************************************************
}
Public Function Max(pValueA, pValueB As LongWord) As LongWord
   If pValueA > pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Max (OVERLOAD)                                                 *
* Purpose : Return the maximum of two values                               *
****************************************************************************
}
Public Function Max(pValueA, pValueB As LongInt) As LongInt
   If pValueA > pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Max (OVERLOAD)                                                 *
* Purpose : Return the maximum of two values                               *
****************************************************************************
}
Public Function Max(pValueA, pValueB As Float) As Float
   If pValueA > pValueB Then
      Result = pValueA
   Else
      Result = pValueB
   EndIf
End Function
{
****************************************************************************
* Name    : Swap (OVERLOAD)                                                *
* Purpose : Swap two values                                                *
****************************************************************************
}
Public Sub Swap(ByRef pValueA, pValueB As Char)
   Dim Tmp As Char
   Tmp = pValueA
   pValueA = pValueB
   pValueB = Tmp
End Sub
{
****************************************************************************
* Name    : Swap (OVERLOAD)                                                *
* Purpose : Swap two values                                                *
*         : NOTE : Will swap string with max 32 chars only                 *
****************************************************************************
}
Public Sub Swap(ByRef pValueA, pValueB As String)
   Dim Tmp As String(33)
   Tmp = pValueA
   pValueA = pValueB
   pValueB = Tmp
End Sub
{
****************************************************************************
* Name    : Swap (OVERLOAD)                                                *
* Purpose : Swap two values                                                *
****************************************************************************
}
Public Sub Swap(ByRef pValueA, pValueB As Byte)
   Dim Tmp As Byte
   Tmp = pValueA
   pValueA = pValueB
   pValueB = Tmp
End Sub
{
****************************************************************************
* Name    : Swap (OVERLOAD)                                                *
* Purpose : Swap two values                                                *
****************************************************************************
}
Public Sub Swap(ByRef pValueA, pValueB As ShortInt)
   Dim Tmp As ShortInt
   Tmp = pValueA
   pValueA = pValueB
   pValueB = Tmp
End Sub
{
****************************************************************************
* Name    : Swap (OVERLOAD)                                                *
* Purpose : Swap two values                                                *
****************************************************************************
}
Public Sub Swap(ByRef pValueA, pValueB As Word)
   Dim Tmp As Word
   Tmp = pValueA
   pValueA = pValueB
   pValueB = Tmp
End Sub
{
****************************************************************************
* Name    : Swap (OVERLOAD)                                                *
* Purpose : Swap two values                                                *
****************************************************************************
}
Public Sub Swap(ByRef pValueA, pValueB As Integer)
   Dim Tmp As Integer
   Tmp = pValueA
   pValueA = pValueB
   pValueB = Tmp
End Sub
{
****************************************************************************
* Name    : Swap (OVERLOAD)                                                *
* Purpose : Swap two values                                                *
****************************************************************************
}
Public Sub Swap(ByRef pValueA, pValueB As LongWord)
   Dim Tmp As LongWord
   Tmp = pValueA
   pValueA = pValueB
   pValueB = Tmp
End Sub
{
****************************************************************************
* Name    : Swap (OVERLOAD)                                                *
* Purpose : Swap two values                                                *
****************************************************************************
}
Public Sub Swap(ByRef pValueA, pValueB As LongInt)
   Dim Tmp As LongInt
   Tmp = pValueA
   pValueA = pValueB
   pValueB = Tmp
End Sub
{
****************************************************************************
* Name    : Swap (OVERLOAD)                                                *
* Purpose : Swap two values                                                *
****************************************************************************
}
Public Sub Swap(ByRef pValueA, pValueB As Float)
   Dim Tmp As Float
   Tmp = pValueA
   pValueA = pValueB
   pValueB = Tmp
End Sub
{
****************************************************************************
* Name    : HighNibble                                                     *
* Purpose : Returns the most significant nibble                            *
****************************************************************************
}
Public Inline Function HighNibble(pValue As WREG) As Byte
   Result = pValue >> 4
End Function
{
****************************************************************************
* Name    : HighNibble                                                     *
* Purpose : Returns the least significant nibble                           *
****************************************************************************
}
Public Inline Function LowNibble(pValue As WREG) As Byte
   Result = pValue And $0F
End Function
{
****************************************************************************
* Name    : SetAllDigital                                                  *
* Purpose : Switches device pins from analog to digital                    *
****************************************************************************
} 
Public Sub SetAllDigital()
  
  // 4 channels 
  #if _device in (18F1230, 18F1330)
  ADCON1 = $00
  CMCON = $07
  
  // 5 channels, ANSEL...
  #elseif _device in (18F2331, 18F2431)
  ANSEL0 = $00

  // 9 channels, ANSEL...
  #elseif _device in (18F4331, 18F4431)
  ANSEL0 = $00
  ANSEL1.0 = 0
  
  // 7 channels, bit field...
  #elseif _device in (18F1220, 18F1320)
  ADCON1 = $7F
  
  // 8 - 13 channels - 0 comp
  #elseif _comparator = 0
  ADCON1 = $0F
  
  // J11 and J16 family...
  #elseif _device in (18F66J11, 18F66J16, 18F67J11, 18F86J11, 18F86J16, 18F87J11)
  ANCON0 = $FF
  ANCON1 = $FF

  // J50 and J55 family...
  #elseif _device in (18F65J50, 18F66J50, 18F66J55, 18F67J50, 18F85J50, 18F86J50, 18F86J55, 18F87J50)
  ANCON0 = $FF
  ANCON1 = $FF
  
  // 18FxxK20 family (no LF versions)
  #elseif _device in (18F23K20, 18F24K20, 18F25K20, 18F26K20, 18F43K20, 18F44K20, 18F45K20, 18F46K20)
    ANSEL = $00
    ANSELH = $00
    CM1CON0 = 0
    CM2CON0 = 0
    SLRCON = 0               // added V1.8

  // 18FxxK22 family (no LF versions)
  #elseif _device in (18f46k22)
    ANSELA = $00
    ANSELB = $00
    ANSELC = $00
    ANSELD = $00
    ANSELE = $00
    CM1CON0 = 0
    CM2CON0 = 0
    
  #else
  ADCON1 = $0F
  CMCON = $07
  #endif
End Sub




