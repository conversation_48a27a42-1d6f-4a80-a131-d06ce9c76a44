{
*****************************************************************************
*  Name    : TESTS.BAS                                                      *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2011 <PERSON>                                *
*          : All Rights Reserved                                            *
*  Date    : 15/08/2011                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

Module TESTS

    'Include "system.bas"
    Include "GSM.bas"
    'Include "COMUNICACIONPC.bas"
    Include "IO.bas"
    'Include "TIMERS.bas"
    Include "ROM.bas"   
    Include "eeprom.bas"
    'Include "DTMF.bas"
    Include "DISPLAY.bas"
    Include "BUZZER.bas"
    'Include "TECLADO.bas"
    'Include "PWM.bas"
    'Include "6P20.bas"
    Include "H6P20.bas"
    Include "CENTRAL.bas"
    'Include "PROGRAMACION.bas"
    Include "RUTINAS.bas"
    'Include "DISPAROS.bas"
    'Include "MEMORIA.bas"
    'Include "SALTEC.bas"
    Include "convert.bas"
    
    Dim RSSITest As Byte
    
    Private Sub CorrerTestConGSM()
        Dim
            paso As Byte,
            stamp As Word,
            terminado, micok As Boolean,
            mensaje As TMensajeDeTexto
            
        ApagarDisplay()
        Delay(500)
        MostrarDigito(DisplayT)
        BeepLargo()
        MostrarDigito(2)
        BeepLargo()
        ApagarDisplay()
        BeepLargo()
        
        paso=0 
        terminado=false
        flagGPRSHabilitado=false
        micok=false
        flagCorriendoTest=true
        
        Repeat
            ProcesarGSM()
            If EstadoModulo=stModuloConfigurado Then
                Select paso
                    Case 0
                        TimerMostrarRSSI=10
                        MostrarRSSI(GSM.RSSI)
                        
                        If btnSel=0 And GSM.RSSI>4 And GSM.RSSI<31 Then
                            RSSITest=GSM.RSSI
                            TimerMostrarRSSI=0
                            BeepCorto()
                            Inc(paso)
                            While btnSel=0
                            Wend
                            Delay(10)
                        EndIf
                    Case 1
                        flagParpadearDisplay=false
                        MostrarDigito(DisplayL)
                        //Hago la llamada
                        LlamarATest()
                        //Marco la hora
                        stamp=Tick
                        flagOkRecibido=false
                        flagNOCARRIERRecibido=false
                        Inc(paso)
                    Case 2
                        MostrarDigito(DisplayA)
                        If flagOkRecibido Then
                            //Se efectuo la llamada
                            flagOkRecibido=false
                            Inc(paso)
                        Else
                            If Tick-stamp>30 Then
                                //si en 15 segundos no se curso la llamada vuelvo al anterior
                                Dec(paso)
                            EndIf
                        EndIf
                    Case 3
                        If btnSel=0 Then
                            micok=true
                            flagParpadearDisplay=true
                        EndIf
                        If flagNOCARRIERRecibido Then
                            //Se corto la llamada, voy al siguiente paso
                            Inc(paso)
                        EndIf
                        If Tick-stamp>90 Then
                            //si en 60 segundos no se verifico el mic algo esta mal y vuelvo a intentar llamar
                            paso=0
                        EndIf
                    Case 4
                        If micok Then
                            Inc(paso)
                        Else
                            EstadoModulo=stModuloApagando
                            paso=0
                        EndIf
                    Case 5
                        MostrarDigito(DisplayT)
                        flagParpadearDisplay=false
                        //Cargo los mensajes de texto y los envio
                        
                        //es momento de enviar un nuevo mensaje de control
                        With mensaje
                            EsNuevo=0                       //No lo marco como nuevo, se a que telefono hay que enviarlo y no necesito volver a cargarlo
                                                            //No los cargo en esta instancia porque desde este modulo no tengo acceso a la rom.
                            Tipo=0                          //Denota el tipo de mensaje, en este caso respuesta de config o fuera de los standard
                            InfoAdicional=0                 //Cualquier info adicional que sea necesaria, en este caso nada
                            Telefonos=1                     //denoto que debe mandarse a un telefono (indicado en TelefonoRespuesta)
                            ErroresTelefonos=0              //Campo en blanco para que el modulo GSM marque los numeros de telefono que dieron error y resolver a partir de ahi
                            ErroresRonda=0                  //Campo en blanco para que el modulo GSM cuente los errores de ronda completa
                            TimeStamp=Tick                  //Marca de timpo para saber cuanto tiempo mantenerlo vivo o purgarlo de la cola
                            'TelefonoRespuesta="+542964459261" ' Martin
                            TelefonoRespuesta="+542804307610" ' Luis
                            'TelefonoRespuesta="+542964417255" ' Luis DNZT
                            'TelefonoRespuesta="+543413802285" ' Luis Test Movistar
                            'TelefonoRespuesta="+542964471627" ' Esteban
                            'TelefonoRespuesta="+542964547145" ' Modem DNZT Seguridad
                            'TelefonoRespuesta="+542964545038" ' Modem control DNZT Fueguina
                            TextoRecibido=CargarDesdeRom(dirIMEI) + CharLF + NumeroSerie() + CharLF + Version + CharLF + FechaFabricacion() + CharLF + DecToStr(RSSITest) + CharLF + "Placa Testeada OK"
                        End With
                        //Cargo el mensaje para el envio
                        PonerMensajeEnCola(mensaje)
                        'mensaje.TelefonoRespuesta="+542964459261" ' Martin
                        'mensaje.TelefonoRespuesta="+542804307610" ' Luis
                        'mensaje.TelefonoRespuesta="+542964417255" ' Luis DNZT
                        'mensaje.TelefonoRespuesta="+542964471627" ' Esteban
                        'mensaje.TelefonoRespuesta="+542964547145" ' Modem DNZT Seguridad
                        'mensaje.TelefonoRespuesta="+542964545039" ' Telefono asembli
                        'mensaje.TelefonoRespuesta="+541126086007" ' Telefono asembli 2014
                        mensaje.TelefonoRespuesta="+542964473115" ' Telefono Monitoreo DNZT
                        mensaje.TextoRecibido=CargarDesdeRom(dirIMEI) + CharLF + Version + CharLF + "Placa Testeada OK"
                        //Cargo el mensaje para el envio
                        PonerMensajeEnCola(mensaje)
                        stamp=Tick
                        //Seteo el numero maximo de reintentos de sms a 255
                        MaximoErroresRonda=255
                        Inc(paso)
                    Case 6
                        //Espero el envio ok de los mensajes
                        If PrimerElementoCola=UltimoElementoCola Then
                            //Si dalieron los dos mensajes
                            terminado=true
                            //Me aseguro de que los valores vuelvan a su nominal....
                            LeerSeteos()
                        EndIf
                        If Tick-stamp>60 Then
                            //Si no resolvio los mensajes en 60 segundos vuelvo a intentar
                            EstadoModulo=stModuloApagando
                            BorrarPrimerMensaje()
                            BorrarPrimerMensaje()
                            MostrarDigito(DisplayE)
                            flagParpadearDisplay=true
                            Dec(paso)
                        EndIf
                End Select
            Else
                MostrarDigito(DisplayC)
            EndIf
        Until terminado

        //Pongo el codigo de instalador igual a los ultimos 4 digitos del imei
        ResetearCodigoInstalador()
        flagParpadearDisplay=false
        //Vuelvo a leer el contenido de la eeprom para actualizar la ram
        LeerSeteos()
        flagCorriendoTest=false
        SecuenciaInicio()
        
    End Sub

    Private Sub CorrerTestSinGSM()
        Dim
            contador, contador1 As Byte
            
        ApagarDisplay()
        Delay(500)
        MostrarDigito(DisplayT)
        BeepLargo()
        MostrarDigito(1)
        BeepLargo()
        ApagarDisplay()
        BeepLargo()
        
        //Espera a que este desconectado el trafo
        Repeat
            MostrarDigito(DisplayE)
            flagParpadearDisplay=true
        Until DeteccionAlimentacion = 0
        flagParpadearDisplay=false

        For contador = 0 To 1
            //Muestra una U
            DISPLAY.MostrarDigito(DisplayU)
            Delay(100)
    
            //Espera se conecte el trafo
            Repeat
            Until DeteccionAlimentacion = 1
            Delay(100)
    
            //Limpia el display
            DISPLAY.ApagarDisplay()
            IO.SalidaBuzzer=BuzzerEncendido
            Delay(100)
    
            //Espera se desconecte el trafo
            Repeat
            Until DeteccionAlimentacion = 0
            Delay(100)

            //Apago el buzzer
            IO.SalidaBuzzer=BuzzerApagado
        Next

        //Espero todas las zonas abiertas
        Repeat
            MostrarDigito(DisplayE)
            flagParpadearDisplay=true
        Until ZonasCableadas=$FF
        flagParpadearDisplay=false

        For contador1 = 0 To 7        
            
            //Test de zona
            For contador = 0 To 1
                //Muestra el numero de zona
                DISPLAY.MostrarDigito(contador1+1)
                Delay(100)
        
                //Espera se cierre la zona
                Repeat
                Until ZonasCableadas.bits(contador1) = 0
                Delay(100)
        
                //Limpia el display
                DISPLAY.ApagarDisplay()
                IO.SalidaBuzzer=BuzzerEncendido
                Delay(100)
        
                //Espera se abra la zona
                Repeat
                Until ZonasCableadas.bits(contador1) = 1
                Delay(100)
    
                //Apago el buzzer
                IO.SalidaBuzzer=BuzzerApagado
            Next
        Next
        
        //Espero el antidesarme abierto
        Repeat
            MostrarDigito(DisplayE)
            flagParpadearDisplay=true
        Until ZonaAntidesarme=1
        flagParpadearDisplay=false

        //Test de zona
        For contador = 0 To 1
            //Muestra el numero de zona
            DISPLAY.MostrarDigito(DisplayA)
            Delay(100)
    
            //Espera se cierre la zona
            Repeat
            Until ZonaAntidesarme = 0
            Delay(100)
    
            //Limpia el display
            DISPLAY.ApagarDisplay()
            IO.SalidaBuzzer=BuzzerEncendido
            Delay(100)
    
            //Espera se abra la zona
            Repeat
            Until ZonaAntidesarme = 1
            Delay(100)

            //Apago el buzzer
            IO.SalidaBuzzer=BuzzerApagado
        Next
        
        //si el bot prog esta pulsado me quedo marcando error
        While btnProg=0
            MostrarDigito(DisplayE)
            flagParpadearDisplay=true
        Wend
        flagParpadearDisplay=false
        
        //Espero el boton
        Repeat
            MostrarDigito(DisplayL)
        Until btnProg=0
        
        //Enciendo el led de estdo por 2 segundos
        ApagarDisplay()
        EncenderLedEstado()
        Delay(2000)
        ApagarLedEstado()
        
        //Test de salida
        For contador = 0 To 1
            //Muestra el numero de zona
            DISPLAY.MostrarDigito(DisplayE)
            Delay(100)
    
            //Espera se cierre el bot prog
            Repeat
            Until btnProg = 0
            Delay(100)
    
            //Limpia el display
            DISPLAY.ApagarDisplay()
            SirenaExterior=SirenaEncendida
            Delay(100)
    
            //Espera se abra el bot prog
            Repeat
            Until btnProg = 1
            Delay(100)

            //Apago la salida
            SirenaExterior=SirenaApagada
        Next
        
        //Test de salida
        For contador = 0 To 1
            //Muestra el numero de zona
            DISPLAY.MostrarDigito(DisplayI)
            Delay(100)
    
            //Espera se cierre el bot prog
            Repeat
            Until btnProg = 0
            Delay(100)
    
            //Limpia el display
            DISPLAY.ApagarDisplay()
            SirenaInterior=SirenaEncendida
            Delay(100)
    
            //Espera se abra el bot prog
            Repeat
            Until btnProg = 1
            Delay(100)

            //Apago la salida
            SirenaInterior=SirenaApagada
        Next
        
        //Test de salida
        For contador = 0 To 1
            //Muestra el numero de zona
            DISPLAY.MostrarDigito(1)
            Delay(100)
    
            //Espera se cierre el bot prog
            Repeat
            Until btnProg = 0
            Delay(100)
    
            //Limpia el display
            DISPLAY.ApagarDisplay()
            SalidaTransistor1=SalidaActivada
            Delay(100)
    
            //Espera se abra el bot prog
            Repeat
            Until btnProg = 1
            Delay(100)

            //Apago la salida
            SalidaTransistor1=SalidaDesactivada
        Next
        
        //Test de salida
        For contador = 0 To 1
            //Muestra el numero de zona
            DISPLAY.MostrarDigito(2)
            Delay(100)
    
            //Espera se cierre el bot prog
            Repeat
            Until btnProg = 0
            Delay(100)
    
            //Limpia el display
            DISPLAY.ApagarDisplay()
            SalidaTransistor2=SalidaActivada
            Delay(100)
    
            //Espera se abra el bot prog
            Repeat
            Until btnProg = 1
            Delay(100)

            //Apago la salida
            SalidaTransistor2=SalidaDesactivada

        Next
        
        //Me aseguro que la salida de teclado no joda en la recepcion del remoto
        Input(SalidaTeclado)
        //Controlo la recepcion de un remoto
        For contador=0 To 1
            //Muestra el numero de zona
            DISPLAY.MostrarDigito(DisplayR)
            Delay(100)
            
            //espero un 6p20 valido
            Repeat
                LeerTren()
            Until Recibiendo6P20

            //Limpia el display
            DISPLAY.ApagarDisplay()
            IO.SalidaBuzzer=BuzzerEncendido
            
            //espero que desaparezca 6p20 valido
            Repeat
                LeerTren()
            Until Not Recibiendo6P20

            //Apago la salida
            IO.SalidaBuzzer=BuzzerApagado
        Next
        //Devuelvo la salida de teclado a su posicion normal
        Output(SalidaTeclado)
        Delay(500)
        
        //Controlo la salida y entrada a la pc
        Repeat
            //Muestro la p
            flagParpadearDisplay=false
            MostrarDigito(DisplayP)
            //Espero el boton
            Repeat
            Until btnProg=0
            
            BeepCorto()
            Delay(500)
            contador1=0
            Output(TxPC)
            For contador=0 To 255
                Toggle(TxPC)
                DelayUS(100)
                If RxPC=TxPC Then
                    Inc(contador1)
                EndIf
            Next 
            
            //Si dio error....
            If contador1<>0 Then
                flagParpadearDisplay=true
                MostrarDigito(DisplayE)
                //Espero se pulse el boton para probar otra vez
                Repeat
                Until btnProg=0
            EndIf
        Until contador1=0
        
        //Controlo entrada y salida de teclado
        Repeat
            //Muestro la t
            flagParpadearDisplay=false
            MostrarDigito(DisplayT)
            //Espero el boton
            Repeat
            Until btnProg=0
            
            BeepCorto()
            Delay(500)
            contador1=0
            Output(SalidaTeclado)
            For contador=0 To 255
                Toggle(SalidaTeclado)
                DelayUS(100)
                If PORTC.2=SalidaTeclado Then
                    Inc(contador1)
                EndIf
            Next 
            
            //Si dio error....
            If contador1<>0 Then
                flagParpadearDisplay=true
                MostrarDigito(DisplayE)
                //Espero se pulse el boton para probar otra vez
                Repeat
                Until btnProg=0
            EndIf
        Until contador1=0
    End Sub
    
    Function NumeroSerieFail() As Boolean
        Dim Dato As Byte
        
        //Asumo todo esta bien
        result = false
        
        //Controlo el mes
        Dato=ReadByte($3fe)
        If Dato>12 Then
            result=true
        EndIf
        
        //Controlo el dia
        Dato=ReadByte($3fd)
        If Dato>31 Then
            result=true
        EndIf
        
        //Controlo PID
        Dato=ReadByte($3fc)
        If Dato<>1 Then
            result=true
        EndIf
        
    End Function


    //Rutina de test general
    Public Sub CorrerTests()
        //Me aseguro que todos los valores en ram esten actualizados
        LeerSeteos()
        If NumeroSerieFail() Then
            MostrarDigito(DisplayE)
            SalidaBuzzer=BuzzerEncendido
            While (true)
            Wend
        EndIf
        If TestFabricaConcluido = TestNinguno Then
            //Si todavia no se corrio ningun test
            CorrerTestSinGSM()
            //Si llego aca, esta todo ok en la placa (sin GSM)
            TestFabricaConcluido=TestSinGSM
            ROM.TestOK(TestSinGSM)
        EndIf
        If TestFabricaConcluido = TestSinGSM Then
            //Si el test sin gsm ya se corrio correctamente
            //Se anula test de GSM para segundo embarque en asembli
            CorrerTestConGSM()                  
            ROM.TestOK(TestConGSM)
        EndIf
    End Sub

