// 
//*************************************************************************** 
// Name    : UserID.bas 
// Author  : <PERSON> 
// Notice  : Copyright (c) 2011 
//         : All Rights Reserved 
// Date    : 04/15/2011 
// Version : 1.0 
// Notes   : functions to read, write, erase, and initialize the UserID area 
//         : THESE FUNCTIONS DISABLE INTERRUPTS 
//*************************************************************************** 
// 
module UserID 

//18F UserID area is eight bytes at $200000 - $200007 
public const USER_ID_SIZE = 8 
const USER_ID_ADDRESS = $200000 

// 
// This macro creates an initialization record in the hex file 
// It can be used to set defaults for the eight-byte UserID area 
// The asm 'db' directive accepts data in a variety of formats 
// 
macro InitUserID() 
asm 
    cur_loc set $                           ; record current program address 
    org USER_ID_ADDRESS                     ; point to the UserID area 
    db  0x5a,"1",0x20,0x30,40h,50h,60,"7"   ; eight bytes of user-defined data 
    org cur_loc                             ; set program addr back to original 
end asm 
end macro 


// 
//----------------------------------------------------------------- 
// register bit definitions 
//----------------------------------------------------------------- 
// 
dim 
    INTCON_GIE  as INTCON.7 

// EECON1 bits 
dim 
    EECON1_EEPGD as EECON1.7,      // flash program(=1) or data eeprom(=0) select 
    EECON1_CFGS  as EECON1.6,      // configuration=1 (overrides EEPGD) 
    EECON1_b5    as EECON1.5,      // unused 
    EECON1_FREE  as EECON1.4,      // flash row erase enable=1, write=0 
    EECON1_WRERR as EECON1.3,      // error flag 
    EECON1_WREN  as EECON1.2,      // write enable=1 
    EECON1_WR    as EECON1.1,      // initiate write=1 
    EECON1_RD    as EECON1.0       // initiate eeprom read=1 

// 
//----------------------------------------------------------------- 
// table access and misc instructions 
//----------------------------------------------------------------- 
// 
inline sub TBLWT_(b as TABLAT) 
    asm 
        TBLWT* 
    end asm 
end sub 

inline sub TBLWT_POSTINC(b as TABLAT) 
    asm 
        TBLWT*+ 
    end asm 
end sub 

inline function TBLRD_() as TABLAT 
    asm 
        TBLRD* 
    end asm 
end function 

inline function TBLRD_POSTINC() as TABLAT 
    asm 
        TBLRD*+ 
    end asm 
end function 

// nop instruction 
inline sub nop() 
   asm 
      nop 
   end asm 
end sub 

inline sub disable_interrupts() 
    INTCON_GIE = 0 
end sub    

// 
//----------------------------------------------------------------- 
// memory access functions 
//----------------------------------------------------------------- 
// 
// set EECON1 register to FLASH program access 
inline sub access_flash_memory() 
    EECON1 = $80         // EEPGD=1, CFGS=0 
end sub 

// set EECON1 register to IDLOC memory access 
inline sub access_memory_idloc() 
    EECON1 = $80         // EEPGD=1, CFGS=0 
end sub 

// restore tableptr for SF access 
inline sub set_flash_read() 
    TBLPTRU = $00 
    access_flash_memory() 
end sub 

// set erase enable 
inline sub flash_erase_enable() 
    EECON1_FREE = 1 
end sub 

// set tableptr address 
macro set_flash_addr(addr) 
    TBLPTRU = byte(longword(addr) >> 16) 
    TBLPTRH = byte(word(addr) >> 8) 
    TBLPTRL = byte(addr) 
end macro 

// read a flash byte at the current tableptr location 
inline function read_flash() as TABLAT 
    TBLRD_POSTINC() 
end function 

// initiate a write to memory 
sub write_memory_unlock() 
    // make certain interrupts are disabled for unlock process 
    disable_interrupts() 

    // unlock write 
    EECON1_WREN = 1 
    EECON2 = $55 
    EECON2 = $AA 
    EECON1_WR = 1 

    // wait for write completion 
    repeat 
    until (EECON1_WR = 0) 
    
    // disable writes 
    EECON1_WREN = 0 
end sub 

// 
//----------------------------------------------------------------- 
// UserID functions 
//----------------------------------------------------------------- 
// 

// 
// erase UserID 
// 
public sub Erase() 
    disable_interrupts() 

    // erase current id 
    set_flash_addr(USER_ID_ADDRESS) 
    access_memory_idloc() 
    flash_erase_enable() 
    write_memory_unlock() 

    set_flash_read() 
end sub 

// 
// write UserID 
// 
public sub Write(byref pArray() as byte) 
    dim i as byte 

    // erase current ID 
    Erase() 
    
    // reprogram id 
    access_memory_idloc() 
    set_flash_addr(USER_ID_ADDRESS) 

    FSR0 = addressof(pArray) 
    // write one less...we'll catch the last byte by itself 
    i = USER_ID_SIZE-1 
    repeat 
        TBLWT_POSTINC(POSTINC0) 
        dec(i) 
    until (i = 0) 

    // write last byte 
    TBLWT_(POSTINC0) 

    // program the block... 
    write_memory_unlock() 

    // set back to read mode 
    set_flash_read() 
end sub 

// 
// read UserID 
// 
public sub Read(byref pArray() as byte) 
    dim i as byte 
    
    // make certain interrupts are disabled 
    INTCON_GIE = 0 

    access_memory_idloc() 
    set_flash_addr(USER_ID_ADDRESS) 

    FSR0 = addressof(pArray) 
    i = USER_ID_SIZE 
    repeat 
        POSTINC0 = read_flash() 
        dec(i) 
    until (i = 0) 

    set_flash_read() 
end sub 

// 
// load initialization data for HEX file 
// 
InitUserID() 

end module 