{
*****************************************************************************
*  Name    : BUFFERUSART.BAS                                                *
*  Author  : <PERSON>                                                   *
*  Notice  : Copyright (c) 2010 Scanner Alarmas                             *
*          : All Rights Reserved                                            *
*  Date    : 28/05/2010                                                     *
*  Version : 1.0                                                            *
*  Notes   :                                                                *
*          :                                                                *
*****************************************************************************
}

Module BufferUSART

    //Modulos
    Include "usart.bas"
    
    //Constantes publicas
    Public Const TamanioBuffer = 128
    
    //Declaracion de varibles
    Public Dim
        MiBuffer(TamanioBuffer) As Byte,' Absolute $700,
        ptrInicio As Byte,
        ptrF<PERSON> As Byte,
        flagDesbordamiento <PERSON>,
        TimerProcesador As Word
    
    //Funciones
    
    {
    ****************************************************************************
    * Name    : Initialize                                                     *
    * Purpose : Initialize buffering - with optional OnData event handler      *
    ****************************************************************************
    }
    Public Sub Initialize()
       'ptrInicio = 0
       'ptrFinal = 0
       flagDesbordamiento = false
       USART.ClearOverrun
    End Sub
    {
    ****************************************************************************
    * Name    : DataAvailable                                                  *
    * Purpose : Check to see if there is data in the buffer                    *
    ****************************************************************************
    }
    Public Function DataAvailable() As Boolean
       RCIE=0
       Result = ptrFinal <> ptrInicio
       RCIE=1
    End Function
    {
    ****************************************************************************
    * Name    : Overrun                                                        *
    * Purpose : Returns true if RC register or buffer has overrun, false       *
    *         : otherwise                                                      *
    ****************************************************************************
    }
    Public Function Overrun() As Boolean
       Result = USART.Overrun Or flagDesbordamiento
    End Function
    {
    ****************************************************************************
    * Name    : GetByte (PRIVATE)                                              *
    * Purpose : Get a single byte from the buffer                              *
    ****************************************************************************
    }
    Function GetByte() As Byte
       Result = MiBuffer(ptrInicio)
       Inc(ptrInicio)
       If ptrInicio > Bound(MiBuffer) Then
          ptrInicio = 0
       EndIf   
    End Function
    {
    ****************************************************************************
    * Name    : ReadByte                                                       *
    * Purpose : Read a single byte from the buffer                             *
    ****************************************************************************
    }
    Public Function LeerCaracter() As Char
       RCIE=0
       Result = GetByte
       RCIE=1
    End Function

    //Inicializacion
    Initialize()
    TimerProcesador=0
    MiBuffer(0)=0
    ptrInicio=0
    ptrFinal=0
